import {
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { Args, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UdienzaService } from '../../udienza/udienza.service';
import { PenaleTUdienza } from '../models/penale_t_udienza.model';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { PenaleTRicudien } from '../models/penale_t_ricudien.model';
import { PenaleCollegio } from '../models/penale_t_collegio.model';
import { CollegioService } from '../../collegio/collegio.service';
import * as moment from 'moment';
import { AuthService } from '../../auth/auth.service';
import { PenaleTUdienzaEntityFake } from '../entities/penale_udienza_fake.entity';
import { PenaleTMagis } from '../models/penale_t_magis.model';
import { PenaleAnagmagis } from '../models/penale_anagmagis.model';
import { AnagraficaMagistratiService } from '../../anagrafica-magistrati/anagrafica-magistrati.service';
import { MagistratiService } from '../../magistrati/magistrati.service';
import { PenaleProvvedimenti } from '../models/penale_provvedimenti.model';
import { ProvvedimentoFindService } from '../../provvedimenti/provvedimento-find.service';
import { RicercaSentenzaService } from '../../ricerca-sentenza/ricerca-sentenza.service';
import { InfoProvvedimento } from '../models/dto/info_provvedimento.model';
import { RicorsoUdienzaCommonService } from '../../ricorso-udienza/ricorso-udienza-common.service';
import { GenericErrorException } from '../../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../../exceptions/code-error-enum.exception';

@Resolver(() => PenaleTUdienza)
export class PenaleTUdienzaResolver {
  private logger = new Logger(PenaleTUdienzaResolver.name);
  constructor(
    private readonly tUdienzaService: UdienzaService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly collegioService: CollegioService,
    private readonly authService: AuthService,
    private readonly anagraficaMagistratiService: AnagraficaMagistratiService,
    private readonly magistratiService: MagistratiService,
    private readonly provvedimentoFindService: ProvvedimentoFindService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
    private readonly ricorsoUdienzaCommonService: RicorsoUdienzaCommonService,
  ) {}

  @Query(() => PenaleTUdienza, { name: 'udienza' })
  async udienza(@Args('id') id: number): Promise<PenaleTUdienza> {
    const currentUser = await this.authService.getCurrentUser();
    const udienza = await this.tUdienzaService.udienzaPerRelatoreByIdUdienza(
      currentUser,
      id,
    );
    if (!udienza) {
      throw new NotFoundException(id);
    }
    return udienza;
  }
  @Query(() => PenaleTUdienza, { name: 'udienzaPerPresidente' })
  async udienzaPerPresidente(@Args('id') id: number): Promise<PenaleTUdienza> {
    this.logger.log(`Inizio udienzaPerPresidente con id:${id}`);
    const currentUser = await this.authService.getCurrentUser();
    const udienza = await this.tUdienzaService.udienzaPerPresidenteByIdUdienza(
      currentUser,
      id,
    );
    if (!udienza) {
      this.logger.error(`Udienza con id:${id} non trovata`);
      throw new GenericErrorException(
        'Udienza non trovata',
        CodeErrorEnumException.UDIENZA_NOT_FOUND,
      );
    }

    this.logger.log(`Fine udienzaPerPresidente con id:${id}`);
    return udienza;
  }

  @ResolveField('collegio', () => [PenaleCollegio])
  async getCollegi(
    @Parent() penaleTUdienza: PenaleTUdienza,
  ): Promise<PenaleCollegio[] | null> {
    this.logger.log(
      `Inizio resolver collegio con idUdienza:${penaleTUdienza.idUdien}`,
    );
    const colleggios: PenaleCollegio[] = [];
    if (penaleTUdienza.idUdien > 0) {
      const colleggiosEntity = await this.collegioService.collegioByIdUdienza(
        penaleTUdienza.idUdien,
      );
      if (colleggiosEntity === null || colleggiosEntity.length == 0) {
        this.logger.log(
          `Fine resolver collegio con idUdienza:${penaleTUdienza.idUdien}, con risultato lista vuota`,
        );
        return [];
      }
      for (const u of colleggiosEntity) {
        const penaleCollegio = new PenaleCollegio({ ...u });
        const magistrato = new PenaleTMagis({
          ...(await this.magistratiService.magistratiServiceByIdMagis(
            penaleCollegio.idMagis,
          )),
        });
        penaleCollegio.magistrato = magistrato;
        if (magistrato.idAnagmagis) {
          if (magistrato?.idAnagmagis) {
            const anagrafica = new PenaleAnagmagis({
              ...(await this.anagraficaMagistratiService.anagraficaMagistrato(
                magistrato?.idAnagmagis,
              )),
            });
            magistrato.anagraficaMagistrato = anagrafica;
            penaleCollegio.magistrato = magistrato;
          }
          if (
            penaleCollegio.tipoMag !== 'RI' &&
            penaleCollegio.tipoMag !== 'PM'
          ) {
            colleggios.push(penaleCollegio);
          }
        }
      }
      const presidente = colleggios.find(pre => pre.tipoMag === 'PRE');
      if (presidente) {
        const idDuplicato = colleggios
          .map(coll => {
            if (coll.tipoMag !== 'PRE') {
              return coll.idMagis;
            }
          })
          .indexOf(presidente?.idMagis);
        if (idDuplicato >= 0) {
          colleggios.splice(idDuplicato, 1);
        }
      }
      colleggios.sort((a, b) => {
        if (a.tipoMag === 'PRE' || b.tipoMag === 'PRE') {
          if (b.tipoMag === 'PRE') {
            return b.tipoMag === 'PRE' && a.tipoMag !== 'PRE'
              ? 1
              : a.gradoMag > b.gradoMag
              ? 1
              : -1;
          } else {
            return -1;
          }
        }
        if (a.gradoMag !== b.gradoMag) {
          return a.gradoMag < b.gradoMag ? -1 : 1;
        }
        if (
          a.magistrato?.anagraficaMagistrato?.cognome &&
          b.magistrato?.anagraficaMagistrato?.cognome
        ) {
          return a.magistrato?.anagraficaMagistrato?.cognome >
            b.magistrato?.anagraficaMagistrato?.cognome
            ? 1
            : -1;
        }
        return 1;
        // a parita di grado va preso il cognome
      });
    }
    this.logger.log(
      `Fine resolver collegio con idUdienza:${penaleTUdienza.idUdien}`,
    );
    return colleggios;
  }
  @ResolveField('ricorsiUdienza', () => [PenaleTRicudien], { nullable: true })
  async getAnagraficaDifensore(
    @Parent() penaleTUdienza: PenaleTUdienza,
    @Args('nrg') nrg: number,
  ): Promise<PenaleTRicudien[] | null> {
    this.logger.log(
      `Inizio resolver getAnagraficaDifensore con idUdienza:${penaleTUdienza.idUdien}`,
    );
    if (penaleTUdienza.idUdien > 0) {
      const ricorsoUdienza =
        await this.ricorsoUdienzaService.ricorsoUdienzaByUdienAndNrg(
          penaleTUdienza.idUdien,
          nrg,
        );
      if (ricorsoUdienza && ricorsoUdienza?.length > 0) {
        this.logger.log(
          `Fine resolver getAnagraficaDifensore con idUdienza:${penaleTUdienza.idUdien}`,
        );
        return ricorsoUdienza;
      }
    }
    this.logger.log(
      `Fine resolver getAnagraficaDifensore con idUdienza:${penaleTUdienza.idUdien}, risultato: null`,
    );
    return null;
  }

  @ResolveField('termineDeposito', () => Date)
  async getTerminaDeposito(
    @Parent() penaleTUdienza: PenaleTUdienza,
  ): Promise<Date | null> {
    this.logger.log(
      `Inizio resolver termineDeposito con idUdienza:${penaleTUdienza.idUdien}`,
    );
    const fineTermineDepositoMoment = moment(penaleTUdienza.dataUdienza);
    return fineTermineDepositoMoment.add(30, 'd').toDate();
  }

  @Query(() => [PenaleTUdienzaEntityFake], {
    name: 'termineDepositoCalendar',
  })
  async getUdienzeInScadenza(
    @Args('startDate') startDate: Date,
    @Args('endDate') endDate: Date,
  ): Promise<Array<PenaleTUdienzaEntityFake> | null> {
    this.logger.log(
      `Inizio query graphql getUdienzeInScadenza con startDate:${startDate}; endDate:${endDate}`,
    );
    const currentCf = await this.authService.getCurrentUser();
    const termineUdienzaWithPeriod =
      await this.tUdienzaService.getTermineUdienzaWithPeriod(
        currentCf,
        startDate,
        endDate,
      );
    this.logger.log(
      `Fine query graphql getUdienzeInScadenza con startDate:${startDate}; endDate:${endDate}; termineUdienzaWithPeriod:${JSON.stringify(
        termineUdienzaWithPeriod,
      )}`,
    );
    return termineUdienzaWithPeriod;
  }

  @Query(() => [PenaleTUdienza], { name: 'udienze' })
  async udienze(): Promise<PenaleTUdienza[]> {
    this.logger.log(
      `Inizio query udienze per utente estensore/relatore corrente`,
    );
    try {
      const currentUser = await this.authService.getCurrentUser();
      const udienzePerRelatoreEstensore =
        this.tUdienzaService.udienzePerRelatoreEstensore(currentUser);
      this.logger.log(
        `Fine query udienze per utente estensore/relatore corrente`,
      );
      return udienzePerRelatoreEstensore;
    } catch (e) {
      this.logger.error(
        `Errore query udienze per utente estensore/relatore corrente`,
        e,
      );
      throw new GenericErrorException(
        'Udienza non trovata',
        CodeErrorEnumException.UDIENZA_ERROR,
      );
    }
  }

  @Query(() => [PenaleTUdienza], { name: 'udienzePerEstensore' })
  async udienzePerEstensore(): Promise<PenaleTUdienza[]> {
    this.logger.log(`Inizio query udienze per utente estensore corrente`);
    try {
      const currentUser = await this.authService.getCurrentUser();
      const udienzePerEstensore =
        this.tUdienzaService.udienzePerEstensore(currentUser);
      this.logger.log(`Fine query udienze per utente estensore corrente`);
      return udienzePerEstensore;
    } catch (e) {
      this.logger.error(
        `Errore query udienze per utente estensore corrente`,
        e,
      );
      throw new GenericErrorException(
        'Udienza non trovata',
        CodeErrorEnumException.UDIENZA_ERROR,
      );
    }
  }
  @ResolveField('provvedimentiByNrg', () => [PenaleProvvedimenti], {
    nullable: true,
  })
  async provvedimentiByNrg(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
    @Parent() penaleTUdienza: PenaleTUdienza,
  ): Promise<Array<PenaleProvvedimenti> | null> {
    this.logger.log(
      `Inizio resolver provvedimentiByNrg idUdien:${idUdien}; nrg:${nrg}`,
    );
    if (idUdien && nrg) {
      const getAllProvvedimentiCollegati =
        await this.provvedimentoFindService.provvedimentoFindByIdUdinAndNrg(
          idUdien,
          nrg,
        );
      this.logger.log(
        `La lista dei provvedimenti è ${
          getAllProvvedimentiCollegati?.length > 0 ? 'piena' : 'vuota'
        }.`,
      );
      return getAllProvvedimentiCollegati;
    }
    this.logger.log(
      `provvedimenti non trovati perche idUdien:${idUdien} o nrg:${nrg} sono null`,
    );
    return null;
  }
  @ResolveField(
    'provvedimentoByNrgPerPresidente',
    () => [PenaleProvvedimenti],
    {
      nullable: true,
    },
  )
  async provvedimentoByNrgPerPresidente(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
    @Parent() penaleTUdienza: PenaleTUdienza,
  ): Promise<Array<PenaleProvvedimenti> | null> {
    this.logger.log(
      `Inizio resolver provvedimentoByNrgPerPresidente idUdien:${idUdien}; nrg:${nrg}`,
    );
    if (idUdien && nrg) {
      const getAllProvvedimentiCollegati =
        await this.provvedimentoFindService.provvedimentoPresidenteFindByIdUdinAndNrg(
          idUdien,
          nrg,
        );
      this.logger.log(
        `fine provvedimentoByNrgPerPresidente, la lista dei provvedimenti è ${
          getAllProvvedimentiCollegati?.length > 0 ? 'piena' : 'vuota'
        }.`,
      );
      return getAllProvvedimentiCollegati;
    }
    this.logger.log(
      `Fine provvedimentoByNrgPerPresidente, provvedimenti non trovati perche idUdien:${idUdien} o nrg:${nrg} sono null`,
    );
    return null;
  }
  /*Il metodo getPubblicatoSIC prende come argomento un'istanza di PenaleTRicudien e
   * controlla la proprietà dello stato dell'oggetto provvedimento, all'interno dell'istanza di PenaleTRicudien.
   * A seconda del valore dello stato, esegue diverse azioni*/
  @ResolveField('checkStatoOnSIC', () => InfoProvvedimento, {
    nullable: true,
  })
  async checkStatoOnSIC(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
  ): Promise<InfoProvvedimento | null> {
    this.logger.log(
      `Inizio resolver checkStatoOnSIC idUdien:${idUdien}; nrg:${nrg}`,
    );
    const ricorsoUdienza =
      await this.ricorsoUdienzaService.ricorsoUdienzaByNrgAndIdUdienza(
        idUdien,
        nrg,
      );
    if (ricorsoUdienza) {
      const chekStatoOnSicCommonByIdRicorsoUdienza =
        await this.ricorsoUdienzaCommonService.checkStatoOnSicCommonByIdRicUdien(
          ricorsoUdienza,
        );
      this.logger.log(
        `Fine resolver checkStatoOnSIC idUdien:${idUdien}; nrg:${nrg}; con risultato.`,
      );
      return chekStatoOnSicCommonByIdRicorsoUdienza;
    }
    this.logger.log(
      `Fine resolver checkStatoOnSIC idUdien:${idUdien}; nrg:${nrg}; senza risultato`,
    );
    return null;
  }

  // checkStatoOnSIC end

  @Query(() => PenaleTUdienza, { name: 'udienzeWithProvvedimentoDet' })
  async udienzeWithProvvedimentoDet(
    @Args('idUdien') id: number,
    @Args('nrg') nrg: number,
  ): Promise<PenaleTUdienza | null> {
    this.logger.log(
      `Inizio query udienzeWithProvvedimentoDet idUdien:${id}; nrg:${nrg}`,
    );
    try {
      const currentUser = await this.authService.getCurrentUser();
      const udienza = await this.tUdienzaService.udienzaPerRelatoreByIdUdienza(
        currentUser,
        id,
      );
      this.logger.log(
        `Fine query udienzeWithProvvedimentoDet idUdien:${id}; nrg:${nrg}`,
      );
      return udienza;
    } catch (e) {
      this.logger.error(
        `Fine query udienzeWithProvvedimentoDet idUdien:${id}; nrg:${nrg}`,
        e,
      );
      this.logger.error(
        `Errore query udienzeWithProvvedimentoDet idUdien:${id}; nrg:${nrg}`,
        e,
      );
      throw new GenericErrorException(
        'Udienza non trovata',
        CodeErrorEnumException.UDIENZA_ERROR,
      );
    }
  }
}
