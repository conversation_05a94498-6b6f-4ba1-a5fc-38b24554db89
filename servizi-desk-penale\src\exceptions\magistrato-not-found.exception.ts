import { InternalServerErrorException } from '@nestjs/common';
import {
  HttpException,
  HttpExceptionOptions,
} from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class MagistratoNotFoundException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
  ) {
    super(
      response,
      CodeErrorEnumException.MAGI_SIC_NOT_FOUND,
      NSTypeErrorEnum.WARN,
      options,
    );
  }
}
