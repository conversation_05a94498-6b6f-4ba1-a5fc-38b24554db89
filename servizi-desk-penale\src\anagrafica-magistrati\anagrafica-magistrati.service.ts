import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleAnagmagisEntity } from '../consultazioni-graphql/entities/penale_anagmagis.entity';

@UfficioService()
export class AnagraficaMagistratiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  anagraficaMagistrati(): Promise<PenaleAnagmagisEntity[]> {
    return this.connection.getRepository(PenaleAnagmagisEntity).find();
  }

  async anagraficaMagistrato(
    idMagistrato: number,
  ): Promise<PenaleAnagmagisEntity | null> {
    return this.connection
      .getRepository(PenaleAnagmagisEntity)
      .findOneBy({ idAnagmagis: idMagistrato });
  }
  async anagraficaMagistratoByCf(cf: string): Promise<PenaleAnagmagisEntity[]> {
    return this.connection
      .getRepository(PenaleAnagmagisEntity)
      .findBy({ codiceFiscale: cf });
  }
}
