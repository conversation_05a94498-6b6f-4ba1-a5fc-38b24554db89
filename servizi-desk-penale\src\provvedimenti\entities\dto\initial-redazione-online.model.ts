import { ProvvedimentiTipoEnum } from '../../../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import {ApiProperty} from "@nestjs/swagger";

export class InitialRedazioneOnlineModel {
  @ApiProperty()
  introduzione: string;
  @ApiProperty()
  idSezionale: string;
  @ApiProperty()
  tipoProvvedimento: ProvvedimentiTipoEnum;
  @ApiProperty()
  pqm: string;
  @ApiProperty()
  oscurato: boolean;
  @ApiProperty()
  motivoRicorso: string;
  @ApiProperty()
  finaleDispositivo: string;
  @ApiProperty()
  semplificata: boolean;
  public toString = (): string => {
    return `${JSON.stringify(this)}`;
  };
}
