import { ValueTransformer } from 'typeorm';

export default class ColumnDateIsoTransformer implements ValueTransformer {
  to(date: Date) {
    return date;
  }

  from(date: any) {
    if (!date) return null;
    if (date instanceof Date) {
      const utcDate = Date.UTC(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        date.getHours(),
        date.getMinutes(),
        date.getSeconds(),
      );
      return new Date(utcDate);
    }
    return new Date(date);
  }
}
