import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class UnauthorizedCustomException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    code: CodeErrorEnumException,
    options?: HttpExceptionOptions,
  ) {
    super(response, code, NSTypeErrorEnum.WARN, options, 401);
  }
}
