import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_T_UTENTI') //nome tabella su schema oracle
export class PenaleTUtenteEntity {
  @PrimaryColumn({ name: 'ID_UTENTE' })
  idUtente: number;

  @Column({ name: 'IDENTIFICATIVO' })
  identificativo: string;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_UFFICIO' })
  ufficio: PenaleParamEntity;
  @Column({ name: 'NOME' })
  nome: string;
  @Column({ name: 'COGNOME' })
  cognome: string;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'TIPO_UTENTE' })
  tipoUtente: string;
  @Column({ name: 'ID_PROFILO' })
  idProfilo: number;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPOLOGIA' })
  tipologia: PenaleParamEntity;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'UFF_APPARTENENZA' })
  uffAppartenenza: PenaleParamEntity;
  @Column({ name: 'CODICE_FISCALE' })
  codiceFiscale: string;
}
