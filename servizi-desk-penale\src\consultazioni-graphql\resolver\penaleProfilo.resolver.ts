import { NotFoundException } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import { PenaleProfilo } from '../models/penale_profilo.model';
import { ProfiloService } from '../../profilo/profilo.service';

@Resolver(() => PenaleProfilo)
export class PenaleProfiloResolver {
  constructor(private readonly profiloService: ProfiloService) {}

  @Query(() => PenaleProfilo, { name: 'profiloById' })
  async profiloById(@Args('id') id: number): Promise<PenaleProfilo> {
    const utente = await this.profiloService.profilo(id);
    if (!utente) {
      throw new NotFoundException(id);
    }
    return utente;
  }

  @Query(returns => [PenaleProfilo], { name: 'profilo' })
  profilo(): Promise<PenaleProfilo[]> {
    return this.profiloService.profili();
  }
}
