import { Logger, NotFoundException } from '@nestjs/common';
import {
  Args,
  Int,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';

import { TRicorsoService } from 'src/fascicolo/t-ricorso.service';
import { PenaleTRicorso } from '../models/penale_t_ricorso.model';
import { PenaleTRicudien } from '../models/penale_t_ricudien.model';
import { PenaleProvvedimenti } from '../models/penale_provvedimenti.model';
import { ProvvedimentiService } from '../../provvedimenti/provvedimenti.service';
import { PenaleTMagis } from '../models/penale_t_magis.model';
import { MagistratiService } from '../../magistrati/magistrati.service';
import { EsitiService } from 'src/esiti/esiti.service';
import { ProvvedimentoRelazioneService } from '../../provvedimento-change-status/provvedimento-relazione.service';
import { AuthService } from '../../auth/auth.service';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { ProvvedimentoChangeStatusService } from '../../provvedimento-change-status/provvedimento-change-status.service';
import { EsitiSentService } from '../../esiti/esiti-sent.service';
import { PenaleParamService } from '../../penale-param/penale-param.service';
import { EsitoParzialeModel } from '../models/esito-parziale.model';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { RicercaSentenzaService } from '../../ricerca-sentenza/ricerca-sentenza.service';
import { Utils } from '../../utils/utils';
import { InfoProvvedimento } from '../models/dto/info_provvedimento.model';
import { RicorsoUdienzaCommonService } from '../../ricorso-udienza/ricorso-udienza-common.service';
import { ProvvedimentiOrigineEnum } from '../entities/enumaration/provvedimenti-origine.enum';
import { FascicoloNotFoundException } from '../../exceptions/fascicolo-not-found.exception';
import { DecimalScalar } from '../decimalScalar';
import { SpoglioService } from '../../spoglio/spoglio.service';
import { DatiRicorsoPrincipaliService } from '../../fascicolo/dati-ricorso-principali.service';
import { PenaleTRicorsoUdienzaEntity } from '../entities/penale_t_ricudien.entity';
import { InfoDettaglioFascicoloModel } from '../models/dto/info-dettaglio-fascicolo.model';
import {
  PaginationCustomQueryFilter,
  PenaleRicorsoUdienzaConnection,
} from '../models/dto/generic_paginator';
import {
  computePageInfo,
  endCursor,
  startCursor,
} from '../../relay-pagination/pagination-utils';

@Resolver(() => PenaleTRicudien)
export class PenaleTRicorsoUdienzaResolver {
  private logger = new Logger(PenaleTRicorsoUdienzaResolver.name);

  constructor(
    private readonly spoglioService: SpoglioService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly tRicorsoService: TRicorsoService,
    private readonly magistratiService: MagistratiService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly esitiSentService: EsitiSentService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
    private readonly provvedimentoChangeStatus: ProvvedimentoChangeStatusService,
    private readonly PenaleParamService: PenaleParamService,
    private readonly esitiService: EsitiService,
    private readonly authService: AuthService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
    private readonly ricorsoUdienzaCommonService: RicorsoUdienzaCommonService,
    protected readonly datiRicorsoPrincipaliService: DatiRicorsoPrincipaliService,
  ) {}

  @ResolveField('ricorso', () => PenaleTRicorso, { nullable: true })
  async getRicorso(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<PenaleTRicorso | null> {
    if (penaleTRicudien.nrg > 0) {
      const ricorsoUsienzaList = await this.tRicorsoService.ricorsoFindByNrg(
        penaleTRicudien.nrg,
      );

      return ricorsoUsienzaList;
    }
    return null;
  }

  @ResolveField('checkStatoOnSIC', () => InfoProvvedimento, { nullable: true })
  async checkStatoOnSIC(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<InfoProvvedimento | null> {
    try {
      this.logger.log(
        `Richiesta di risolvere lo stato sul sic. idRicUdien:${penaleTRicudien.idRicudien}`,
      );
      const statoSic =
        await this.ricorsoUdienzaCommonService.checkStatoOnSicCommonByIdRicUdien(
          penaleTRicudien,
        );
      return statoSic;
    } catch (e) {
      this.logger.error(
        `Errore nella richiesta di risolvere lo stato del sic.`,
        e,
      );
    }
    return null;
  }

  @ResolveField('esitoParziale', () => EsitoParzialeModel, { nullable: true })
  async isEsitoParziale(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<EsitoParzialeModel | null> {
    const esitoParziale = new EsitoParzialeModel();
    if (penaleTRicudien.idEsito) {
      const param = await this.PenaleParamService.getParam(
        penaleTRicudien.idEsito,
      );

      esitoParziale.esitoParziale = !(
        param?.sigla == 'D' || param?.sigla == 'P'
      );
      esitoParziale.motivo = param?.descrizione ? param.descrizione : '';
      return esitoParziale;
    }
    return null;
  }

  @ResolveField('relatore', () => PenaleTMagis, { nullable: true })
  async getRelatore(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<PenaleTMagis | null> {
    if (penaleTRicudien.idRelatore > 0) {
      const ricorsoUsienzaList =
        await this.magistratiService.magistratiServiceByIdMagis(
          penaleTRicudien.idRelatore,
        );

      return ricorsoUsienzaList;
    }
    return null;
  }

  @ResolveField('estensore', () => PenaleTMagis, { nullable: true })
  async getEstensore(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<PenaleTMagis | null> {
    if (penaleTRicudien.nrg > 0) {
      const idMagisEstensoreForNrg =
        await this.authService.getIdMagisEstensoreForNrg(
          penaleTRicudien.nrg,
          penaleTRicudien.idUdienza,
        );
      if (idMagisEstensoreForNrg) {
        const ricorsoUsienzaList =
          await this.magistratiService.magistratiServiceByIdMagis(
            idMagisEstensoreForNrg,
          );
        return ricorsoUsienzaList;
      }
    }
    return null;
  }

  @ResolveField('isEstensore', () => Boolean, { nullable: true })
  async isEstensore(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<boolean> {
    const currentUserCf = await this.authService.getCurrentUser();
    return this.ricorsoUdienzaService.isEstensore(
      currentUserCf,
      penaleTRicudien,
    );
  }

  @ResolveField('isRelatore', () => Boolean, { nullable: true })
  async isRelatore(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<boolean> {
    if (penaleTRicudien.nrg > 0) {
      const currentUserCf = await this.authService.getCurrentUser();
      return await this.ricorsoUdienzaService.isRelatoreforNrg(
        currentUserCf,
        penaleTRicudien,
      );
    }
    return false;
  }

  @ResolveField('isPresidente', () => Boolean, { nullable: true })
  async isPresidente(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<boolean> {
    if (penaleTRicudien.nrg > 0) {
      return await this.authService.isPresidenteByNrgAndIdUdienza(
        penaleTRicudien.nrg,
        penaleTRicudien.idUdienza,
      );
    }
    return false;
  }

  @ResolveField('provvedimento', () => PenaleProvvedimenti, { nullable: true })
  async getProvvedimento(
    @Parent() penaleTRicudien: PenaleTRicudien,
  ): Promise<PenaleProvvedimenti | null> {
    if (penaleTRicudien.nrg > 0) {
      const provvedimento =
        await this.provvedimentiService.provvedimentoByIdUdienAndNrgModify(
          penaleTRicudien.idUdienza,
          penaleTRicudien.nrg,
        );
      if (provvedimento) {
        const provvedimentoPresidente =
          await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
            provvedimento.idProvvedimento,
          );
        // controllo il provvedimento originale
        if (provvedimentoPresidente?.idProvvedimentoOrigine) {
          // vado a prendermi l'ultima relazione con questa origine
          const provvedimentoRelatore =
            await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
              provvedimento.idProvvedimento,
            );
          // controllo se la relazione esiste e vedo che non sia la stessa
          if (
            provvedimentoRelatore?.idProvvedimentoDestinazione &&
            provvedimentoRelatore.idProvvedimentoDestinazione ===
              provvedimentoPresidente.idProvvedimentoDestinazione
          ) {
            // significa che non è mai stato duplicato
            provvedimento.isDuplicato = false;
          } else {
            // significa che è stato duplicato
            provvedimento.isDuplicato = true;
          }

          if (provvedimentoRelatore?.idProvvedimentoOrigine) {
            // significa che ha dei duplicati
            provvedimento.hasDuplicato = true;
          } else {
            // ssignifica che non ha dei duplicati
            provvedimento.hasDuplicato = false;
          }
        } else {
          provvedimento.isDuplicato = false;
          const provvedimentoRelatore =
            await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
              provvedimento.idProvvedimento,
            );
          if (provvedimentoRelatore?.idProvvedimentoOrigine) {
            // significa che ha dei duplicati
            provvedimento.hasDuplicato = true;
          } else {
            // ssignifica che non ha dei duplicati
            provvedimento.hasDuplicato = false;
          }
        }
        if (provvedimento?.stato === ProvvedimentiStatoEnum.IN_BOZZA) {
          provvedimento.isRevisione =
            await this.provvedimentoChangeStatus.checkIsRevisione(
              provvedimento.idProvvedimento,
            );
        }

        const statoRicorsoUdienza =
          await this.ricercaSentenzaService.sentenzaByIdRicUdien(
            penaleTRicudien.idRicudien,
          );
        const statoCorretto = Utils.checkStatoCorretto(
          statoRicorsoUdienza,
          provvedimento?.stato || null,
        );
        const penaleProvvModel = new PenaleProvvedimenti({ ...provvedimento });
        if (statoCorretto) {
          penaleProvvModel.stato = statoCorretto;
        }
        if (
          penaleProvvModel.stato === ProvvedimentiStatoEnum.BOZZA_PRESIDENTE
        ) {
          const changeStatus =
            await this.provvedimentoChangeStatus.changeStatusByIdProvvAndLast(
              penaleProvvModel.idProvvedimento,
            );
          penaleProvvModel.stato =
            changeStatus?.prevStato ?? penaleProvvModel.stato;
        }
        return penaleProvvModel;
      }
    }
    return null;
  }

  /**
   * restituisce il valore da oscurare del sic complessivo calcolando anche
   * i fasicoli riuniti se è presente almeno un fascicolo da oscurare
   * @param penaleProvvedimenti
   */
  @ResolveField('oscuratoSicComplessivo', () => Boolean, { nullable: true })
  async oscuratoSicComplessivo(
    @Parent() penaleProvvedimenti: PenaleTRicudien,
  ): Promise<boolean | null> {
    const daOscuratare =
      await this.provvedimentiService.getProvvedimentoOscuratoSicComplessivo(
        penaleProvvedimenti.idUdienza,
        penaleProvvedimenti.nrg,
      );
    return daOscuratare;
  }

  /**
   * restituisce il valore da oscurare del sic del singolo fascicolo
   * @param penaleProvvedimenti
   */
  @ResolveField('oscuratoSicSingle', () => Boolean, { nullable: true })
  async oscuratoSicSingle(
    @Parent() penaleProvvedimenti: PenaleTRicudien,
  ): Promise<boolean | null> {
    const daOscuratare =
      await this.provvedimentiService.getProvvedimentoOscuratoSicSingle(
        penaleProvvedimenti.idUdienza,
        penaleProvvedimenti.nrg,
      );
    return daOscuratare;
  }

  /**
   * restituisce il valore del desk/csp
   * @param penaleRicUdien
   */
  @ResolveField('oscuramentoDeskCsp', () => Boolean, { nullable: true })
  async oscuramentoDeskCsp(
    @Parent() penaleRicUdien: PenaleTRicudien,
  ): Promise<boolean | null> {
    const provvedimentoByIdUdienAndNrg =
      await this.provvedimentiService.provvedimentoByIdUdienAndNrg(
        penaleRicUdien.idUdienza,
        penaleRicUdien.nrg,
      );
    if (
      provvedimentoByIdUdienAndNrg &&
      provvedimentoByIdUdienAndNrg?.length > 0
    ) {
      if (
        provvedimentoByIdUdienAndNrg[0].origine ==
        ProvvedimentiOrigineEnum.SYSTEM
      ) {
        const idsProvv = provvedimentoByIdUdienAndNrg.map(
          prov => prov.idProvvedimento,
        );
        return await this.provvedimentiService.isOscuratoEditor(idsProvv);
      }
      // sono nel caso dei file. Devo prendere il valore dal timbripub
      return await this.provvedimentiService.checkOscuramentoType(
        penaleRicUdien.idUdienza,
        penaleRicUdien.nrg,
      );
    }
    return false;
  }

  @ResolveField('tipologia', () => String, { nullable: true })
  async getTipologia(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<string | null> {
    const daOscuratare = await this.provvedimentiService.getTipoProvvedimento(
      penaleProvvedimenti.idUdienza,
      penaleProvvedimenti.nrg,
    );
    return daOscuratare;
  }

  @Query(() => PenaleTRicudien, { name: 'ricorsoUdienza' })
  async ricorsoUdienza(
    @Args('id') idRicUdien: number,
  ): Promise<PenaleTRicudien> {
    const ricorso = await this.ricorsoUdienzaService.ricorsoUdienzaByid(
      idRicUdien,
    );
    if (!ricorso) {
      throw new NotFoundException(idRicUdien);
    }
    return ricorso;
  }

  @Query(() => [PenaleTRicudien], { name: 'ricorsoUdienzaInRicUdien' })
  async ricorsoUdienzaInIdRicUdien(
    @Args({ name: 'idRicUdienList', type: () => [Int] })
    idRicUdienList: number[],
  ): Promise<Array<PenaleTRicudien>> {
    this.logger.log(
      `Inizio ricerca ricUdien per idsRicorsoUdienza:${idRicUdienList}`,
    );
    const ricorsoUdienza =
      await this.ricorsoUdienzaService.ricorsoUdienzaByIdRicUdienList(
        idRicUdienList,
      );
    if (!ricorsoUdienza || ricorsoUdienza.length === 0) {
      this.logger.error(
        `Ricorso udienza non trovato per idRicUdienzaList:${idRicUdienList}`,
      );
      throw new FascicoloNotFoundException(
        `Ricorso udienza non trovato per idRicUdienzaList:${idRicUdienList}`,
      );
    }
    return ricorsoUdienza;
  }

  @ResolveField('valPondComplessivo', () => DecimalScalar, { nullable: true })
  async getValPondComplessivo(
    @Parent() ricUdin: PenaleTRicudien,
  ): Promise<number | null> {
    const ricorsoRiunitoViewByIdRicUdienPadre =
      await this.ricercaSentenzaService.getRicorsoRiunitoViewByIdRicUdienPadre(
        ricUdin?.idRicudien,
      );
    if (
      ricorsoRiunitoViewByIdRicUdienPadre &&
      ricorsoRiunitoViewByIdRicUdienPadre?.length > 0
    ) {
      const valPondSingle = await this.spoglioService.spoglioFindByNrg(
        ricUdin.nrg,
      );
      const nrgs = ricorsoRiunitoViewByIdRicUdienPadre
        .map(ric => ric.nrg ?? 0)
        .filter(ric => ric > 0);
      if (nrgs?.length > 0) {
        const spoglioComplessivo = await this.spoglioService.spoglioFindByNrgs(
          nrgs,
        );
        const valPodComplessivo = await spoglioComplessivo.reduce(
          async (somma, spoglio) => {
            if (spoglio && (!spoglio?.valPond || spoglio?.valPond === 0)) {
              if (spoglio.nrg) {
                const datiRicorsoByNrg =
                  await this.datiRicorsoPrincipaliService.datiRicorsoByNrg(
                    spoglio.nrg,
                  );
                if (
                  datiRicorsoByNrg?.tipoRiunione?.sigla.toUpperCase() === '02'
                ) {
                  spoglio.valPond = 0.0;
                } else {
                  spoglio.valPond = 1.0;
                }
              }
              spoglio.valPond = 1.0;
            }
            return (await somma) + (spoglio.valPond ?? 0);
          },
          Promise.resolve(0),
        );
        return valPodComplessivo + (valPondSingle?.valPond ?? 1.0);
      }
    }
    return null;
  }

  @Query(returns => InfoDettaglioFascicoloModel, {
    name: 'getFascitoloDetExtraInfo',
  })
  async getFascitoloDetExtraInfo(
    @Args('idUdien') idUdienza: number,
  ): Promise<InfoDettaglioFascicoloModel | null> {
    this.logger.log(
      `Inizio query getFascitoloDetExtraInfo con idUdienza:${idUdienza}`,
    );
    const currentUser = await this.authService.getCurrentUser();
    const valPodECount =
      await this.ricorsoUdienzaService.getValorePonderaleComplessivoAndTotaleElementi(
        currentUser,
        idUdienza,
      );
    this.logger.log(
      `Fine resolver ricorsoUdienzaValorePonderaleTotale con idUdienza:${idUdienza}; valPodECount:${valPodECount}`,
    );
    return valPodECount;
  }

  @Query(returns => PenaleRicorsoUdienzaConnection, { name: 'ricorsiUdienza' })
  async ricorsiUdiezaByIdUdien(
    @Args('idUdien') idUdienza: number,
    @Args() paginationArgs: PaginationCustomQueryFilter,
  ): Promise<PenaleRicorsoUdienzaConnection | null> {
    this.logger.log(
      `Inizio query ricorsiUdiezaByIdUdien con idUdienza:${idUdienza}`,
    );
    const from = startCursor(paginationArgs);
    const to = endCursor(paginationArgs);
    const offset = Number.parseInt(from) || 0;
    const records = Number.parseInt(to) || 5;
    const currentUser = await this.authService.getCurrentUser();
    if (idUdienza > 0) {
      const [ricorsoUdienza, total] =
        await this.ricorsoUdienzaService.ricorsoUdienzaByUdien(
          currentUser,
          idUdienza,
          paginationArgs,
        );
      this.logger.log(
        `Fine resolver ricorsiUdiezaByIdUdien con idUdienza:${idUdienza}; trovati:${ricorsoUdienza?.length}`,
      );
      const connection = new PenaleRicorsoUdienzaConnection();
      connection.pageInfo = computePageInfo(from, `${offset + records}`, total);
      if (ricorsoUdienza && ricorsoUdienza?.length > 0) {
        const mappedItems = ricorsoUdienza.map(item =>
          mapRicorsoUdienzaToModel(item),
        );
        connection.aggregate = {
          count: mappedItems.length,
          total,
          totalElement: total,
        };
        const start =
          Number.parseInt(
            connection?.pageInfo?.startCursor
              ? connection?.pageInfo?.startCursor
              : '0',
          ) + 1;
        const edges = mappedItems.map((fe, index) => ({
          cursor: `${start + index}`,
          node: fe,
        }));
        connection.edges = edges;
        return connection;
      } else {
        connection.edges = [];
        return connection;
      }
    }
    throw new FascicoloNotFoundException(
      `Ricorsi in udienza non trovati per idUdienza:${idUdienza}`,
    );
  }
}

const mapRicorsoUdienzaToModel = (
  entity: PenaleTRicorsoUdienzaEntity,
): PenaleTRicudien => {
  return {
    // mappa qui tutti i campi necessari
    ...entity,
  };
};
