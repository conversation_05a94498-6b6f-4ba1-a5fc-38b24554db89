import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTEsitoSentEntity } from '../consultazioni-graphql/entities/penale_t_esito_sent.entity';

@UfficioService()
export class EsitiSentService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  esitiSent(): Promise<PenaleTEsitoSentEntity[]> {
    return this.connection.getRepository(PenaleTEsitoSentEntity).find();
  }

  esitoSent(idEsito: number): Promise<PenaleTEsitoSentEntity | null> {
    return this.connection
      .getRepository(PenaleTEsitoSentEntity)
      .findOneBy({ idEsito: idEsito });
  }
}
