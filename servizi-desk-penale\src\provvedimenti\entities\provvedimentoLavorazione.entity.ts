import { BeforeInsert, Column, Entity, PrimaryColumn } from 'typeorm';
import { v4 as uuid4 } from 'uuid';

@Entity({ name: 'PROVVEDIMENTI_LAVORAZIONE' })
export class ProvvedimentoLavorazioneEntity {
  @PrimaryColumn({ name: 'IDPROVVLAV' })
  idProvvLav: string;

  @BeforeInsert()
  generateUuid() {
    this.idProvvLav = uuid4().replace(/-/g, '');
  }

  @Column({ name: 'IDFASC' })
  idFasc: string;

  @Column({ name: 'SUBPROC' })
  subProc: string;

  @Column({ name: 'CODICEUFFICIO' })
  codiceUfficio: string;

  @Column({ name: 'TIPOPROVVEDIMENTO' })
  tipoProvvedimento: string;

  @Column({ name: 'DISPOSITIVOPROVVEDIMENTO' })
  dispositivoProvvedimento: string;

  @Column({ name: 'DESCRDISPOSITIVOPROVVEDIMENTO' })
  descrDispositivoProvvedimento: string;

  @Column({ name: 'CFGIUDICE' })
  cfGiudice: string;

  @Column({ name: 'IDSTORICOISTANZA' })
  idStoricoIstanza: string;

  @Column({ name: 'DESCRSTORICOISTANZA' })
  descrStoricoIstanza: string;

  @Column({ name: 'IDCATDEPOSITO' })
  idCatDeposito: number;
  public constructor(init?: Partial<ProvvedimentoLavorazioneEntity>) {
    Object.assign(this, init);
  }
  public toString = (): string => {
    return `${JSON.stringify(this)}`;
  };
}
