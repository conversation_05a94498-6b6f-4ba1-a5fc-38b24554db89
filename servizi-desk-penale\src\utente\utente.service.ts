import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTUtenteEntity } from '../consultazioni-graphql/entities/penale_t_utente.entity';

@UfficioService()
export class UtenteService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  utenti(): Promise<PenaleTUtenteEntity[]> {
    return this.connection.getRepository(PenaleTUtenteEntity).find();
  }

  utente(idUtente: number): Promise<PenaleTUtenteEntity | null> {
    return this.connection
      .getRepository(PenaleTUtenteEntity)
      .findOneBy({ idUtente: idUtente });
  }

  async utenteByCf(codiceFiscale: string): Promise<PenaleTUtenteEntity | null> {
    return this.connection
      .getRepository(PenaleTUtenteEntity)
      .findOneBy({ codiceFiscale: codiceFiscale });
  }
}
