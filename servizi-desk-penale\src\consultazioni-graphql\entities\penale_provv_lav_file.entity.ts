import { BeforeInsert, Column, <PERSON>tity, PrimaryColumn } from 'typeorm';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';
import { v4 as uuid4 } from 'uuid';

@Entity('PROVV_LAV_FILE') //nome tabella su schema oracle
export class PenaleProvvLavFileEntity {
  @PrimaryColumn({ name: 'IDCAT', default: 'uuid_generate_v4()' })
  idCategoria: string;

  @BeforeInsert()
  generateUuid() {
    this.idCategoria = uuid4().replace(/-/g, '');
  }

  @Column({ name: 'IDPROVV' })
  idProvvedimento: string;
  @Column({ name: 'NOMEFILE' })
  nomeFile: string;
  @Column({ name: 'CONTENT' })
  content: Buffer;
  @Column({ name: 'MIMETYPE' })
  mimeType: string;
  @Column({
    name: 'SIGNED',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  signed: boolean;
  @Column({ name: 'TIPOFILE' })
  tipoFile: string;

  @Column({
    name: 'OSCURATO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  oscurato: boolean;
  public constructor(init?: Partial<PenaleProvvLavFileEntity>) {
    Object.assign(this, init);
  }

  @Column({ name: 'CREATE_AT' })
  createAt: Date;
}
