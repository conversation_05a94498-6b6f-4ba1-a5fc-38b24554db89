import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ConfigLoader } from './config-loader';
import { ConfigurationController } from './configuration.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [ConfigLoader],
      isGlobal: true,
    }),
  ],
  providers: [ConfigurationController],
  exports: [ConfigurationController],
})
export class ConfigurationModule {}
