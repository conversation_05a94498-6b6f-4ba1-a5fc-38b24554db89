import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleVerbaleRicorsoEntity } from '../consultazioni-graphql/entities/penale_verbale_ricorso.entity';
import { PenaleMotivazioniService } from 'src/penale-motivazioni/penale-motivazioni.service';

@UfficioService()
export class VerbaleRicorsoService {
  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly penaleMotivazioniService: PenaleMotivazioniService,
  ) {}

  verbaleRicorsi(): Promise<PenaleVerbaleRicorsoEntity[]> {
    return this.connection.getRepository(PenaleVerbaleRicorsoEntity).find();
  }

  async verbaleRicorso(
    idRicUdien: number,
  ): Promise<PenaleVerbaleRicorsoEntity | null> {
    const verbaleRicorso = await this.connection
      .getRepository(PenaleVerbaleRicorsoEntity)
      .findOneBy({ idRicorsoUdienza: idRicUdien });
    const penaleMotivazioni =
      await this.penaleMotivazioniService.findByRicudien(idRicUdien);

    if (penaleMotivazioni.length > 0) {
      const motivazione = penaleMotivazioni[0];
      return {
        ...verbaleRicorso,
        testoVerbale: motivazione.noteDifensori ?? '',
      } as PenaleVerbaleRicorsoEntity;
    }
    return verbaleRicorso;
  }
}
