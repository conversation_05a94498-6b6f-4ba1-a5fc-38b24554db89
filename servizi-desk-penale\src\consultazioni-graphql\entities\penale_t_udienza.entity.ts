import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';
import { PenaleTSentenzaEntity } from './penale_t_sentenza.entity';
import ColumnDateIsoTransformer from './utility/column-date-iso-transformer';

@Entity('PENALE_T_UDIENZA') //nome tabella su schema oracle
export class PenaleTUdienzaEntity {
  @PrimaryColumn({ name: 'ID_UDIEN' })
  idUdien: number;

  @Column({
    name: 'DATAUD',
    type: 'timestamp with time zone',
    transformer: new ColumnDateIsoTransformer(),
  })
  dataUdienza: Date;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_TIPOUD' })
  tipoUdienza: PenaleParamEntity;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_SEZIONE' })
  sezione: PenaleParamEntity;

  @Column({ name: 'ID_TIPOUD' })
  idTipoDiUdienza: number;

  @Column({ name: 'ID_FUNZIONE' })
  idFunzionario: number;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'OPERATORE' })
  operatore: number;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_AULA' })
  aula: PenaleParamEntity;

  @Column({ name: 'NOTEPG' })
  notePg: string;
  @Column({ name: 'INIZIOUDIENZA' })
  inizioUdienza: Date;
  @Column({ name: 'FINEUDIENZA' })
  fineUdienza: Date;

  sentenza?: Array<PenaleTSentenzaEntity>;
  isEstensore?: boolean;
  allPubblicate?: boolean;
}
