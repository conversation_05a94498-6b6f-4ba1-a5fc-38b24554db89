import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('V_DESK_CONTEGGIO_SCRIVANIA') //nome vista su schema oracle
export class PenaleDeskConteggioScrivaniaEntity {
  @PrimaryColumn({ name: 'ID_UDIENZA' })
  idUdienza: number;
  @Column({ name: 'DATAUD' })
  dataUd: Date;
  @Column({ name: 'CF_PRESIDENTE', nullable: true })
  cfPresidente: string;
  @Column({ name: 'SEZIONE', nullable: true })
  sezione: string;
  @Column({ name: 'TIPOUD', nullable: true })
  tipoUd: string;
  @Column({ name: 'AULA', nullable: true })
  aula: string;
  @Column({ name: 'MESE', nullable: true })
  mese: number;
  @Column({ name: 'DATA_UDIENZA', nullable: true })
  dataUdienza: string;
  @Column({ name: 'RICORSI_TOTALI', nullable: true })
  ricorsiTotali: number;
  @Column({ name: 'MINUTA_ACCETTATA', nullable: true })
  minutaAccettata: number;
  @Column({ name: 'MINUTA_ACCETTATA_MODIFICA', nullable: true })
  minutaAccettataModifica: number;
  @Column({ name: 'RICHIESTA_MODIFICA', nullable: true })
  richiestaModifica: number;
  @Column({ name: 'PUBBLICATI', nullable: true })
  pubblicati: number;
  @Column({ name: 'LAVORATI', nullable: true })
  lavorati: number;
  @Column({ name: 'FIRMATI', nullable: true })
  firmati: number;
  @Column({ name: 'TOT_PUBBLICATI', nullable: true })
  totPubblicati: number;
  @Column({ name: 'PUBBLICATI_SIC', nullable: true })
  pubblicatiSic: number;
  @Column({ name: 'MINUTE_PERVENUTE_SIC', nullable: true })
  minutePervenuteSic: number;
  @Column({ name: 'PROVV_DEPOSITATI_SIC', nullable: true })
  provvDepositatiSic: number;
  @Column({ name: 'BUSTA_RIFIUTATA', nullable: true })
  bustaRifiutata: number;
  @Column({ name: 'MINUTA_MODIFICATA_PRESIDENTE', nullable: true })
  minutaModificataPresidente: number; // nel dto corrisponde a bozzaMinutaModificata
  @Column({ name: 'BOZZA_PRESIDENTE', nullable: true })
  bozzaPresidente: number;
  @Column({ name: 'RIUNITI', nullable: true })
  riuniti: number;
}
