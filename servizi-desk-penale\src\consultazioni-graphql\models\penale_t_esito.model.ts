import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleTEsitoSent } from './penale_t_esito_sent.model';

@ObjectType()
export class PenaleTEsito {
  @Field(type => ID)
  idFunzione: number;

  @Field(type => Int)
  operatore: number;

  @Field(type => String)
  rinvioDescrizione: string;

  @Field(type => String)
  art28: string;

  @Field(type => String)
  art94: string;

  @Field(type => String)
  note: string;

  @Field(type => Int)
  idRicUdienza: number;

  @Field(type => Int)
  idEsito: number;

  @Field(type => String)
  motivoSosp: string;

  @Field(type => String)
  idReatoParte: string;

  @Field(type => Boolean)
  riunito: boolean;

  @Field(type => String)
  privacy: string;

  privacyParam?: PenaleParam;

  @Field(type => String)
  semplificata: string;

  esitoSent?: PenaleTEsitoSent;
}
