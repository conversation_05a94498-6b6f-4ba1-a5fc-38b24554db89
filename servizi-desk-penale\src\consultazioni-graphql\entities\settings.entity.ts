import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('SETTINGS') //nome tabella su schema oracle
export class SettingsEntity {
  @PrimaryColumn({ name: 'CF_UTENTE' })
  cfUtente: string;

  @Column({ name: 'USERNAME_FIRMA' })
  usernameFirma: string;

  @Column({ name: 'DT_CREAZIONE' })
  dtCreazione: Date;

  @Column({ name: 'DT_MODIFICA' })
  dtModifica: Date;

  @Column({ name: 'PASSWORD_FIRMA' })
  passwordFirma: string;
}
