import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, <PERSON>rent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { ReatiService } from '../../reati/reati.service';
import { PenaleTReati } from '../models/penale_t_reati.model';
import { PenaleTUtente } from '../models/penale_t_utente.model';
import { PenaleProvvedimentiNote } from '../models/penale_provvedimenti_note.model';

@Resolver(() => PenaleTParti)
export class PenaleReatiResolver {
  constructor(private readonly reatiService: ReatiService) {}

  @Query(() => PenaleTReati, { name: 'reato' })
  async reato(@Args('id') id: number): Promise<PenaleTReati> {
    const parti = await this.reatiService.reato(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleTReati], { name: 'reati' })
  reati(): Promise<PenaleTReati[]> {
    return this.reatiService.reati();
  }
}
