import { Field, InputType, Int } from '@nestjs/graphql';
import { ArgsDepositoProvvedimentoInput } from '../../../provvedimenti/entities/dto/provvLavorazione.input';
import { ProvvedimentiOrigineEnum } from '../enumaration/provvedimenti-origine.enum';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class CreateProvvLavorazioneInput {
  @ApiProperty()
  @Field(() => Int)
  nrg: number;
  @ApiProperty()
  @Field(() => ArgsDepositoProvvedimentoInput)
  argsProvvedimento: ArgsDepositoProvvedimentoInput;
  @Field(() => Int)
  idUdienza: number;
  @ApiProperty()
  @Field(() => ProvvedimentiOrigineEnum)
  origine: ProvvedimentiOrigineEnum;

  @ApiProperty()
  @Field(() => Boolean)
  allegatoOscurato?: boolean = false;
}
