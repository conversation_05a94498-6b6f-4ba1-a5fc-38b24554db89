import { Inject } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleControPartiEntity } from '../consultazioni-graphql/entities/penale_contro_parti.entity';

@UfficioService()
export class ControPartiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  getControParti(idParte: number[]): Promise<PenaleControPartiEntity[]> {
    return this.connection
      .getRepository(PenaleControPartiEntity)
      .find({ where: { idParte: In(idParte) } });
  }
}
