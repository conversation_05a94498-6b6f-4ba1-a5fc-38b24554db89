import { Resolver, Query } from '@nestjs/graphql';
import { Version } from './models/version.model';
import { VersionService } from './version.service';
import { Log } from 'src/decorators/log.decorator';

@Resolver(() => Version)
@Log()
export class VersionResolver {
  constructor(private readonly versionService: VersionService) {}

  @Query(() => Version, { name: 'appVersions' })
  async getAppVersions(): Promise<Version> {
    return await this.versionService.getVersions();
  }
}
