import {
  Column,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>,
  ManyToMany,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';

@Entity('PENALE_T_REATI') //nome tabella su schema oracle
export class PenaleTReatiEntity {
  @PrimaryColumn({ name: 'ID_REATO' })
  idReato: number;
  @Column({ name: 'FONTENORM' })
  fonteNorm: string;

  @Column({ name: 'ANNO' })
  anno: number;
  @Column({ name: 'ART' })
  art: number;
  @Column({ name: 'CAPO' })
  capo: number;
  @Column({ name: 'DESCR' })
  descrizione: string;
  @Column({ name: 'LETTERA' })
  lettera: string;

  @Column({ name: 'LIBRO' })
  libro: number;
  @Column({ name: 'COMMA' })
  comma: number;
  @Column({ name: 'NUMLEG' })
  numeroLegale: number;
  @Column({ name: 'ID_VOCE' })
  idVoce: number;
  @Column({ name: 'TITOLO' })
  titolo: number;
  @Column({ name: 'PARAGRAFO' })
  paragrafo: number;
  @Column({ name: 'OG<PERSON>' })
  oggi: Date;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OPERATORE' })
  operatore: number;

  @Column({ name: 'AGGRAVA' })
  aggrava: string;
  @Column({ name: 'VALIDO' })
  valido: string;
  @Column({ name: 'PRIVACY' })
  privacy: number;

  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;
  @Column({ name: 'GRUPPO' })
  gruppo: number;
  @Column({ name: 'PROVENIENZA' })
  provenienza: string;
  @Column({ name: 'DATA_FINE_UTILIZZO' })
  dataFineUtilizzo: Date;

  @Column({ name: 'EPPO' })
  eppo: string;

  @Column({ name: 'ALTRO_IDENTIFICATIVO' })
  altroIdentificativo: number;
}
