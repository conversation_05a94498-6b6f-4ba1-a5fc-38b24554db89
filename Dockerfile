# immagine base node-lts debian
FROM harbor.netserv.it/portali/node:lts-bullseye AS base-oracle

# Installa le dipendenze di Oracle e dos2unix
RUN apt-get update \
    && apt-get -y upgrade \
    && apt-get -y install --no-install-recommends libaio1 dos2unix \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Scarica e installa il client Oracle
RUN cd /opt \
    && wget https://download.oracle.com/otn_software/linux/instantclient/215000/instantclient-basic-linux.x64-********.0dbru.zip -O ora.zip \
    && unzip ora.zip \
    && rm -f  ora.zip

# Aggiungi le librerie client di Oracle al PATH del linker
ENV LD_LIBRARY_PATH "/opt/instantclient_21_5:$LD_LIBRARY_PATH"

# Immagine di sviluppo
FROM base-oracle AS development

# Installazione app
WORKDIR /usr/src/app
COPY servizi-desk-penale/package*.json servizi-desk-penale/yarn.lock  ./

RUN yarn install --production=false

# Copia degli environment
COPY servizi-desk-penale/uffici.yaml ./
COPY servizi-desk-penale/.env* ./
RUN dos2unix .env* && dos2unix uffici.yaml

COPY servizi-desk-penale/ .

RUN yarn prebuild && yarn build

# immagine di produzione
FROM base-oracle as production

# Ambiente prod
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

COPY servizi-desk-penale/package*.json servizi-desk-penale/yarn.lock ./

RUN yarn install --production=true

# Copia dell'env di produzione
COPY --from=development /usr/src/app/uffici.yaml ./
COPY --from=development /usr/src/app/.env.docker ./.env.production

# Copia degli eseguibili
COPY --from=development /usr/src/app/dist ./dist

CMD ["node", "--inspect=0.0.0.0:9229", "dist/main"]
