import { Module } from '@nestjs/common';
import { MultiTenantModule } from 'src/multi-tenant/multi-tenant.module';
import { ProvvedimentiNoteService } from './provvedimenti-note.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    AuthModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [ProvvedimentiNoteService],
  exports: [ProvvedimentiNoteService],
})
export class ProvvedimentiNoteModule {}
