import { applyDecorators, Type } from '@nestjs/common';
import { ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

export const ApiOkResponseArray = <DataDto extends Type<unknown>>(
  dataDto: DataDto,
  descrizione: string,
) =>
  applyDecorators(
    ApiOkResponse({
      status: 200,
      schema: {
        type: 'array',
        items: { $ref: getSchemaPath(dataDto) },
      },
      description: descrizione,
    }),
  );
