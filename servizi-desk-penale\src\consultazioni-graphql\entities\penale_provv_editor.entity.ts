import { BeforeInsert, Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';
import { v4 as uuid4 } from 'uuid';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';

@Entity('PENALE_PROVV_EDITOR') //nome tabella su schema oracle
export class PenaleProvvEditorEntity {
  @PrimaryColumn({ name: 'ID_PROVV_EDITOR' })
  idProvvedimentoEditor: string;

  @Column({ name: 'ID_PROVV' })
  idProvvedimento: string;

  @Column({
    name: 'TEXT_LIBERO',
    type: 'blob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  textLibero?: string | null;
  @Column({
    name: 'TEXT_OSCURATO',
    type: 'blob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  textOscurato?: string | null;

  @Column({
    name: 'INTRODUZIONE',
    type: 'blob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  introduzione?: string | null;
  @Column({
    name: 'MOTIVO_RICORSO',
    type: 'blob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  motivoRicorso?: string | null;
  @Column({
    name: 'FINALE_DISPOSITIVO',
    type: 'blob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  finaleDeposito?: string | null;
  @Column({
    name: 'PQM',
    type: 'blob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  pqm?: string | null;
  @Column({ name: 'CREATE_AT' })
  createAt: Date;
  @Column({
    name: 'STRUTTURATO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  strutturato: boolean;

  @Column({
    name: 'OSCURATO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  oscurato: boolean;

  public constructor(init?: Partial<PenaleProvvEditorEntity>) {
    Object.assign(this, init);
  }
  @BeforeInsert()
  generateUuid() {
    this.idProvvedimentoEditor = uuid4().replace(/-/g, '');
  }
}
