plugins {
    id "ns-patch" version "${patchPluginVersion}"
}

patch {
    includeVersionFile true
    includeConf false
    basename = 'SERVIZI-DESK-CP'
}


releaseNotes {

    withAs true
    // withDb a false per evitare che aggiunga la sezione per script db presenti nello stesso progetto.
	withDb false

    //modalitaApplicazioneDB "senza_fermo"
    modalitaApplicazioneDB "con_fermo"
    applicazionePatchAS {
        descrizione = "Il presente documento descrive la procedura di aggiornamento (patch) del prodotto servizi desk penale . La patch è rilasciata sotto forma di file compresso (.zip). Nel documento sono inoltre elencati impatti, modifiche ed interventi oggetto del presente aggiornamento."
        elenco = ["Effettuare il login alla macchina AS come utente root",
                  "Copiare il file zip in una cartella temporanea e scompattarlo",
                  "Stoppare il servizio httpd",
                  "Eseguire lo script 'install.sh' presente nella cartella 'application_server' (sh install.sh)",                    
                 // "Eseguire le configurazioni del paragrafo 'Modifiche Configurazione', per riavviare il servizio servizi-deskcp.service ",
                  "Riavviare il servizio httpd"
        ]
    }


    /*interventiEseguiti {
         descrizione = "Risoluzione ticket 47310"
            //  elenco=["Collegarsi alla macchina client del Portale DESK Cassazione Penale",
            //          "Accedere al file di configurazione .env nella root dell’applicazione (/opt/app)",
            //          "Inserire il seguente parametro per abilitare o meno la firma remota: ",
            //          "FIRMA_REMOTA=true"
            //           ]
    } */
    
    modificheEvolutive{
        descrizione = "Contratto CIG B2BD866C4A - PLO21 - Interventi migliorativi per l’operatività della Settima Sezione Penale"
    }


    dipendenzeSistemi{
        descrizione = "Installare prima la patch CASSAZIONE_PENALE_DB_1.04.00"
    }

    modificheDB {
        descrizione = "Sono riportate nella patch CASSAZIONE_PENALE_DB_1.04.00"
    }


/*
     modificheConfigurazione {
         descrizione = "Nel file del servizio /usr/lib/systemd/system/servizi-deskcp.service modificare la  WorkingDirectory in '/opt/servizi-desk-penale'. Eseguire il comando 'systemctl daemon-reload' e 'systemctl restart servizi-deskcp.service'"
    } 
*/
    impattiSistemi {
        descrizione = "Questa patch ha impatto sul sistema Web Desk Penale"
    } 

}


task copyBuild(type: Copy) {
    from "$rootDir/servizi-desk-penale/build"
    into "$buildDir/application_server/servizi-desk-penale"
}

copyBuild.dependsOn ':servizi-desk-penale:build'

afterEvaluate{
    tasks['patch'].dependsOn copyBuild
}

