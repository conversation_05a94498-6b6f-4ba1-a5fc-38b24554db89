import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { ReatiRicorsoService } from '../../reati-ricorso/reati-ricorso.service';
import { PenaleReatiRicorso } from '../models/penale_reati_ricorso.model';
import { PenaleTReati } from '../models/penale_t_reati.model';
import { ReatiService } from '../../reati/reati.service';
import { Utils } from '../../utils/utils';
import { PenaleParamService } from 'src/penale-param/penale-param.service';
import { PenaleParamEntity } from '../entities/penale_param.entity';
import { PenaleParam } from '../models/penale_param.model';

@Resolver(() => PenaleReatiRicorso)
export class PenaleReatiRicorsoResolver {
  constructor(
    private readonly reatiRicorsoService: ReatiRicorsoService,
    private readonly reatiService: ReatiService,
    private readonly penaleParamService: PenaleParamService,
  ) {}

  @Query(() => PenaleReatiRicorso, { name: 'reatoRicorso' })
  async reatoRicorso(@Args('id') id: number): Promise<PenaleReatiRicorso> {
    const parti = await this.reatiRicorsoService.reatoRicorso(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @ResolveField('reato', () => PenaleTReati, { nullable: true })
  async getReato(
    @Parent() reato: PenaleReatiRicorso,
    ): Promise<PenaleTReati | null> {
    let readServer:PenaleTReati | null = new PenaleTReati();
    let penaleParamReato: PenaleParam | null = null;
    
    if (reato?.idReato) {
      readServer = await this.reatiService.reato(reato.idReato);
    } 
    else if (reato.istProc){
      penaleParamReato = await this.penaleParamService.getParam(reato.istProc);
    }
    
    if (readServer) {
      const stagione = reato.tipoD?.descrizione ?? null;
      const stagioneConverita = Utils.reatoSostituisciData(
        stagione,
        reato.dataDa ?? null,
        reato.dataA ?? null,
      );
      let diplayReato = '';
      if (readServer.idReato){
        diplayReato = `${readServer.fonteNorm} ${readServer.art} ${readServer.numeroLegale} ${readServer.anno} ${readServer.lettera} ${readServer.comma} ${readServer.altroIdentificativo}`;
      }
      else {
        diplayReato = `${penaleParamReato?.descrizione ?? ''}`;
      }
      if (stagioneConverita) {
        diplayReato += ` ${stagioneConverita}`;
      }
      if (readServer.eppo && readServer.eppo !== '0') {
        diplayReato += ' EPPO';
      }
      readServer.displayReati = diplayReato;
    }
    
    return readServer;
  }


  @Query(returns => [PenaleReatiRicorso], { name: 'reatiRicorso' })
  reatiRicorso(): Promise<PenaleReatiRicorso[]> {
    return this.reatiRicorsoService.reatiRicorso();
  }
}
