import { InputType, Field, Int } from '@nestjs/graphql';
import { ArgsProvvedimentoInput } from './args-provvedimento.input';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class GenerateProvvLavorazioneInput {
  @ApiProperty()
  @Field(() => Int)
  nrg: number;
  @ApiProperty()
  @Field(() => Int)
  idUdienza: number;
  @ApiProperty()
  @Field(() => ArgsProvvedimentoInput)
  argsProvvedimento: ArgsProvvedimentoInput;
  @ApiProperty()
  @Field(() => Boolean)
  strutturato?: boolean = false;

  @ApiProperty()
  @Field(() => Boolean)
  allegatoOscurato?: boolean = false;

  @ApiProperty()
  @Field(() => Boolean)
  addCodeFirma?: boolean = false;
}
