import { Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleMotivazioniEntity } from '../consultazioni-graphql/entities/penale_motivazioni.entity';

@Injectable()
@UfficioService()
export class PenaleMotivazioniService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  async findAll(): Promise<PenaleMotivazioniEntity[]> {
    return this.connection.getRepository(PenaleMotivazioniEntity).find();
  }

  async findById(
    idMotivazione: number,
  ): Promise<PenaleMotivazioniEntity | null> {
    return this.connection
      .getRepository(PenaleMotivazioniEntity)
      .findOneBy({ idMotivazione });
  }

  async findByRicudien(idRicudien: number): Promise<PenaleMotivazioniEntity[]> {
    return this.connection
      .getRepository(PenaleMotivazioniEntity)
      .findBy({ idRicudien });
  }

  async create(
    motivazione: Partial<PenaleMotivazioniEntity>,
  ): Promise<PenaleMotivazioniEntity> {
    const newMotivazione = this.connection
      .getRepository(PenaleMotivazioniEntity)
      .create(motivazione);
    return this.connection
      .getRepository(PenaleMotivazioniEntity)
      .save(newMotivazione);
  }

  async update(
    idMotivazione: number,
    motivazione: Partial<PenaleMotivazioniEntity>,
  ): Promise<PenaleMotivazioniEntity | null> {
    await this.connection
      .getRepository(PenaleMotivazioniEntity)
      .update(idMotivazione, motivazione);
    return this.findById(idMotivazione);
  }

  async delete(idMotivazione: number): Promise<void> {
    await this.connection
      .getRepository(PenaleMotivazioniEntity)
      .delete(idMotivazione);
  }
}
