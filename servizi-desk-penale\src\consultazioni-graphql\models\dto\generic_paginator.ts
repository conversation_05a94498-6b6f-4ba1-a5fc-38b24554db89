import { Field, ObjectType, ArgsType } from '@nestjs/graphql';
import { PenaleTRicudien } from '../penale_t_ricudien.model';
import {
  ConnectionType,
  EdgeType,
} from '../../../relay-pagination/connection-paging';
import { ProvvedimentiStatoEnum } from '../../entities/enumaration/provvedimenti-stato.enum';
import { PaginationQueryBaseArgs } from '../../../relay-pagination/pagination-query.args';
import { ScrivaniaProvvedimentiModel } from '../scrivania_provvedimenti.model';

@ObjectType()
class AggregatePageInfo {
  @Field(_type => Number)
  count: number;
  @Field(_type => Number)
  total: number;
  @Field(_type => Number)
  totalElement?: number;
}

@ObjectType()
export class PenalRicorsoUdienzaEdge extends EdgeType(PenaleTRicudien) {}

@ObjectType()
export class PenaleRicorsoUdienzaConnection extends ConnectionType(
  PenaleTRicudien,
  PenalRicorsoUdienzaEdge,
) {
  @Field(_type => AggregatePageInfo)
  aggregate: AggregatePageInfo;
}

@ObjectType()
export class ProvvedimentiDtoEdge extends EdgeType(
  ScrivaniaProvvedimentiModel,
) {}

@ObjectType()
export class ProvvedimentiDtoConnection extends ConnectionType(
  ScrivaniaProvvedimentiModel,
  ProvvedimentiDtoEdge,
) {
  @Field(_type => AggregatePageInfo)
  aggregate: AggregatePageInfo;
}

/**
 * Parametro Input per la ricerca dei fascicoli
 */
@ArgsType()
export class PaginationCustomQueryFilter extends PaginationQueryBaseArgs {
  @Field(() => ProvvedimentiStatoEnum, { nullable: true })
  status?: ProvvedimentiStatoEnum | null;
}
