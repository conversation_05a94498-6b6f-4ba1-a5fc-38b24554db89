import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { VerbaleRicorsoService } from './verbale-ricorso.service';
import { PenaleMotivazioniModule } from '../penale-motivazioni/penale-motivazioni.module';

@Module({
  imports: [
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
    PenaleMotivazioniModule,
  ],
  providers: [VerbaleRicorsoService],
  exports: [VerbaleRicorsoService],
})
export class VerbaleRicorsoModule {}
