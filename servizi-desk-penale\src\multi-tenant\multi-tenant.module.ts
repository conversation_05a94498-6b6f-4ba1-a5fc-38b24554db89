import {
  DynamicModule,
  Inject,
  InternalServerErrorException,
  Logger,
  MiddlewareConsumer,
  Module,
  NestModule,
  Scope,
} from '@nestjs/common';
import { RouteInfo } from '@nestjs/common/interfaces/middleware/middleware-configuration.interface';
import { Type } from '@nestjs/common/interfaces/type.interface';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { NextFunction, Request } from 'express';
import { DataSource } from 'typeorm';
import { NsCustomLoggerTypeorm } from '../utils/ns-custom-logger-typeorm';

export const UFFICIO_CONNECTION = 'UFFICIO_CONNECTION';
export const MULTI_TENANT_MODULE_CONFIG_OPTIONS =
  'MULTI_TENANT_MODULE_CONFIG_OPTIONS';
export const DEFAULT_DB = 'DEFAULT';

export interface IDatebase {
  index: string;
  datasource: Promise<DataSource>;
}

export const databases = new Array<IDatebase>();

/**
 * Configurazione del modulo
 */
export interface MultiTenantModuleOptions {
  /**
   * Routes per le quali attivare la configurazione dinamica dell'ufficio
   */
  routes: (string | Type<any> | RouteInfo)[];
}

/**
 * Modulo per la configurazione automatica dello schema
 * d'ufficio attivo nella REQUEST corrente
 * in base all'ufficio specificato
 *
 *
 */
@Module({})
export class MultiTenantModule implements NestModule {
  private readonly LOG = new Logger(MultiTenantModule.name);
  private cassazioneDataSource: DataSource;

  constructor(
    @Inject(MULTI_TENANT_MODULE_CONFIG_OPTIONS)
    private readonly options: MultiTenantModuleOptions,
    private readonly configService: ConfigService,
  ) {
    this.createUfficioConnection();
  }

  private createOrGetConnection() {
    if (this.cassazioneDataSource && this.cassazioneDataSource.isInitialized) {
      return this.cassazioneDataSource;
    }
    const registro: any = this.configService.get(`app.uffici.datasource`);
    this.cassazioneDataSource = new DataSource({
      // name: codiceUfficio + ' - ' + codiceRegistro,
      type: registro.dbType,
      host: registro.dbHost,
      port: parseInt(registro.dbPort, 10),
      username: registro.dbUser,
      password: registro.dbPassword,
      database: registro.database, // undefined per Oracle
      serviceName: registro.dbServiceName, // definito per Oracle
      entities: [
        'dist/consultazioni-graphql/**/*.view.{ts,js}',
        'dist/consultazioni-graphql/**/*.entity.{ts,js}',
      ], // Path delle classi di entity

      synchronize: false, // WARNING: non mettere a true, altrimenti sovrascrive il db
      logger: new NsCustomLoggerTypeorm(),
    });
    const dataSourcePromise = this.cassazioneDataSource.initialize();
    databases.push({ index: DEFAULT_DB, datasource: dataSourcePromise });
    return dataSourcePromise;
  }

  /**
   * Configurazione dinamica del modulo
   *
   * E' necessario specificare le rotte influenzate dal modulo,
   * e la logica di estrazione del codice ufficio dalla request
   *
   * @param options opzioni del modulo
   */
  static forRoutes(options: MultiTenantModuleOptions): DynamicModule {
    return {
      module: MultiTenantModule,
      providers: [
        {
          provide: MULTI_TENANT_MODULE_CONFIG_OPTIONS,
          useValue: options,
        },
        {
          provide: UFFICIO_CONNECTION,
          inject: [REQUEST],
          scope: Scope.REQUEST,
          useFactory: async () => {
            const newVar =
              databases && databases.length > 0
                ? databases && databases.find(db1 => db1.index == DEFAULT_DB)
                : null;
            return newVar?.datasource;
          },
        },
      ],
      exports: [UFFICIO_CONNECTION],
    };
  }

  static forRoutesAsync(options: MultiTenantModuleOptions): DynamicModule {
    return {
      module: MultiTenantModule,
      providers: [
        {
          provide: MULTI_TENANT_MODULE_CONFIG_OPTIONS,
          useValue: options,
        },
        {
          provide: UFFICIO_CONNECTION,
          inject: [REQUEST],
          scope: Scope.REQUEST,
          useFactory: async () => {
            const newVar =
              databases && databases.length > 0
                ? databases && databases.find(db1 => db1.index == DEFAULT_DB)
                : null;
            return newVar?.datasource;
          },
        },
      ],
      exports: [UFFICIO_CONNECTION],
    };
  }
  async configure(consumer: MiddlewareConsumer): Promise<void> {
    consumer
      .apply(async (req: Request, _: Response, next: NextFunction) => {
        try {
          this.createOrGetConnection();
          next();
        } catch (e) {
          this.LOG.error('Errore nella creazione della connessione al DB.');
          throw new InternalServerErrorException(
            'Errore connessione allo schema di ufficio',
            `Non è stato possibile collegarsi allo schema dell'ufficio.`,
          );
        }
      })
      .forRoutes(...this.options.routes);
  }

  private createUfficioConnection() {
    try {
      this.createOrGetConnection();
    } catch (e) {
      this.LOG.log(`Errore  nell'inizializzata connessione per l'ufficio.`);
    }
  }
}
