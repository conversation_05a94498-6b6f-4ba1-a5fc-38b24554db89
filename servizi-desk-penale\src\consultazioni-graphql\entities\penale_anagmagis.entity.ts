import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_ANAGMAGIS') //nome tabella su schema oracle
export class PenaleAnagmagisEntity {
  @PrimaryColumn({ name: 'ID_ANAGMAGIS' })
  idAnagmagis: number;

  @Column({ name: 'COGNOME' })
  cognome: string;

  @Column({ name: 'NOME' })
  nome: string;
  @Column({ name: 'DATANASC' })
  dataNascita: Date;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @Column({ name: 'CODICE_FISCALE' })
  codiceFiscale: string;
}
