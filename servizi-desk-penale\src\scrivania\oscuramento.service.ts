import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { DataSource, In, IsNull } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { Inject } from '@nestjs/common';
import { PenaleVRicercaPenaleSentenzaEntity } from '../consultazioni-graphql/entities/penale_v_ricerca_penale_sentenza.entity';
import { PenaleTRicorsoEntity } from '../consultazioni-graphql/entities/penale_t_ricorso.entity';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { isNull } from '@nestjs/graphql/dist/plugin/utils/ast-utils';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { PenaleProvvEditorEntity } from '../consultazioni-graphql/entities/penale_provv_editor.entity';
import {
  CspbackendTimbripubbEntity,
  TipoOscuramentoEnum,
} from '../consultazioni-graphql/entities/cspbackend_timbripubb.entity';
import { TRicorsoUdienzaService } from '../ricorso-udienza/t-ricorso-udienza.service';
import { EsitiService } from '../esiti/esiti.service';

@UfficioService()
export class OscuramentoService {
  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly esitiService: EsitiService,
    private readonly ricorsoUdienza: TRicorsoUdienzaService,
  ) {}

  async provvedimentoByIdUdienAndNrg(idUdienza: number, nrg: number) {
    const porvvList = await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .find({
        select: {
          idProvvedimento: true,
          origine: true,
        },
        where: {
          nrg: nrg,
          idUdienza: idUdienza,
        },
        order: {
          dataUltimaModifica: 'asc',
        },
      });
    return porvvList?.length > 0 ? porvvList : null;
  }

  async isOscuratoEditor(idsProvv: string[]) {
    const porvvList = await this.connection
      .getRepository(PenaleProvvEditorEntity)
      .findOne({
        select: {
          idProvvedimentoEditor: true,
          oscurato: true,
        },
        where: {
          idProvvedimento: In(idsProvv),
        },
        order: {
          createAt: 'DESC',
        },
      });
    return porvvList?.oscurato || false;
  }

  /* serve a verificare il tipoOscuramento per uno specifico record
   * identificato da idUdienza e nrg nel database CspbackendTimbripubbEntity.*/
  async checkOscuramentoType(idUdienza: number, nrg: number) {
    const porvvList = await this.connection
      .getRepository(CspbackendTimbripubbEntity)
      .findOne({
        select: {
          idUdien: true,
          nrg: true,
          tipoOscuramento: true,
        },
        where: {
          idUdien: idUdienza,
          nrg: nrg,
        },
      });
    if (!porvvList) {
      return false;
    }
    return !(
      porvvList?.tipoOscuramento == null ||
      porvvList?.tipoOscuramento == TipoOscuramentoEnum.NESSUNO
    );
  }

  async getProvvedimentoOscurato(
    idUdien: number,
    nrg: number,
  ): Promise<boolean> {
    const ricUdin = await this.ricorsoUdienza.ricorsoUdienzaByNrgAndIdUdienza(
      idUdien,
      nrg,
    );
    if (ricUdin?.idRicudien) {
      const esitoRuolo = await this.esitiService.esito(ricUdin.idRicudien);
      if (esitoRuolo?.privacyParam && esitoRuolo.privacyParam?.sigla == 'SI') {
        return true;
      }
    }
    return false;
  }
}
