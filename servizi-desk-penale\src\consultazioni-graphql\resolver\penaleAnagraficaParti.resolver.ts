import { NotFoundException } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { AnagraficaPartiService } from '../../anagrafica-parti/anagrafica-parti.service';
import { PenaleAnagraficaParti } from '../models/penale_anagparti.model';

@Resolver(() => PenaleTParti)
export class PenaleAnagraficaPartiResolver {
  constructor(
    private readonly anagraficaPartiService: AnagraficaPartiService,
  ) {}

  @Query(() => PenaleAnagraficaParti, { name: 'anagraficaParte' })
  async parte(@Args('id') id: number): Promise<PenaleAnagraficaParti> {
    const parti =
      await this.anagraficaPartiService.anagraficaPartiByIdAnagraficaParte(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleAnagraficaParti], { name: 'anagraficaParti' })
  parti(): Promise<PenaleAnagraficaParti[]> {
    return this.anagraficaPartiService.anagraficaParti();
  }
}
