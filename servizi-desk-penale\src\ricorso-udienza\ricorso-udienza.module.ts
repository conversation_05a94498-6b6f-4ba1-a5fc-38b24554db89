import { Module } from '@nestjs/common';
import { MultiTenantModule } from 'src/multi-tenant/multi-tenant.module';
import { TRicorsoUdienzaService } from './t-ricorso-udienza.service';
import { RicorsoUdienzaCommonService } from './ricorso-udienza-common.service';
import { PenaleUdienzaModule } from '../penale-udienza/penale-udienza.module';
import { RicercaSentenzaModule } from '../ricerca-sentenza/ricerca-sentenza.module';
import { FascicoloModule } from '../fascicolo/fascicolo.module';

@Module({
  imports: [
    PenaleUdienzaModule,
    RicercaSentenzaModule,
    FascicoloModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [TRicorsoUdienzaService, RicorsoUdienzaCommonService],
  exports: [TRicorsoUdienzaService, RicorsoUdienzaCommonService],
})
export class RicorsoUdienzaModule {}
