import {
  Inject,
  Injectable,
  Logger,
  Scope,
  UnauthorizedException,
} from '@nestjs/common';
import { Role } from './role.enum';
import { Request } from 'express';
import { REQUEST } from '@nestjs/core';
import { HEADER_COD_FISCALE } from '../constants';
import { UtentiQueryAuthService } from '../utenti-auth/utenti-query-auth.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Utils } from '../utils/utils';
import { FirmaRemotaErrorException } from '../exceptions/firma-remota-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { RuoloNotFoundException } from '../exceptions/ruolo-not-found.exception';
import { UnauthorizedCustomException } from '../exceptions/unauthorized-custom.exception';
import { Log } from '../decorators/log.decorator';

const scopeFirmaRemota = 'firmaremota';
/**
 * Servizio di verifica autorizzazioni
 */

@Injectable({ scope: Scope.REQUEST })
export class AuthService {
  private logger = new Logger(AuthService.name);
  private codeEnabledModHeader = 'app.enableModheader';
  constructor(
    @Inject(REQUEST)
    private readonly request: Request,
    private jwtService: JwtService,
    private readonly utentiQueryAuthService: UtentiQueryAuthService,
    private configService: ConfigService,
  ) {}

  /**
   * Verifica che il soggetto abbia almeno uno dei ruoli richiesti
   * @param cf codice fiscale del soggetto
   * @param roles lista dei ruoli abilitati
   */
  async checkRoles(cf: string, roles: Role[]): Promise<boolean> {
    return roles.some(r => this.hasRole(r));
  }

  /**
   * Verifica che il soggetto abbia il ruolo specificato
   * @param cf codice fiscale del soggetto
   * @param roles lista dei ruoli abilitati
   */
  async hasRole(role: Role): Promise<boolean> {
    switch (role) {
      case Role.PRESIDENTE:
        return await this.utentiQueryAuthService.isPresidente(
          await this.getCurrentUser(),
        );
      case Role.RELATORE:
        return await this.utentiQueryAuthService.isRelatore(
          await this.getCurrentUser(),
        );
      case Role.ESTENSORE:
        return await this.utentiQueryAuthService.isEstensore(
          await this.getCurrentUser(),
        );
      default:
        return false;
    }
  }

  async getCf(): Promise<string> {
    const currentUser = await this.getCurrentUser();
    return currentUser;
  }

  async isRelatoreforIdUdienza(nrg: number) {
    return await this.utentiQueryAuthService.isRelatoreByIdUdienza(
      nrg,
      await this.getCurrentUser(),
    );
  }
  async isPresidenteByNrgAndIdUdienza(nrg: number, idUdienza: number) {
    return await this.utentiQueryAuthService.isPresidenteByNrgAndIdUdienza(
      nrg,
      await this.getCurrentUser(),
      idUdienza,
    );
  }
  async isEstensoreforNrg(nrg: number) {
    return await this.utentiQueryAuthService.isEstensoreByNrg(
      nrg,
      await this.getCurrentUser(),
    );
  }
  async isEstensoreforNrgAndIdUdienza(nrg: number, idUdiuen: number) {
    return await this.utentiQueryAuthService.isEstensoreByNrgAndIdUdienza(
      nrg,
      idUdiuen,
      await this.getCurrentUser(),
    );
  }

  async getIdMagisEstensoreForNrg(nrg: number, idUdien: number) {
    return await this.utentiQueryAuthService.getIdMagisEstensore(nrg, idUdien);
  }
  async checkFiscalCodeRefactory(fiscalCode: string): Promise<Role[]> {
    const roles = new Array<Role>();
    const isPresidente = await this.utentiQueryAuthService.isPresidente(
      await this.getCurrentUser(),
    );
    const isRelatore = await this.utentiQueryAuthService.isRelatore(
      await this.getCurrentUser(),
    );
    const isEstensore = await this.utentiQueryAuthService.isEstensore(
      await this.getCurrentUser(),
    );
    if (isPresidente) {
      roles.push(Role.PRESIDENTE);
    }
    if (isRelatore) {
      roles.push(Role.RELATORE);
    }
    if (isEstensore) {
      roles.push(Role.ESTENSORE);
    }
    return roles;
  }

  async getRole(): Promise<Role[]> {
    const roles = new Array<Role>();
    roles.push(
      ...(await this.checkFiscalCodeRefactory(await this.getCurrentUser())),
    );
    if (roles?.length == 0) {
      throw new RuoloNotFoundException(
        "Ruolo dell'utente non determinato - Contattare il CED",
      );
    }
    this.logger.debug(`utente loggato con roles:${JSON.stringify(roles)}`);
    return roles;
  }

  /**
   * Ottiene nome e cognome dell'utente corrente
   */  
  async getNomeCognomeUser(): Promise<string> {
    const enableModheader = this.configService.get(
      this.codeEnabledModHeader,
    ) as string;
    if (Utils.isBoolean(enableModheader)) {
      const fiscalCode = this.request.headers[HEADER_COD_FISCALE] as string;
      if (fiscalCode) {
        const magisByCf = await this.utentiQueryAuthService.getMagisByCf(
          fiscalCode,
        );
        return magisByCf?.nome + ' ' + magisByCf?.cognome;
      }
    }
    const token = this.extractTokenFromHeader(this.request);
    if (token) {
      try {
        const payload = this.jwtService.decode(token) as any;
        if (payload?.fiscalNumber) {
          const fiscalCode = payload?.fiscalNumber as string;
          const magisByCf = await this.utentiQueryAuthService.getMagisByCf(
            fiscalCode,
          );
          return magisByCf?.nome + ' ' + magisByCf?.cognome;
        } else {
          throw new UnauthorizedException('CODE_UNAUTHORIZED');
        }
      } catch (e) {
        this.logger.error(
          'Errore nel check del token per la restiruzione del nome e del cognome.',
          e,
        );
        throw new UnauthorizedException('CODE_UNAUTHORIZED');
      }
    }
    throw new UnauthorizedException('CODE_UNAUTHORIZED');
  }

  /**
   * Ottiene l'utente corrente dal contesto
   */
  async getCurrentUser(): Promise<string> {
    const modHeader = await this.getModHeader();
    if (modHeader) {
      return modHeader;
    }
    try {
      const payload = await this.getPayload();
      if (payload?.fiscalNumber) {
        const fiscalCode = payload?.fiscalNumber as string;
        await this.checkFiscalCode(fiscalCode);
        return fiscalCode;
      } else {
        throw new UnauthorizedException('CODE_UNAUTHORIZED');
      }
    } catch (e) {
      this.logger.error('Errore nel check del token per il current user.', e);
      throw e;
    }
  }

  /**
   * Controlla se l'utente ha la firma remota abilitata
   */
  async getCheckUser(): Promise<boolean> {
    try {
      const payload = await this.getPayload();
      if (payload?.scp) {
        const scope = payload?.scp as string;
        if (scope?.includes(scopeFirmaRemota)) {
          return true;
        } else {
          throw new FirmaRemotaErrorException(
            'Firma remota non abilitata',
            CodeErrorEnumException.FIRMA_REMOTA_NOT_ENABLED,
          );
        }
      } else {
        throw new UnauthorizedException('CODE_UNAUTHORIZED');
      }
    } catch (e) {
      this.logger.log("Errore nel check dell'utente", e);
      throw e;
    }
  }

  async getModHeader(): Promise<string | null> {
    const header = this.request.headers[HEADER_COD_FISCALE] as string;
    const enableModheader = this.configService.get(
      this.codeEnabledModHeader,
    ) as string;
    if (header && Utils.isBoolean(enableModheader)) {
      await this.checkFiscalCode(header);
      return header;
    }
    return null;
  }

  async getPayload(): Promise<any> {
    const token = this.extractTokenFromHeader(this.request);
    if (!token) {
      throw new UnauthorizedException('CODE_UNAUTHORIZED');
    }
    try {
      const payload: any = this.jwtService.decode(token) as any;
      if (payload?.fiscalNumber) {
        return payload;
      } else {
        throw new UnauthorizedException('CODE_UNAUTHORIZED');
      }
    } catch (e) {
      this.logger.error('Errore nel check del token.', e);
      throw new UnauthorizedException('CODE_UNAUTHORIZED');
    }
  }

  /**
   * Ottiene l'utente corrente dal contesto
   */
  async getUserEmail(): Promise<string> {
    const enableModheader = this.configService.get(
      this.codeEnabledModHeader,
    ) as string;
    if (Utils.isBoolean(enableModheader)) {
      const email = this.request.headers[HEADER_COD_FISCALE] as string;
      if (email) {
        return 'Enabled@ModHeader';
      }
    }
    const token = this.extractTokenFromHeader(this.request);
    if (!token) {
      throw new UnauthorizedException('CODE_UNAUTHORIZED');
    }
    try {
      const payload = this.jwtService.decode(token) as any;
      if (payload?.ADN_User) {
        const userEmail = payload?.ADN_User as string;
        return userEmail;
      } else {
        this.logger.log(
          'Errore nel check del token payload o adn_user non valorizzati',
        );
        throw new UnauthorizedException('CODE_UNAUTHORIZED');
      }
    } catch (e) {
      this.logger.error('Errore nel controllo del token', e);
      throw new UnauthorizedException('CODE_UNAUTHORIZED');
    }
  }

  private async checkFiscalCode(fiscalCode: string) {
    const magisByCf = await this.utentiQueryAuthService.getMagisByCf(
      fiscalCode,
    );
    if (!magisByCf) {
      this.logger.log(
        'Magistrato presente nel Sic ma il CF o non è presente oppure è errato',
      );
      throw new UnauthorizedCustomException(
        'CODE_MAGIS_SIC',
        CodeErrorEnumException.CODE_MAGIS_SIC,
      );
    }
    const utenteByCf = await this.utentiQueryAuthService.getUtenteByCf(
      fiscalCode,
    );
    if (!utenteByCf) {
      this.logger.log('Utente non registrato nel sic penale');
      throw new UnauthorizedCustomException(
        'CODE_UTENTE_SIC',
        CodeErrorEnumException.CODE_UTENTE_SIC,
      );
    }
  }

  extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  async getCurrentId(): Promise<number> {
    const idUtente = await this.utentiQueryAuthService.getIdUtente(
      await this.getCurrentUser(),
    );
    return idUtente || 0;
  }
}
