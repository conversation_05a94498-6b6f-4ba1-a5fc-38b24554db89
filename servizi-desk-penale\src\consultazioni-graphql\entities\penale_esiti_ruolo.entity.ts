import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_ESITI_RUOLO') //nome tabella su schema oracle
export class PenaleEsitiRuoloEntity {
  @PrimaryColumn({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'ID_UDIEN' })
  idUdienza: number;
  @Column({ name: 'ID_ESITO_RUOLO' })
  idEsitoRuolo: number;

  @Column({ name: 'OSCURAMENTO' })
  oscuramento: string;
  @Column({ name: 'DATA_INSERIMENTO' })
  dataInserimento: Date;
  @Column({ name: 'DATA_MODIFICA' })
  dataModifica: Date;

  public constructor(init?: Partial<PenaleEsitiRuoloEntity>) {
    Object.assign(this, init);
  }
}
