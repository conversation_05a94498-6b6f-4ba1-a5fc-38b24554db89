import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleTParti } from './penale_t_parti.model';
import { PenaleReatiRicorso } from './penale_reati_ricorso.model';
import { PenaleTSpoglio } from './penale_t_spoglio.model';
import { PenaleListPartiModel } from './penale_list_parti.model';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { PenaleTUdienza } from './penale_t_udienza.model';
import { PenaleProvvedimenti } from './penale_provvedimenti.model';

@ObjectType()
export class PenaleTRicorso {
  @Field(type => ID)
  nrg: number;

  @Field(type => Date)
  dataIscrizione: Date;

  parti?: PenaleTParti[];

  listaParti?: PenaleListPartiModel;
  @Field(type => Int)
  anno?: number;
  @Field(type => Int)
  numero?: number;

  @Field(type => String)
  statoRicorso?: string;

  @Field(type => String)
  detParti?: string;

  doveSta?: PenaleParam;
  doveFto?: PenaleParam;
  tipoRicorso?: PenaleParam;

  @Field(type => Int)
  codinam1?: number;
  @Field(type => Int)
  codinam2?: number;
  @Field(type => Int)
  codinam3?: number;
  @Field(type => Int)
  nrgPrinc?: number;
  @Field(type => Int)
  idFunzionario: number;
  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  operatore: number;
  @Field(type => String)
  urgente?: string;
  @Field(type => Int)
  idRinvio?: number;
  @Field(type => Int)
  nrgReale: number;
  @Field(type => Int)
  idSezerr?: number;
  @Field(type => String)
  confisca: string;
  @Field(type => String)
  mafia: string;
  @Field(type => String)
  note?: string;
  sezione?: PenaleParam;
  @Field(type => String)
  vecchioRito: string;
  @Field(type => Int)
  idMagisinam?: number;
  @Field(type => Int)
  idDoveinc?: number;
  @Field(type => Date)
  dataPassa?: Date;
  @Field(type => Date)
  dataPassaFto?: Date;
  @Field(type => Date)
  dataPassaInc?: Date;
  @Field(type => String)
  tipoRicorsoOld?: string;
  @Field(type => Int)
  idAuloInc?: number;
  @Field(type => Int)
  privacy?: number;
  @Field(type => Int)
  glbDtime?: number;
  @Field(type => Int)
  numParti?: number;
  @Field(type => String)
  flagSitmp: string;
  @Field(type => Date)
  dataDecorsoImp?: Date;
  @Field(type => String)
  reatiRicorso?: PenaleReatiRicorso[];
  spoglio?: PenaleTSpoglio;
  provvedimentoImpugnato?: string;
  udienza?: PenaleTUdienza;
  provvedimento?: PenaleProvvedimenti;
  public constructor(init?: Partial<PenaleTRicorso>) {
    Object.assign(this, init);
  }
}
