import { BeforeInsert, Column, Entity, PrimaryColumn } from 'typeorm';
import { v4 as uuid4 } from 'uuid';
import { ProvvedimentiTipoEnum } from './enumaration/provvedimenti-tipo.enum';
import { ProvvedimentiStatoEnum } from './enumaration/provvedimenti-stato.enum';
import { ProvvedimentiOrigineEnum } from './enumaration/provvedimenti-origine.enum';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';

@Entity('PENALE_PROVVEDIMENTI') //nome tabella su schema oracle
export class PenaleProvvedimentiEntity {
  @PrimaryColumn({
    name: 'ID_PROVV',
    default: 'uuid_generate_v4()',
  })
  idProvvedimento: string;

  @BeforeInsert()
  generateUuid() {
    this.idProvvedimento = uuid4().replace(/-/g, '');
  }

  @Column({ name: 'NOME_DOCUMENTO' })
  nomeDocumento: string;

  @Column({ name: 'NRG' })
  nrg: number;
  @Column({ name: 'ID_UDIEN' })
  idUdienza: number;
  @Column({
    name: 'STATO',
  })
  stato: ProvvedimentiStatoEnum;
  @Column({ name: 'TIPO' })
  tipo: ProvvedimentiTipoEnum;
  @Column({ name: 'ORIGINE' })
  origine: ProvvedimentiOrigineEnum;
  @Column({ name: 'DATA_DEPOSITO' })
  dataDeposito: Date;

  @Column({ name: 'DATA_ULTIMA_MODIFICA' })
  dataUltimaModifica: Date;
  @Column({ name: 'DATA_DECISIONE' })
  dataDecisione: Date;

  @Column({ name: 'ID_AUTORE' })
  idAutore: number;
  @Column({ name: 'FK_IDCAT', nullable: true })
  fkIdCat?: number;

  @Column({ name: 'MODIFICA_PRESIDENTE' })
  modificaPresidente: boolean;

  @Column({
    name: 'LAST_MODIFIED',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  lastModified: boolean;

  // campo transiente true se va disabilitato il bottone duplicato
  disabledButton?: boolean;

  isDuplicato: boolean;
  hasDuplicato?: boolean;

  // campo transiente
  enabledRichiestaDiModificaEVerificato?: boolean;

  isRevisione?: boolean;
  @Column({
    name: 'PROVV_LOCK',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  provvedimentoLock: boolean;
  public constructor(init?: Partial<PenaleProvvedimentiEntity>) {
    Object.assign(this, init);
  }
}
