import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_PARTI_CONTRO') //nome tabella su schema oracle
export class PenaleControPartiEntity {
  @PrimaryColumn({ name: 'ID_PARTE_1' })
  idParte: number;

  @PrimaryColumn({ name: 'ID_PARTE_2' })
  idControParte: number;

  @Column({ name: 'OPERATORE' })
  OPERATORE: number;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  public constructor(init?: Partial<PenaleControPartiEntity>) {
    Object.assign(this, init);
  }
}
