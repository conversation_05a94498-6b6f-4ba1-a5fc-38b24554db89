import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, <PERSON>rent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { PartiService } from '../../parti/parti.service';
import { AnagraficaPartiService } from '../../anagrafica-parti/anagrafica-parti.service';
import { DifensoriPartiService } from '../../difensori-parti/difensori-parti.service';
import { PenaleAnagraficaParti } from '../models/penale_anagparti.model';
import { PenaleDifenparti } from '../models/penale_difenparti.model';
import { PenaleParteLegate } from '../models/penale_parte_legate.model';
import { PartiLegateService } from '../../parti-legate/parti-legate.service';

@Resolver(() => PenaleTParti)
export class PenaleTPartiResolver {
  constructor(
    private readonly partiService: PartiService,
    private readonly anagraficaPartiService: AnagraficaPartiService,
    private readonly partiLegateService: PartiLegateService,
    private readonly difensoriPartiService: DifensoriPartiService,
  ) {}

  @ResolveField('anagraficaParte', () => PenaleAnagraficaParti, {
    nullable: true,
  })
  async getAnagraficaParti(
    @Parent() penaleTParti: PenaleTParti,
  ): Promise<PenaleAnagraficaParti | null> {
    if (penaleTParti.idAnagraficaParte && penaleTParti.idAnagraficaParte > 0) {
      return await this.anagraficaPartiService.anagraficaPartiByIdAnagraficaParte(
        penaleTParti.idAnagraficaParte,
      );
    }
    return null;
  }

  @ResolveField('parteLegata', () => PenaleParteLegate, {
    nullable: true,
  })
  async getParteLegate(
    @Parent() penaleTParti: PenaleTParti,
  ): Promise<PenaleParteLegate | null> {
    if (penaleTParti.idAnagraficaParte && penaleTParti.idParte > 0) {
      return await this.partiLegateService.parteLegata(penaleTParti.idParte);
    }
    return null;
  }

  @ResolveField('displayParti', () => String, {
    nullable: true,
  })
  async getDisplayParti(
    @Parent() penaleTParti: PenaleTParti,
  ): Promise<string | null> {
    if (penaleTParti && penaleTParti.tipoFig) {
      return `${penaleTParti.tipoFig?.sigla} - ${
        penaleTParti.ricorrente ? 'R ' : 'N '
      }`;
    }
    return null;
  }

  @ResolveField('difensori', () => [PenaleDifenparti])
  async getDifensori(
    @Parent() penaleTParti: PenaleTParti,
  ): Promise<PenaleDifenparti[] | null> {
    if (penaleTParti.idParte > 0) {
      return await this.difensoriPartiService.difensoreByIdParte(
        penaleTParti.idParte,
      );
    }
    return null;
  }

  @Query(() => PenaleTParti, { name: 'parte' })
  async parte(@Args('id') id: number): Promise<PenaleTParti> {
    const parti = await this.partiService.parte(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleTParti], { name: 'parti' })
  parti(): Promise<PenaleTParti[]> {
    return this.partiService.parti();
  }
}
