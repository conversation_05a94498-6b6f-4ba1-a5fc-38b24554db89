import {
  AdvancedConsoleLogger,
  LogLevel,
  Log<PERSON>essage,
  Logger,
  LoggerOptions,
} from 'typeorm';
import { QueryRunner } from 'typeorm/query-runner/QueryRunner';
import { Logger as LoggerNest } from '@nestjs/common';
export class NsCustomLoggerTypeorm
  extends AdvancedConsoleLogger
  implements Logger
{
  private logger = new LoggerNest(NsCustomLoggerTypeorm.name);
  constructor(options?: LoggerOptions) {
    super(options);
  }
  logQueryCache(query: string, queryRunner?: QueryRunner): void {
    this.logger.debug(`[CACHE HIT] ${query}`);
  }

  logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    const processedParams = this.stringifyParameters(parameters);
    this.logger.debug(`Query: ${query} -- Parameters: ${processedParams}`);
  }

  logQueryError(error: string, query: string, parameters?: any[], queryRunner?: QueryRunner) {
    const processedParams = this.stringifyParameters(parameters);
    this.logger.error(`Query Failed: ${query} -- Parameters: ${processedParams} -- Error: ${error}`);
  }

  logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner) {
    const processedParams = this.stringifyParameters(parameters);
    this.logger.warn(`Query is slow: ${query} -- Execution time: ${time}ms -- Parameters: ${processedParams}`);
  }

  logSchemaBuild(message: string, queryRunner?: QueryRunner) {
    this.logger.log(message);
  }

  logMigration(message: string, queryRunner?: QueryRunner) {
    this.logger.log(message);
  }

  log(level: 'log' | 'info' | 'warn', message: any, queryRunner?: QueryRunner) {
    if (level === 'warn') {
      this.logger.warn(message);
    } else {
      this.logger.log(message);
    }
  }

  private stringifyParameters(parameters?: any[]): string {
    if (!parameters?.length) return '[]';
    try {
      const processedParams = parameters.map(param => {
        if (param instanceof Buffer) {
          return '[Blob]';
        } else if (typeof param === 'string' && param.length > 120) {
          return param.substring(0, 100) + '...';
        } else {
          return param;
        }
      });
      return JSON.stringify(processedParams);
    } catch {
      return '[unserializable parameters]';
    }
  }
  /**
   * Write log to specific output.
   * Mantiene la compatibilità con il sistema esistente ma usa livelli appropriati
   */
  protected writeLog(
    level: LogLevel,
    logMessage: LogMessage | string | number | (LogMessage | string | number)[],
    queryRunner?: QueryRunner,
  ): void {
    const messages = this.prepareLogMessages(logMessage, {
      highlightSql: false,
    });

    for (const message of messages) {
      switch (message.type ?? level) {
        case 'log':
        case 'schema-build':
        case 'migration':
          this.logger.log(message.message);
          break;

        case 'info':
          if (message.prefix) {
            this.logger.log(`${message.prefix} ${message.message}`);
          } else {
            this.logger.log(message.message);
          }
          break;

        case 'query':
          // Le query ora vanno a livello DEBUG
          if (message.prefix) {
            this.logger.debug(`${message.prefix} ${message.message}`);
          } else {
            this.logger.debug(message.message);
          }
          break;

        case 'warn':
        case 'query-slow':
          if (message.prefix) {
            this.logger.warn(`${message.prefix} ${message.message}`);
          } else {
            this.logger.warn(message.message);
          }
          break;

        case 'error':
        case 'query-error':
          const stack = new Error().stack;
          if (message.prefix) {
            this.logger.error(
              `${message.prefix} ${message.message} \n ${stack}`,
            );
          } else {
            this.logger.error(`${message.message} \n ${stack}`);
          }
          break;
      }
    }
  }
}
