import { Inject, Logger } from '@nestjs/common';
import { Brackets, DataSource, ILike } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { TemplateTextAreaEntity } from '../consultazioni-graphql/entities/template_txt_area.entity';
import { TemplateInputDto } from './template-input.dto';
import { forEach } from 'jszip';

@UfficioService()
export class TemplateTextAreaService {
  private logger = new Logger(TemplateTextAreaService.name);
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  getTemplateByParam(search: TemplateInputDto) {
    this.logger.log(
      `Chiamata ai template con parametri:${JSON.stringify(search)}`,
    );
    const templateTextAreaEntitySelectQueryBuilder = this.connection
      .getRepository(TemplateTextAreaEntity)
      .createQueryBuilder('template')
      .where('1=1');
    if (search.title && search.title == '') {
      templateTextAreaEntitySelectQueryBuilder.andWhere({
        title: ILike(`%${search.title}%`),
      });
    }
    if (search.tipologia && !search.tipologiaList) {
      templateTextAreaEntitySelectQueryBuilder.andWhere({
        tipologia: ILike(search.tipologia),
      });
    } else if (search.tipologiaList && search.tipologiaList.length > 0) {
      const listaTipologie = search?.tipologiaList || [];
      templateTextAreaEntitySelectQueryBuilder.andWhere(
        new Brackets(qb => {
          qb.where('1=0');
          for (const tip of listaTipologie) {
            qb.orWhere({
              tipologia: ILike(tip),
            });
          }
        }),
      );
    }
    const templateTextAreaEntities = templateTextAreaEntitySelectQueryBuilder
      .orderBy({ ord: 'ASC' })
      .getMany();
    this.logger.log('Fine chiamata ai getTemplate');
    return templateTextAreaEntities;
  }

  async getAllTemplate() {
    this.logger.log('Chiamata a tutti i template');
    return this.connection.getRepository(TemplateTextAreaEntity).find();
  }
  async getAllTemplateById(idTempalte: string) {
    this.logger.log(`Chiamata al getTempalte byID:${idTempalte}`);
    return this.connection
      .getRepository(TemplateTextAreaEntity)
      .findOneBy({ idTemplate: idTempalte });
  }

  async insert(presidenteDto: TemplateInputDto) {
    this.logger.log('Inserimento del template');
    const tempalte = new TemplateTextAreaEntity({ ...presidenteDto });
    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(TemplateTextAreaEntity)
      .values(tempalte)
      .returning(['idTemplate'])
      .execute();
    const x = result.raw[0];
    this.logger.log('Fine inserimento del template');
    return this.getAllTemplateById(x[0]);
  }
}
