import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleCollegio } from './penale_t_collegio.model';
import { PenaleTRicudien } from './penale_t_ricudien.model';
import { PenaleProvvedimenti } from './penale_provvedimenti.model';

@ObjectType()
export class PenaleTUdienza {
  @Field(type => ID)
  idUdien: number;

  @Field(type => Date)
  dataUdienza: Date;
  tipoUdienza: PenaleParam;
  sezione: PenaleParam;
  ricorsiUdienza?: PenaleTRicudien[];
  collegio?: PenaleCollegio[];

  @Field(type => Int)
  idTipoDiUdienza: number;

  @Field(type => Int)
  idFunzionario: number;
  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  operatore: number;

  aula?: PenaleParam;

  @Field(type => String)
  notePg?: string;
  @Field(type => Date)
  inizioUdienza?: Date;
  @Field(type => Date)
  fineUdienza?: Date;

  termineDeposito?: Date;
  termineDepositoCalendar?: Date;
  esitoParziale?: boolean;
  isEstensore?: boolean;
  allPubblicate?: boolean;

  provvedimentiByNrg?: PenaleProvvedimenti[];

  public constructor(init?: Partial<PenaleTUdienza>) {
    Object.assign(this, init);
  }
}
