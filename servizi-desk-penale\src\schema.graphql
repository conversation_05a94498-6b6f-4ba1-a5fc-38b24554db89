# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type AggregatePageInfo {
  count: Float!
  total: Float!
  totalElement: Float!
}

type AggregatePenaleNotifiche {
  count: Float!
  total: Float!
  totalElement: Float
  unread: Float
}

input ArgsDepositoProvvedimentoInput {
  anRuolo: Float
  firmato: String
  numRuolo: Float
  pin: String
  tipologiaProvvedimento: ProvvedimentiTipoEnum!
}

type CivileAnagdifen {
  codiceAvvocato: String
  codiceFiscale: String
  codiceFiscalePrecedente: String
  codiceForo: Int
  cognome: String
  dUff: Int
  dataCassazionista: DateTime
  dataNascita: DateTime
  deceduto: Int
  dtAvv: DateTime
  dtProc: DateTime
  email: String
  fax: String
  foroProvincia: Int
  idAuloRif: Int
  idParam: ID!
  luogoNascita: String
  nazionalita: String
  nome: String
  provicniaNascita: String
}

type CodeDeposito {
  cf: String!
  codeDepositoDto: CodeDepositoDto
  idProvv: String!
  ruolo: Role
}

type CodeDepositoDto {
  dataUdienza: DateTime
  nrg: String
  numOrdine: Float
  tipoProvvedimento: String
  udienza: String
}

input CodeDepositoInput {
  """codice fiscale utente"""
  cf: String!

  """id provvedimento"""
  idProvv: String!
}

input CreateNotificheInput {
  descrizione: String!
  idUdien: Int!
  nrg: Int!
  tipo: String
}

input CreateProvvLavorazioneInput {
  allegatoOscurato: Boolean = false
  argsProvvedimento: ArgsDepositoProvvedimentoInput!
  idUdienza: Int!
  nrg: Int!
  origine: ProvvedimentiOrigineEnum!
}

input CreateProvvedimentiChangeStatusInput {
  idAutore: Int!
  idProvvedimento: String
  prevStato: ProvvedimentiStatoEnum
  stato: ProvvedimentiStatoEnum!
}

input CreateProvvedimentiInput {
  addCodeFirma: Boolean = false
  dataDeposito: DateTime!
  idUdienza: Int!
  nomeDocumento: String!
  nrg: Int!
  origine: ProvvedimentiOrigineEnum
  stato: ProvvedimentiStatoEnum
  tipo: ProvvedimentiTipoEnum!
}

input CreateProvvedimentiNoteInput {
  idAutore: Int!
  idProvvedimento: String!
  note: String!
}

input CredenzialiFirmaRemotaInput {
  passwordFirma: String
  pinFirma: String
  usernameFirma: String
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input DatiBustaInput {
  codiceFiscaleMittente: String
  codiceUfficioDestinatario: String
  idMsg: String
  ruoloMittente: String
}

"""A simple UUID parser"""
scalar Decimal

type EsitoParzialeGraph {
  descrizione: String
  motivo: String
}

type EsitoParzialeModel {
  esitoParziale: Boolean
  motivo: String
  tipoDiEsito: String
}

input FirmaProvvLavorazioneInput {
  bustaMakerData: DatiBustaInput
  codiceUfficio: String
  credenzialiFirmaRemota: CredenzialiFirmaRemotaInput
  firmato: Boolean!
  generazioneDatiAtto: GenerazioneDatiAttoInput
  idProvvedimento: String!
  nrg: Int!
  tipologiaProvvedimento: ProvvedimentiTipoEnum
}

input GenerazioneDatiAttoInput {
  allegatoOscurato: String = "false"
  annoFascicolo: Int
  numeroFascicolo: Int
  tipoProvvedimento: String
}

type InfoDettaglioFascicoloModel {
  totalCount: Float!
  valorePonderaleTotale: Float!
}

type InfoProvvedimento {
  dataMinuta: DateTime
  dataPubblicazione: DateTime
  idUdienza: Float
  isPrincipalRicorsoRiunito: Boolean
  nrg: Float
  numRaccoltaGenerale: Float
  numRaccoltaGeneraleString: String
  ricorsoRiunito: RiunitoDto
  statoProvvedimento: ProvvedimentiStatoEnum
}

type Mutation {
  GenerazioneProvvedimentoCreateMutation(createProvvLavorazioneInput: CreateProvvLavorazioneInput!): PenaleProvvedimenti!
  creaCodeDeposito(codaDeposito: CodeDepositoInput!): CodeDeposito!
  creaNotifica(notifica: CreateNotificheInput!): PenaleNotifiche!
  creaSettings(settings: SettingsInput!): Settings!
  createProvvedimento(provvedimento: CreateProvvedimentiInput!): PenaleProvvedimenti!
  createProvvedimentoChangeStatus(changeStatus: CreateProvvedimentiChangeStatusInput!): PenaleProvvChangeStatus!
  createProvvedimentoNote(provvedimento: CreateProvvedimentiNoteInput!): PenaleProvvedimentiNote!
  deleteSetting: Boolean!
  eliminaCodeDeposito(idProvv: String!): Boolean!
  firmaEDeposita(firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput!): PenaleProvvedimenti!
  unNotificaToRead(id: String!): PenaleNotifiche!
  updateReadAllNotify: Boolean!
  updateReadNotifyList(segnaLette: NotificheToReadInput!): Boolean!
  updateSetting(settings: SettingsInput!): Settings!
}

input NotificheToReadInput {
  ids: [String!]!
}

type PageInfo {
  endCursor: String
  hasNextPage: Boolean
  hasPreviousPage: Boolean
  startCursor: String
}

type PenalRicorsoUdienzaEdge {
  """Used in `before` and `after` args"""
  cursor: String!

  """The target node"""
  node: PenaleTRicudien!
}

type PenaleAnagmagis {
  codiceFiscale: String
  cognome: String
  dataNascita: DateTime
  idAnagmagis: ID!
  idFunzione: Int
  nome: String
  oggi: DateTime
  operatore: Int
}

type PenaleAnagmagisDetails {
  codiceFiscale: String
  cognome: String
  dataNascita: DateTime
  idAnagmagis: ID!
  idFunzione: Int
  nome: String
  oggi: DateTime
  operatore: Int
}

type PenaleAnagraficaParti {
  codiceFiscale: String
  codiceUnivoco: String
  cognome: String!
  dataNascita: DateTime
  glbTime: Int
  idAnagParte: ID!
  idFunzione: Int
  luogoNascita: String
  nazionalita: Int
  nome: String
  oggi: DateTime
  operatore: Int
  provinciaNascita: String
}

type PenaleColleggioDetails {
  colleggioMagistrati: [PenaleCollegioDetailsModel!]
  relatore: PenaleTMagisDeatils
}

type PenaleCollegio {
  glbDtime: Int
  gradoMag: Int!
  idFunzione: ID!
  idMagis: Int!
  idMagiscolle: Int!
  idUdienza: Int!
  inUdienza: Int
  isEstensore: Boolean
  isRelatore: Boolean
  magistrato: PenaleTMagis!
  oggi: DateTime!
  operatore: Int!
  tipoMag: String!
}

type PenaleCollegioDetailsModel {
  glbDtime: Int
  gradoMag: Int!
  idFunzione: ID!
  idMagis: Int!
  idMagiscolle: Int!
  idUdienza: Int!
  inUdienza: Int
  isEstensore: Boolean
  isRelatore: Boolean
  magistrato: PenaleTMagisDeatils
  oggi: DateTime!
  operatore: Int!
  tipoMag: String!
}

type PenaleDifenparti {
  avviso: String
  cap: String
  comune: String
  dataAnomina: DateTime
  dataRevoca: DateTime
  dataRinunzia: DateTime
  deceduto: Int
  difensoreAnagrafica: CivileAnagdifen
  esito: String
  gruppo: Int
  idAnagraficaDifensore: Int!
  idDifensoriParti: ID!
  idFunzione: Int!
  idParti: Int!
  indirizzo: String
  merito: String
  nrg: Int!
  oggi: DateTime
  operatore: Int!
  parte: PenaleTParti
  provincia: String
  tipoDifensore: PenaleParam
}

type PenaleListPartiEControParteModel {
  controParte: [PenaleTParti!]!
  parte: PenaleTParti!
}

type PenaleListPartiModel {
  controParteList: [PenaleListPartiEControParteModel!]!
  parte: [PenaleTParti!]!
}

type PenaleNotifiche {
  annoFascicolo: Float
  coleggio: PenaleParam
  dataCreazione: DateTime
  dataUdienza: DateTime
  descrizione: String
  fineUdienza: DateTime
  idNotifica: ID!
  idUdienza: Int!
  idUtente: Int!
  inizioUdienza: DateTime
  isEstensore: Boolean
  isPrincipale: Boolean
  nrg: Int!
  numeroFascicolo: Float
  read: Boolean
  sezione: PenaleParam
  tipo: String
  tipoProvvedimento: ProvvedimentiTipoEnum
  tipoUdienza: PenaleParam
}

type PenaleNotificheConnection {
  aggregate: AggregatePenaleNotifiche!
  edges: [PenaleNotificheEdge!]!
  pageInfo: PageInfo!
}

type PenaleNotificheEdge {
  """Used in `before` and `after` args"""
  cursor: String!

  """The target node"""
  node: PenaleNotifiche!
}

type PenaleParam {
  descrizione: String!
  idParam: ID!
  sigla: String!
}

type PenaleParteLegate {
  anagraficaParte: PenaleAnagraficaParti
  idAnagraficaParte: ID!
  idFunzione: Int!
  idParte: ID!
  operatore: Int!
  tipoLegame: PenaleParam
}

type PenaleProfilo {
  dataFine: DateTime
  dataInizio: DateTime
  descrizioneProfilo: String
  idProfilo: ID!
  inUsoA: String
  operatore: Int
}

type PenaleProvvChangeStatus {
  dateChange: DateTime!
  idAutore: Int!
  idProvvedimento: String!
  idProvvedimentoChangeStatus: ID!
  isRevisione: Boolean
  prevStato: String
  stato: String!
}

type PenaleProvvLavFile {
  createAt: DateTime!
  idCategoria: ID!
  idProvvedimento: String!
  mimeType: String!
  nomeFile: String!
  oscurato: Boolean!
  signed: Boolean!
  tipoFile: String!
}

type PenaleProvvedimenti {
  autore: PenaleTUtente
  changeStatus: PenaleProvvChangeStatus
  checkDownloadAndSign: Boolean
  dataDecisione: DateTime!
  dataDeposito: DateTime
  dataUltimaModifica: DateTime!
  disabledButton: Boolean
  disabledCreaNuovoProv: Boolean
  disabledModificaOrDuplica: Boolean
  enabledRichiestaDiModificaEVerificato: Boolean
  fkIdCat: String
  hasDuplicato: Boolean
  hasNote: Boolean
  idAutore: Int!
  idProvvedimento: ID!
  idUdienza: Int!
  isDuplicato: Boolean
  isOscurato: Boolean!
  isRevisione: Boolean
  lastModified: Boolean!
  listaFile: [PenaleProvvLavFile!]
  nomeDocumento: String
  note: [PenaleProvvedimentiNote!]
  nrg: Int!
  origine: String
  provvedimentoLock: Boolean!
  stato: String
  tipo: String
}

type PenaleProvvedimentiNote {
  autore: PenaleTUtente
  dataInserimento: DateTime!
  idAutore: Int!
  idProvvNote: ID!
  idProvvedimento: String!
  note: String!
  statoInserimento: StatoInserimentoNoteEnum
}

type PenaleReatiRicorso {
  dataA: DateTime
  dataDa: DateTime
  glbDtime: Int
  idFunzione: Int!
  idReatiRicorsi: ID!
  idReato: Int
  istProc: Int
  note: String
  nrg: Int!
  oggi: DateTime!
  operatore: Int!
  principale: Boolean!
  reato: PenaleTReati
  ricorso: PenaleTRicorso
  tipoD: PenaleParam
  tipoData: String
}

type PenaleRicorsoUdienzaConnection {
  aggregate: AggregatePageInfo!
  edges: [PenalRicorsoUdienzaEdge!]!
  pageInfo: PageInfo!
}

type PenaleTEsito {
  art28: String!
  art94: String!
  esitoSent: PenaleTEsitoSent
  idEsito: Int!
  idFunzione: ID!
  idReatoParte: String!
  idRicUdienza: Int!
  motivoSosp: String!
  note: String!
  operatore: Int!
  privacy: String!
  privacyParam: PenaleParam
  rinvioDescrizione: String!
  riunito: Boolean!
  semplificata: String!
}

type PenaleTEsitoSent {
  art28: String
  art94: String
  idEsito: Int
  idEsito1: Int
  idEsito2: Int
  idEsito3: Int
  idFunzione: ID!
  idSent: Int
  operatore: Int
  sentenza: PenaleTSentenza
}

type PenaleTEstensoriSentenza {
  estensore: PenaleTMagis
  id: Int!
  idSentenza: Int
  sentenza: PenaleTSentenza
}

type PenaleTMagis {
  anagraficaMagistrato: PenaleAnagmagis
  codMag: Int
  dataFine: DateTime
  dataInizio: DateTime!
  glbDtime: Int
  grado: Int!
  idAnagmagis: Int
  idFunzione: Int
  idMagis: ID!
  oggi: DateTime
  operatore: Int
  tipoMag: PenaleParam
  ufficio: Int!
}

type PenaleTMagisDeatils {
  anagraficaMagistrato: PenaleAnagmagisDetails
  codMag: Int
  dataFine: DateTime
  dataInizio: DateTime!
  glbDtime: Int
  grado: Int!
  idAnagmagis: Int
  idFunzione: Int
  idMagis: ID!
  oggi: DateTime
  operatore: Int
  tipoMag: PenaleParam
  ufficio: Int!
}

type PenaleTParti {
  altri: String
  anagraficaParte: PenaleAnagraficaParti
  art159: String
  art161: String
  art165: String
  dataDecor: DateTime
  dataScarcerazione: DateTime
  datarresto: DateTime
  difensori: [PenaleDifenparti!]!
  displayParti: String
  dtinizStato: DateTime!
  glbDtime: Int
  idAnagraficaParte: Int
  idFunzione: Int!
  idParamfig: Int
  idParte: ID!
  idTipoFig: Int!
  nrg: Int!
  numOrdine: Int
  oggi: DateTime!
  operatore: Int!
  parteLegata: PenaleParteLegate
  penaSup5: String!
  privacy: Int
  ricorrente: Boolean!
  secretata: String
  statoParte: String
  stralcio: String
  tipoFig: PenaleParam
  tipoLegame: Int
  uffins: Int
}

type PenaleTProvved {
  dataProvv: DateTime!
  gradoProvv: PenaleParam!
  idProvvedimento: ID!
  impugnato: Boolean!
  nrg: Int!
  numProvv: Int!
  oggi: DateTime!
  operatore: Int!
  tipoProvv: PenaleParam!
}

type PenaleTReati {
  aggrava: String!
  altroIdentificativo: Int
  anno: Int!
  art: Int
  capo: Int!
  comma: Int!
  dataFineUtilizzo: DateTime
  descrizione: String
  displayReati: String
  eppo: String
  fonteNorm: String!
  glbDtime: Int
  gruppo: Int
  idFunzione: Int!
  idReato: ID
  idVoce: Int!
  lettera: String!
  libro: Int!
  numeroLegale: Int!
  oggi: DateTime!
  operatore: Int!
  paragrafo: Int!
  privacy: Int
  provenienza: String
  titolo: Int!
  valido: String
}

type PenaleTRicorso {
  anno: Int!
  codinam1: Int
  codinam2: Int
  codinam3: Int
  confisca: String!
  dataDecorsoImp: DateTime
  dataIscrizione: DateTime!
  dataPassa: DateTime
  dataPassaFto: DateTime
  dataPassaInc: DateTime
  detParti: String
  doveFto: PenaleParam
  doveSta: PenaleParam
  flagSitmp: String!
  glbDtime: Int
  idAuloInc: Int
  idDoveinc: Int
  idFunzionario: Int!
  idMagisinam: Int
  idRinvio: Int
  idSezerr: Int
  listaParti: PenaleListPartiModel
  mafia: String!
  note: String
  nrg: ID!
  nrgPrinc: Int
  nrgReale: Int!
  numParti: Int
  numero: Int!
  oggi: DateTime!
  operatore: Int!
  parti: [PenaleTParti!]
  privacy: Int
  provvedimento(idUdien: Float!, nrg: Float!): PenaleProvvedimenti
  provvedimentoImpugnato: [String!]
  reatiRicorso: [PenaleReatiRicorso!]
  sezione: PenaleParam
  spoglio: PenaleTSpoglio
  statoRicorso: String
  tipoRicorso: PenaleParam
  tipoRicorsoOld: String
  udienza(idUdien: Float!, nrg: Float!): PenaleTUdienza!
  urgente: String
  vecchioRito: String!
}

type PenaleTRicudien {
  anno: Int
  art161: String!
  art169: String!
  checkStatoOnSIC: InfoProvvedimento
  esito: PenaleTEsito
  esitoFascicolo: PenaleParam!
  esitoParziale: EsitoParzialeModel
  estensore: PenaleTMagis
  faxEsito: String!
  glbDTime: Int!
  idConcl1: Int!
  idConcl2: Int!
  idConcl3: Int!
  idEsito: Int!
  idFunzione: Int!
  idMagiscolle: Int!
  idMotivorinv: Int!
  idRelatore: Int!
  idRicudien: ID!
  idSosp: Int!
  idUdienza: Int!
  importante: String!
  isEstensore: Boolean
  isPresidente: Boolean
  isRelatore: Boolean
  notiPres: String!
  nrg: Int!
  numOrdine: Int!
  numero: Int
  oggi: DateTime!
  operatore: Int!
  oscuramentoDeskCsp: Boolean
  oscuratoSicComplessivo: Boolean
  oscuratoSicSingle: Boolean
  principale: Int
  provvedimento: PenaleProvvedimenti
  relatore: PenaleTMagis
  ricorso: PenaleTRicorso
  statoProvvedimento: ProvvedimentiStatoEnum
  tipologia: String
  urgente: String!
  valPondComplessivo: Decimal
}

type PenaleTSentenza {
  dataAsse: DateTime!
  dataMass: DateTime!
  dataMinuta: DateTime!
  dataPubblicazione: DateTime!
  dataRest: DateTime!
  dataRestPr: DateTime!
  idEstentore: Int!
  idFunzione: Int!
  idMagis: Int!
  idPqm: Int!
  idSent: Int!
  idTipoSent: PenaleParam
  idTipoUdienza: Int!
  nraccg: Int!
  numMass: Int!
  operatore: Int!
  quanti: Int!
  quantiCon: Int!
  quantiConPr: Int!
  quantiPr: Int!
  sentenza: Int!
}

type PenaleTSpoglio {
  art12: String
  art127: String!
  art438: String!
  art444: String!
  art611: String!
  codiceConf: Int
  cognomePmcc: String
  cognomePmce: String
  commento: String
  dataDecreto: DateTime
  dataDecterm: DateTime
  dataPassaPg: DateTime
  dataPrescriz2: DateTime
  dataPrescrizione: DateTime
  dataSosp: DateTime
  deplano: String
  glbDtime: Int
  idAuloPmcc: Int
  idAuloPmce: Int
  idFunzione: Int
  idMotivo: Int
  idSpogliatore: Int
  idSpoglio: ID!
  idTipoudPrev: Int!
  mae: String
  modello: Int
  nomePmcc: String
  nomePmce: String
  note: String
  nrg: Int!
  oggi: DateTime
  operatore: Int
  pgCustodia: Int
  pgSuper: Int
  presidi: String
  privacy: Int
  urgentePg: String
  valPond: Decimal
  vecchiRito: String
}

type PenaleTUdienza {
  allPubblicate: Boolean
  aula: PenaleParam
  checkStatoOnSIC(idUdien: Float!, nrg: Float!): InfoProvvedimento
  collegio: [PenaleCollegio!]!
  dataUdienza: DateTime!
  esitoParziale: Boolean
  fineUdienza: DateTime
  idFunzionario: Int!
  idTipoDiUdienza: Int!
  idUdien: ID!
  inizioUdienza: DateTime
  isEstensore: Boolean
  notePg: String
  oggi: DateTime!
  operatore: Int!
  provvedimentiByNrg(idUdien: Float!, nrg: Float!): [PenaleProvvedimenti!]
  provvedimentoByNrgPerPresidente(idUdien: Float!, nrg: Float!): [PenaleProvvedimenti!]
  ricorsiUdienza(nrg: Float!): [PenaleTRicudien!]
  sezione: PenaleParam!
  termineDeposito: DateTime!
  termineDepositoCalendar: DateTime
  tipoUdienza: PenaleParam!
}

type PenaleTUdienzaEntityFake {
  aula: String!
  dataDeposito: DateTime!
  dataScadenza: DateTime!
  dataUdienza: DateTime!
  idProvvvedimento: String!
  idTipoDiUdienza: Int!
  idUdien: ID!
  nrg: Int!
  nrgReale: Int!
  numero: Int!
  numeroChiamata: Int!
  sezione: String!
  statoProvvedimento: String!
  statoProvvedimentoLavorazione: String!
}

type PenaleTUtente {
  codiceFiscale: String
  cognome: String!
  idProfilo: Int
  idUtente: ID!
  identificativo: String!
  nome: String
  operatore: Int!
  profilo: PenaleProfilo
  tipoUtente: String
  tipologia: PenaleParam
  uffAppartenenza: PenaleParam
  ufficio: PenaleParam
}

type PenaleUdienza {
  aula: String
  checkStatoOnSIC(idUdien: Float!, nrg: Float!): InfoProvvedimento
  dataUdienza: DateTime!
  fineUdienza: DateTime
  idFunzione: Int!
  idUdienza: ID!
  inizioUdienza: DateTime
  notePg: String
  oggi: DateTime!
  operatore: Int!
  sezione: String!
  tipoUdienza: String!
}

type ProvvedimentiDtoConnection {
  aggregate: AggregatePageInfo!
  edges: [ProvvedimentiDtoEdge!]!
  pageInfo: PageInfo!
}

type ProvvedimentiDtoEdge {
  """Used in `before` and `after` args"""
  cursor: String!

  """The target node"""
  node: ScrivaniaProvvedimentiModel!
}

enum ProvvedimentiOrigineEnum {
  LOCALE
  SYSTEM
}

enum ProvvedimentiStatoEnum {
  BOZZA_PRESIDENTE
  BUSTA_RIFIUTATA
  BUSTA_RIFIUTATA_AL_PRESIDENTE
  CODA_DI_FIRMA
  DA_REDIGERE
  ERRORE_DI_PUBBLICAZIONE
  INVIATO_IN_CANCELLERIA_RELATORE
  INVIATO_IN_CANCEL_PRESIDENTE
  IN_BOZZA
  IN_CODE_FIRMA_REL
  IN_RELAZIONE_ESTENSORE
  MINUTA_ACCETTATA
  MINUTA_DA_MODIFICARE
  MINUTA_DEPOSITATA_SIC
  MINUTA_IN_REVISIONE
  MINUTA_MODIFICATA
  MINUTA_MODIFICATA_PRESIDENTE
  PROVV_DEPOSITATO_SIC
  PUBBLICATA
  PUBBLICATO_SIC
  RIUNITO
}

enum ProvvedimentiTipoEnum {
  MINUTA_ORDINANZA
  MINUTA_SENTENZA
  ORDINANZA
  SENTENZA
}

type Query {
  PenaleTEstensoriSentenza: [PenaleTEstensoriSentenza!]!
  anagraficaDifensore(id: Float!): CivileAnagdifen!
  anagraficaMagistrati: [PenaleAnagmagis!]!
  anagraficaMagistrato(id: Float!): PenaleAnagmagis!
  anagraficaParte(id: Float!): PenaleAnagraficaParti!
  anagraficaParti: [PenaleAnagraficaParti!]!
  appVersions: Version!
  checkStatoOnSICQuery(idProvv: String!): InfoProvvedimento!
  codeDeposito: [CodeDeposito!]!
  codeDepositoByCf(ruolo: String!): [CodeDeposito!]!
  codeDepositoByIdProvv(idProvv: String!): CodeDeposito!
  codeDepositoByIdProvvCf(idProvv: String!): CodeDeposito!
  colleggioDetails(id: Float!, nrg: Float!): PenaleColleggioDetails!
  colleggioDetailsByOrdine(id: Float!, ordine: Float!): PenaleColleggioDetails!
  collegi: [PenaleCollegio!]!
  collegio(id: Float!): PenaleCollegio!
  collegioByNrgAndIdUdienza(id: Float!, nrg: Float!): [PenaleCollegio!]!
  countNotificheNotRead: Int!
  difensoreParti(id: Float!): PenaleDifenparti!
  difensoriParti: [PenaleDifenparti!]!
  esitoParziale(ricorsoUdienza: Float!): EsitoParzialeGraph!
  getFascitoloDetExtraInfo(idUdien: Float!): InfoDettaglioFascicoloModel!
  getProvvLavorazione(nrg: Float!): PenaleProvvedimenti!
  getProvvLavorazioneByIdProvv(idProvv: String!): PenaleProvvedimenti!
  getRiuntiByIdRicUdien(idRicUdien: Float!): [RiunitoDetails!]!
  impostazioniByCf: Settings!
  magistrati: [PenaleTMagis!]!
  magistrato(id: Float!): PenaleTMagis!
  notifica(id: String!): PenaleNotifiche!
  notifiche(after: String, before: String, first: Int, last: Int, read: Boolean, term: String): PenaleNotificheConnection!
  notificheByCurrentUser(after: String, before: String, first: Int, last: Int, read: Boolean, term: String): PenaleNotificheConnection!
  notificheByCurrentUserPopover(after: String, before: String, first: Int, last: Int, read: Boolean, term: String): PenaleNotificheConnection!
  parte(id: Float!): PenaleTParti!
  parteLegate(id: Float!): PenaleParteLegate!
  parti: [PenaleTParti!]!
  partiLegate: [PenaleParteLegate!]!
  penaleUdienzaByIdUdienza(idUdienza: Float!): PenaleUdienza!
  provvedimenti: [PenaleProvvedimenti!]!
  provvedimentiByIds(ids: [String!]!): [PenaleProvvedimenti!]!
  provvedimentiImpugnati: [PenaleTProvved!]!
  provvedimentiNote: [PenaleProvvedimentiNote!]!
  provvedimentiScrivania(after: String, before: String, first: Int, idUdienza: Float!, last: Int, status: ProvvedimentiStatoEnum): ProvvedimentiDtoConnection!
  provvedimento(id: String!): PenaleProvvedimenti!
  provvedimentoByIdUdien(id: Float!): [PenaleProvvedimenti!]!
  provvedimentoByIdUdienzaAndOrdine(id: Float!, ordine: Float!): [PenaleProvvedimenti!]!
  provvedimentoByNrg(nrg: Float): [PenaleProvvedimenti!]!
  provvedimentoByNrgPerPresidente(nrg: Float!): [PenaleProvvedimenti!]!
  provvedimentoChangeStatus: [PenaleProvvChangeStatus!]!
  provvedimentoChangeStatusById(id: String!): PenaleProvvChangeStatus!
  provvedimentoChangeStatusByIdProvvedimento(id: String!, roles: String!): [PenaleProvvChangeStatus!]!
  provvedimentoImpugnato(id: Float!): PenaleTProvved!
  provvedimentoNote(id: String!): [PenaleProvvedimentiNote!]!
  provvedimentoTrackingByIdProvvedimento(id: String!, roles: String!): [PenaleProvvChangeStatus!]!
  reati: [PenaleTReati!]!
  reatiRicorso: [PenaleReatiRicorso!]!
  reato(id: Float!): PenaleTReati!
  reatoRicorso(id: Float!): PenaleReatiRicorso!
  ricorsi: [PenaleTRicorso!]!
  ricorsiUdienza(after: String, before: String, first: Int, idUdien: Float!, last: Int, status: ProvvedimentiStatoEnum): PenaleRicorsoUdienzaConnection!
  ricorso(id: Float!): PenaleTRicorso!
  ricorsoByIdUdienAndNrg(idUdien: Float!, nrg: Float!): PenaleTRicorso!
  ricorsoUdienza(id: Float!): PenaleTRicudien!
  ricorsoUdienzaInRicUdien(idRicUdienList: [Int!]!): [PenaleTRicudien!]!
  spogli: [PenaleTSpoglio!]!
  spoglio(id: Float!): PenaleTSpoglio!
  termineDepositoCalendar(endDate: DateTime!, startDate: DateTime!): [PenaleTUdienzaEntityFake!]!
  test: ScrivaniaProvvedimentiModel
  udienza(id: Float!): PenaleTUdienza!
  udienzaPerPresidente(id: Float!): PenaleTUdienza!
  udienze: [PenaleTUdienza!]!
  udienzePerEstensore: [PenaleTUdienza!]!
  udienzeWithProvvedimentoDet(idUdien: Float!, nrg: Float!): PenaleTUdienza!
  utenteById(id: Float!): PenaleTUtente!
  utenti: [PenaleTUtente!]!
  utentiByCf(cf: String!): PenaleTUtente!
}

type RiunitoDetails {
  anno: Int
  idRicorsoUdienza: Int!
  nrg: Int!
  numero: Int
}

type RiunitoDto {
  anno: Float
  numero: Float
}

enum Role {
  ESTENSORE
  PRESIDENTE
  RELATORE
}

type ScrivaniaProvvedimentiModel {
  dataDeposito: DateTime
  idProvvedimento: String
  idRicUdien: Int!
  modificaPresidente: Boolean
  nrg: Int
  nrgFormat: String!
  numOrdine: Int
  oscuratoDESKCSP: String!
  oscuratoSIC: String!
  relatore: String!
  riunito: InfoProvvedimento
  stato: ProvvedimentiStatoEnum!
  tipologia: String
}

type Settings {
  cfUtente: String
  passwordFirma: String
  usernameFirma: String
}

input SettingsInput {
  """Codice fiscale utente"""
  cfUtente: String!

  """Password firma remota"""
  passwordFirma: String!

  """Username firma remota"""
  usernameFirma: String!
}

enum StatoInserimentoNoteEnum {
  MINUTA_ACCETTATA
}

type Version {
  appVersion: String!
  serviziDepositoVersion: String!
}