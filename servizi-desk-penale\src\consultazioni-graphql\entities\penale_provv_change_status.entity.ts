import { BeforeInsert, Column, Entity, PrimaryColumn } from 'typeorm';
import { ProvvedimentiStatoEnum } from './enumaration/provvedimenti-stato.enum';
import { v4 as uuid4 } from 'uuid';

@Entity('PENALE_PROVV_CHANGE_STATUS') //nome tabella su schema oracle
export class PenaleProvvChangeStatusEntity {
  @PrimaryColumn({
    name: 'ID_PROVV_CHANGE_STATUS',
    default: 'uuid_generate_v4()',
  })
  idProvvedimentoChangeStatus: string;

  @Column({ name: 'ID_PROVV' })
  idProvvedimento: string;

  @Column({ name: 'STATO' })
  stato: ProvvedimentiStatoEnum;
  @Column({ name: 'PREV_STATO' })
  prevStato: ProvvedimentiStatoEnum;
  @Column({ name: 'DATE_CHANGE' })
  dateChange: Date;

  @Column({ name: 'ID_AUTORE' })
  idAutore: number;

  isRevisione: boolean;
  public constructor(init?: Partial<PenaleProvvChangeStatusEntity>) {
    Object.assign(this, init);
  }
  @BeforeInsert()
  generateUuid() {
    this.idProvvedimentoChangeStatus = uuid4().replace(/-/g, '');
  }
}
