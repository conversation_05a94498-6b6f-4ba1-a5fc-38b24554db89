import { InternalServerErrorException } from '@nestjs/common';
import {
  HttpException,
  HttpExceptionOptions,
} from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class FascicoloNotFoundException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
  ) {
    super(
      response,
      CodeErrorEnumException.FASCICOLO_NOT_FOUND,
      NSTypeErrorEnum.ERROR,
      options,
    );
  }
}
