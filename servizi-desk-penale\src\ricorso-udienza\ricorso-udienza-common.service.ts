import {
  InfoProvvedimento,
  RiunitoDto,
} from '../consultazioni-graphql/models/dto/info_provvedimento.model';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { Utils } from '../utils/utils';
import { PenaleUdienzaService } from '../penale-udienza/penale-udienza.service';
import { RicercaSentenzaService } from '../ricerca-sentenza/ricerca-sentenza.service';
import { TRicorsoUdienzaService } from './t-ricorso-udienza.service';
import { PenaleTRicudien } from '../consultazioni-graphql/models/penale_t_ricudien.model';
import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { TRicorsoService } from '../fascicolo/t-ricorso.service';
import { Logger } from '@nestjs/common';

@UfficioService()
export class RicorsoUdienzaCommonService {
  private logger = new Logger(RicorsoUdienzaCommonService.name);
  constructor(
    private readonly penaleUdienzaService: PenaleUdienzaService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly ricorsoService: TRicorsoService,
  ) {}
  async checkStatoOnSicCommon(idUdien: number, nrg: number | null) {
    if (nrg) {
      const ricorsoUdienza =
        await this.ricorsoUdienzaService.ricorsoUdienzaByNrgAndIdUdienza(
          idUdien,
          nrg,
        );

      const newVar = await this.checkStatoOnSicCommonByIdRicUdien(
        ricorsoUdienza,
      );
      return newVar;
    }
    return null;
  }

  async checkStatoOnSicCommonByIdRicUdien(
    ricorsoUdienza: PenaleTRicudien | null,
  ) {
    if (ricorsoUdienza) {
      // return await this.connection.manager.transaction( async transactionalEntityManager => {
      const infoProvv = new InfoProvvedimento();

      infoProvv.idUdienza = ricorsoUdienza.idUdienza;
      infoProvv.nrg = ricorsoUdienza.nrg;
      if (!ricorsoUdienza.principale) {
        this.logger.log(
          `Eseguo la query sulla penale sentenza vista. idRicUdien:${ricorsoUdienza?.idRicudien}`,
        );
        const sentenzaEntity =
          await this.ricercaSentenzaService.sentenzaByIdRicUdien(
            ricorsoUdienza.idRicudien,
          );

        if (
          sentenzaEntity?.riunitiView?.riunito &&
          Boolean(Number(sentenzaEntity?.riunitiView?.riunito))
        ) {
          this.logger.log('Entro nel blocco del riunito');
          infoProvv.statoProvvedimento = ProvvedimentiStatoEnum.RIUNITO;

          const [anno, numero] = Utils.calcoloNumeroFascicolo(
            sentenzaEntity?.riunitiView?.nrgRealePadre + '',
          );
          const ricorsoRiunito = new RiunitoDto();
          if (anno && numero) {
            ricorsoRiunito.anno = anno;
            ricorsoRiunito.numero = numero;
          }
          infoProvv.ricorsoRiunito = ricorsoRiunito;

          this.logger.log(
            `fine nel blocco del riunito: ${JSON.stringify(infoProvv)}`,
          );
        } else {
          // se arrivo in questo punto significa che nn si tratta di un ricorso riuniuto ma potrebbe essere il pricipale
          infoProvv.isPrincipalRicorsoRiunito =
            sentenzaEntity?.riunitiView?.isPrincipale || false;
        }
        //FIXME inizio da eliminare  una volta che facciamo la fase 2 dei riuniti
        /*   if (infoProvv.isPrincipalRicorsoRiunito) {
          infoProvv.statoProvvedimento =
            ProvvedimentiStatoEnum.RIUNITO_CARTACEO;
        }*/
        // fine eliminazione fixme
        if (
          sentenzaEntity?.dataPubblicazione != null &&
          !sentenzaEntity?.pubblicatoTelematico &&
          sentenzaEntity.numeroRaccoltaGenerale != null
        ) {
          infoProvv.statoProvvedimento = ProvvedimentiStatoEnum.PUBBLICATO_SIC;
        } else if (
          sentenzaEntity?.dataPubblicazione != null &&
          !sentenzaEntity?.pubblicatoTelematico
        ) {
          infoProvv.statoProvvedimento =
            ProvvedimentiStatoEnum.PROVV_DEPOSITATO_SIC;
        } else if (
          sentenzaEntity?.dataMinuta != null &&
          !sentenzaEntity?.depositoTelematico
        ) {
          infoProvv.statoProvvedimento =
            ProvvedimentiStatoEnum.MINUTA_DEPOSITATA_SIC;
        } else {
          infoProvv.statoProvvedimento = ricorsoUdienza.statoProvvedimento;
          if (
            sentenzaEntity?.riunitiView?.riunito &&
            Boolean(Number(sentenzaEntity?.riunitiView?.riunito))
          ) {
            infoProvv.statoProvvedimento = ProvvedimentiStatoEnum.RIUNITO;
          }
        }
        infoProvv.dataPubblicazione = sentenzaEntity?.dataPubblicazione;
        infoProvv.dataMinuta = sentenzaEntity?.dataMinuta;
        infoProvv.numRaccoltaGenerale = sentenzaEntity?.numeroRaccoltaGenerale;
        const [anno, numero] = Utils.calcoloNumeroRaccoltaGenerale(
          sentenzaEntity?.numeroRaccoltaGenerale + '',
        );
        if (numero && anno) {
          infoProvv.numRaccoltaGeneraleString = numero + '/' + anno;
        }
        //FIXME inizio da eliminare  una volta che facciamo la fase 2 dei riuniti
        /*  if (
          !infoProvv.statoProvvedimento &&
          infoProvv.isPrincipalRicorsoRiunito
        ) {
          infoProvv.statoProvvedimento =
            ProvvedimentiStatoEnum.RIUNITO_CARTACEO;
        }*/
        // fine eliminazione fixme
        this.logger.log(
          `Fine nel blocco del riunito2. infoProvv:${JSON.stringify(
            infoProvv,
          )}, idRIcUdien:${ricorsoUdienza.idRicudien}`,
        );
        return infoProvv;
      } else {
        infoProvv.statoProvvedimento = ProvvedimentiStatoEnum.RIUNITO;
        const ricorsoOnlyNrgReale =
          await this.ricorsoService.ricorsoFinOnlyNrgRelaedByNrg(
            ricorsoUdienza.principale,
          );

        if (ricorsoOnlyNrgReale) {
          const ricorsoRiunito = new RiunitoDto();
          const [anno, numero] = Utils.calcoloNumeroFascicolo(
            ricorsoOnlyNrgReale?.nrgReale + '',
          );
          if (anno && numero) {
            ricorsoRiunito.anno = anno;
            ricorsoRiunito.numero = numero;
          }
          infoProvv.ricorsoRiunito = ricorsoRiunito;
        }
      }
      return infoProvv;
      //},);
    }
    return null;
  }
}
