import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTRicorsoEntity } from '../consultazioni-graphql/entities/penale_t_ricorso.entity';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';

@UfficioService()
export class TRicorsoService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  ricorsi(): Promise<PenaleTRicorsoEntity[]> {
    return this.connection.getRepository(PenaleTRicorsoEntity).find({
      relations: {
        doveFto: true,
        doveSta: true,
        sezione: true,
        tipoRicorso: true,
      },
    });
  }

  ricorsoFindByNrg(nrg: number): Promise<PenaleTRicorsoEntity | null> {
    return this.connection.getRepository(PenaleTRicorsoEntity).findOne({
      where: { nrg },
      relations: {
        doveFto: true,
        doveSta: true,
        sezione: true,
        tipoRicorso: true,
      },
    });
  }
  ricorsoFinOnlyNrgRelaedByNrg(
    nrg: number,
  ): Promise<PenaleTRicorsoEntity | null> {
    return this.connection.getRepository(PenaleTRicorsoEntity).findOne({
      select: { nrg: true, nrgReale: true },
      where: { nrg },
    });
  }
}
