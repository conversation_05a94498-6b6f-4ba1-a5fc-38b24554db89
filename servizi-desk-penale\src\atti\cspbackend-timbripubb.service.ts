import { Logger } from '@nestjs/common';
import { CspbackendTimbripubbEntity } from '../consultazioni-graphql/entities/cspbackend_timbripubb.entity';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { StreamableFile } from '@nestjs/common';
import { ServiziDepositoException } from '../exceptions/servizi-deposito.exception';
import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { Utils } from 'src/utils/utils';
import { UfficiDBService } from '../uffici/ufficiDB.service';

@UfficioService()
export class CspbackendTimbripubbService {
  private readonly logger = new Logger(CspbackendTimbripubbService.name);

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly configService: ConfigService,
    private readonly ufficiDBService: UfficiDBService,
  ) {}

  async getIdCatAttoAndIdCatAttoOscurato(
    nrg: number,
    idUdien: number,
  ): Promise<{ idCatAtto: string; idCatAttoOscurato: string } | null> {
    const repository = this.connection.getRepository(
      CspbackendTimbripubbEntity,
    );
    const result = await repository.findOne({
      where: { nrg, idUdien },
      select: ['idcatatto', 'idcatattooscurato'],
    });

    if (result) {
      return {
        idCatAtto: result.idcatatto,
        idCatAttoOscurato: result.idcatattooscurato,
      };
    }

    return null;
  }

  async downloadAtto(idAtto: string): Promise<StreamableFile> {
    this.logger.log(`Inizio del Download dell'atto. idAtto: ${idAtto}`);

    const ufficioDB = await this.ufficiDBService.getFirstUfficio();
    Utils.checkUfficoDB(ufficioDB);

    const urlDeposito = `${this.configService.get(
      'app.depositiUrl',
    )}/atti/download-atto-informatico/${idAtto}?originale=false&codiceUfficio=${
      ufficioDB?.codiceUfficio
    }&registro=CASSPENALE`;

    this.logger.debug(`call servizi depositi: ${urlDeposito}`);

    try {
      const response = await axios.get(urlDeposito, {
        responseType: 'arraybuffer',
      });

      if (response.status === 200) {
        this.logger.log(
          `Download dell'atto completato con successo. idAtto: ${idAtto}`,
        );
        return new StreamableFile(response.data, { type: 'application/pdf' });
      } else {
        this.logger.error(
          `Errore nel download dell'atto. idAtto: ${idAtto}, ${response.status}: ${response.statusText}`,
        );
        throw new ServiziDepositoException('depError:' + response?.data || '');
      }
    } catch (error) {
      this.logger.error(
        `Errore nel contattare il servizio 'servizio-depositi': idAtto: ${idAtto}, url: ${urlDeposito}`,
        error,
      );
      if (error.response) {
        throw new ServiziDepositoException(
          "Errore nel contattare il servizio 'servizio-depositi': " +
            error.response.data,
        );
      } else {
        throw new ServiziDepositoException('noServiceError');
      }
    }
  }
}
