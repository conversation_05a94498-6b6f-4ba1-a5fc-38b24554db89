import { <PERSON>rg<PERSON>, <PERSON><PERSON>, Query, <PERSON>solve<PERSON>ield, Resol<PERSON> } from '@nestjs/graphql';
import { PenaleTRicorso } from '../models/penale_t_ricorso.model';
import { PenaleTRicorsoResolver } from './penaleTRicorso.resolver';
import { Inject, NotFoundException } from '@nestjs/common';
import { PenaleTUdienza } from '../models/penale_t_udienza.model';
import { PenaleProvvedimenti } from '../models/penale_provvedimenti.model';
import { ProvvedimentiService } from '../../provvedimenti/provvedimenti.service';

@Resolver(() => PenaleTRicorso)
export class PenaleRicorsoDetailsResolver extends PenaleTRicorsoResolver {
  @Inject()
  protected readonly provvedimentoService: ProvvedimentiService;
  @Query(() => PenaleTRicorso, { name: 'ricorsoByIdUdienAndNrg' })
  async ricorsoByIdUdienAndNrg(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
  ): Promise<PenaleTRicorso> {
    const ricorso = await this.tRicorsoService.ricorsoFindByNrg(nrg);
    if (!ricorso) {
      throw new NotFoundException(nrg);
    }
    return ricorso;
  }

  @ResolveField('udienza', () => PenaleTUdienza)
  async getDataUdienza(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<PenaleTUdienza | null> {
    if (idUdien) {
      return this.udienzaService.udienza(idUdien);
    }
    return null;
  }

  @ResolveField('provvedimento', () => PenaleProvvedimenti, { nullable: true })
  async getLastProvvedimento(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<PenaleProvvedimenti | null> {
    if (idUdien) {
      return this.provvedimentoService.provvedimentoLastProvvedimentoByIdUdienAndNrg(
        idUdien,
        nrg,
      );
    }
    return null;
  }
}
