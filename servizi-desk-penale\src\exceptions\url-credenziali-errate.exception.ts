import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class UrlCredenzialiErrateException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
  ) {
    super(
      response,
      CodeErrorEnumException.CREDENZIALI_ERRATE,
      NSTypeErrorEnum.ERROR,
      options,
    );
  }
}
