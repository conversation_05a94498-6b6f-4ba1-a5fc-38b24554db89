import {
  BeforeInsert,
  Column,
  Entity,
  PrimaryColumn,
} from 'typeorm';
import { v4 as uuid4 } from 'uuid';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';
import { PenaleRicercaRiunitiEntity } from './penale_v_ricerca_riuniti.entity';

@Entity('PENALE_NOTIFICHE') //nome tabella su schema oracle
export class PenaleNoticheEntity {
  @PrimaryColumn({ name: 'ID_NOTIFICA' })
  idNotifica: string;

  @Column({ name: 'DESCRIZIONE' })
  descrizione: string;
  @Column({ name: 'TIPO' })
  tipo: string;
  @Column({ name: 'NRG' })
  nrg: number;

  @Column({
    name: 'N_READ',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  read: boolean;
  @Column({ name: 'CREATE_AT' })
  dataCreazione: Date;
  @Column({ name: 'ID_UTENTE_TO_SEND' })
  idUtente: number;
  @Column({ name: 'ID_UDIEN' })
  idUdienza: number;
  riunitiView: PenaleRicercaRiunitiEntity;

  public constructor(init?: Partial<PenaleNoticheEntity>) {
    Object.assign(this, init);
  }

  @BeforeInsert()
  generateUuid() {
    this.idNotifica = uuid4().replace(/-/g, '');
  }
}
