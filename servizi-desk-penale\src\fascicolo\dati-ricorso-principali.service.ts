import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleDatiRicPrincipaleEntity } from '../consultazioni-graphql/entities/penale_dati_ric_principale.entity';

@UfficioService()
export class DatiRicorsoPrincipaliService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  async datiRicorsoByNrg(
    nrg: number,
  ): Promise<PenaleDatiRicPrincipaleEntity | null> {
    return this.connection
      .getRepository(PenaleDatiRicPrincipaleEntity)
      .findOneBy({ nrg: nrg });
  }
}
