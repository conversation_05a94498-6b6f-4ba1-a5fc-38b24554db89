import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, <PERSON>rent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { ReatiService } from '../../reati/reati.service';
import { PenaleTReati } from '../models/penale_t_reati.model';
import { PenaleTUtente } from '../models/penale_t_utente.model';
import { PenaleProvvedimentiNote } from '../models/penale_provvedimenti_note.model';
import { ProvvimpugnatiService } from '../../provvImpugnati/provvimpugnati.service';
import { PenaleTProvved } from '../models/penale_t_provved.model';

@Resolver(() => PenaleTProvved)
export class PenaleTProvvedResolver {
  constructor(private readonly provvImpugnatiService: ProvvimpugnatiService) {}

  @Query(() => PenaleTProvved, { name: 'provvedimentoImpugnato' })
  async provvedimentoImpugnato(
    @Args('id') id: number,
  ): Promise<PenaleTProvved> {
    const provvedimento =
      await this.provvImpugnatiService.provvedimentoImpugnato(id);
    if (!provvedimento) {
      throw new NotFoundException(id);
    }
    return provvedimento;
  }

  @Query(returns => [PenaleTProvved], { name: 'provvedimentiImpugnati' })
  provvedimentiImpugnati(): Promise<PenaleTProvved[]> {
    return this.provvImpugnatiService.provvedimentiImpugnati();
  }
}
