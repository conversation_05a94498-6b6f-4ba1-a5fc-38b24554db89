import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
@ObjectType()
export class PenaleAnagraficaParti {
  @Field(type => ID)
  idAnagParte: number;

  @Field(type => String)
  cognome: string;
  @Field(type => String)
  nome?: string;
  @Field(type => String)
  codiceFiscale?: string;
  @Field(type => Date)
  dataNascita?: Date;

  @Field(type => String)
  luogoNascita?: string;
  @Field(type => String)
  provinciaNascita?: string;
  @Field(type => Int)
  idFunzione?: number;

  @Field(type => Date)
  oggi?: Date;

  @Field(type => Int)
  operatore?: number;
  @Field(type => Int)
  nazionalita?: number;
  @Field(type => Int)
  glbTime?: number;

  @Field(type => String)
  codiceUnivoco?: string;
}
