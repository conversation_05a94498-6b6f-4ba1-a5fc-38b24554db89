import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleReatiRicorsoEntity } from '../consultazioni-graphql/entities/penale_reati_ricorso.entity';

@UfficioService()
export class ReatiRicorsoService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  reatiRicorso(): Promise<PenaleReatiRicorsoEntity[]> {
    return this.connection.getRepository(PenaleReatiRicorsoEntity).find({
      relations: {
        tipoD: true,
      },
    });
  }

  reatoRicorso(
    idReatiRicorsi: number,
  ): Promise<PenaleReatiRicorsoEntity | null> {
    return this.connection.getRepository(PenaleReatiRicorsoEntity).findOne({
      where: { idReatiRicorsi: idReatiRicorsi },
      relations: {
        tipoD: true,
      },
    });
  }

  async reatiRicorsoFindByNrg(
    nrg: number,
  ): Promise<PenaleReatiRicorsoEntity[] | null> {
    return this.connection.getRepository(PenaleReatiRicorsoEntity).find({
      where: { nrg },
      relations: {
        tipoD: true,
      },
    });
  }
}
