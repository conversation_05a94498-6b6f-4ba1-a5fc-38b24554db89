import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { Column, PrimaryColumn } from 'typeorm';
import {PenaleTSentenza} from "./penale_t_sentenza.model";

@ObjectType()
export class PenaleTEsitoSent {
  @Field(type => ID)
  idFunzione: number;

  @Field(type => Int)
  operatore?: number;
  @Field(type => Int)
  idEsito1?: number;
  @Field(type => Int)
  idEsito2?: number;
  @Field(type => Int)
  idEsito3?: number;
  @Field(type => Int)
  idEsito?: number;
  @Field(type => Int)
  idSent?: number;
  @Field(type => String)
  art28?: string;

  @Field(type => String)
  art94?: string;

  sentenza?: PenaleTSentenza;
  public constructor(init?: Partial<PenaleTEsitoSent>) {
    Object.assign(this, init);
  }
}
