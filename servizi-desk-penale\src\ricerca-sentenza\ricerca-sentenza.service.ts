import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { Inject } from '@nestjs/common';
import { PenaleVRicercaPenaleSentenzaEntity } from '../consultazioni-graphql/entities/penale_v_ricerca_penale_sentenza.entity';
import { PenaleRicercaRiunitiEntity } from '../consultazioni-graphql/entities/penale_v_ricerca_riuniti.entity';

@UfficioService()
export class RicercaSentenzaService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  async sentenzaByIdRicUdien(
    idRicudien: number,
  ): Promise<PenaleVRicercaPenaleSentenzaEntity | null> {
    return this.connection
      .getRepository(PenaleVRicercaPenaleSentenzaEntity)
      .findOne({
        where: { idRicUdien: idRicudien },
        relations: {
          riunitiView: true,
        },
      });
  }

  async getRicorsoRiunitoViewByIdRicUdien(
    idRicudien: number,
  ): Promise<PenaleRicercaRiunitiEntity | null> {
    return this.connection.getRepository(PenaleRicercaRiunitiEntity).findOne({
      where: { idRicUdien: idRicudien },
    });
  }
  async getRicorsoRiunitoViewByIdRicUdienPadre(
    idRicudienPadre: number,
  ): Promise<Array<PenaleRicercaRiunitiEntity> | null> {
    return this.connection.getRepository(PenaleRicercaRiunitiEntity).find({
      where: { idRicudienPadre: idRicudienPadre },
    });
  }
}
