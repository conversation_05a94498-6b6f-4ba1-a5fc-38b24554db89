import { Field, ID, Int, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PenaleAnagmagisDetails {
  @Field(type => ID)
  idAnagmagis: number;

  @Field(type => String)
  cognome?: string;
  @Field(type => String)
  nome?: string;

  @Field(type => Date)
  dataNascita?: Date;
  @Field(type => Date)
  oggi?: Date;
  @Field(type => Int)
  operatore?: number;
  @Field(type => Int)
  idFunzione?: number;
  @Field(type => String)
  codiceFiscale?: string;

  public constructor(init?: Partial<PenaleAnagmagisDetails>) {
    Object.assign(this, init);
  }
}
