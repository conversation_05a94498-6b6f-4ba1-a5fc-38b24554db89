import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';
import { PenaleTEsitoEntity } from './penale_t_esito.entity';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';

@Entity('PENALE_T_SENTENZA') //nome tabella su schema oracle
export class PenaleTSentenzaEntity {
  @Column({ name: 'SENTENZA' })
  sentenza: number;

  @PrimaryColumn({ name: 'ID_SENT' })
  idSent: number;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_TIPOSENT' })
  idTipoSent: PenaleParamEntity;

  @Column({ name: 'ID_ESTENSORE' })
  idEstentore: number;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @Column({ name: 'OPERATOR<PERSON>' })
  operatore: number;
  @Column({ name: 'DATAMINUT<PERSON>' })
  dataMinuta: Date;
  @Column({ name: 'DATAPUBBL' })
  dataPubblicazione: Date;
  @Column({ name: 'NRACCG' })
  nraccg: number;
  @Column({ name: 'ID_PQM' })
  idPqm: number;
  @Column({ name: 'ID_TIPOUD' })
  idTipoUdienza: number;
  @Column({ name: 'QUANTI' })
  quanti: number;
  @Column({ name: 'QUANTICON' })
  quantiCon: number;
  @Column({ name: 'QUANTIPR' })
  quantiPr: number;
  @Column({ name: 'QUANTICONPR' })
  quantiConPr: number;
  @Column({ name: 'NUMMASS' })
  numMass: number;
  @Column({ name: 'DATAREST' })
  dataRest: Date;
  @Column({ name: 'DATARESTPR' })
  dataRestPr: Date;
  @Column({ name: 'DATAMASS' })
  dataMass: Date;
  @Column({ name: 'DATAASSE' })
  dataAsse: Date;
  @Column({ name: 'ID_MAGIS' })
  idMagis: number;
  @Column({
    name: 'DEPOSITO_TELEMATICO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  depositoTelematico: boolean;
  @Column({
    name: 'PUBBLICATO_TELEMATICO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  pubblicatoTelematico: boolean;

  esito?: PenaleTEsitoEntity;
}
