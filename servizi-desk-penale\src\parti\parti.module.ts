import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { TRicorsoUdienzaService } from '../ricorso-udienza/t-ricorso-udienza.service';
import { PartiService } from './parti.service';

@Module({
  imports: [
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [PartiService],
  exports: [PartiService],
})
export class PartiModule {}
