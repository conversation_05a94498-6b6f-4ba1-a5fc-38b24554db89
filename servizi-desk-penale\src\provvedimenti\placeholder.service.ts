import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { Inject } from '@nestjs/common';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { DataSource } from 'typeorm';
import { ArgsProvvedimentoInput } from '../consultazioni-graphql/entities/dto/args-provvedimento.input';
import { PenaleTRicorsoEntity } from '../consultazioni-graphql/entities/penale_t_ricorso.entity';
import { PenaleTUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_udienza.entity';
import { PenaleTUdienza } from '../consultazioni-graphql/models/penale_t_udienza.model';
import { PenaleCollegioEntity } from '../consultazioni-graphql/entities/penale_collegio.entity';
import { PenaleTMagisEntity } from '../consultazioni-graphql/entities/penale_t_magis.entity';
import { PenaleTMagis } from '../consultazioni-graphql/models/penale_t_magis.model';
import { PenaleAnagmagisEntity } from '../consultazioni-graphql/entities/penale_anagmagis.entity';
import { PenaleAnagmagis } from '../consultazioni-graphql/models/penale_anagmagis.model';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import * as moment from 'moment/moment';
import { Utils } from '../utils/utils';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';
import { PenaleTEsitoSentEntity } from '../consultazioni-graphql/entities/penale_t_esito_sent.entity';
import { GenericErrorException } from 'src/exceptions/generic-error.exception';
import { CodeErrorEnumException } from 'src/exceptions/code-error-enum.exception';
import { PenaleMotivazioniService } from 'src/penale-motivazioni/penale-motivazioni.service';

interface Parte {
  TIPOFIG: string;
  COGNOME: string;
  NOME: string;
  LUOGONASC: string;
  DATANASC: Date;
  DESCRIZIONE: string;
  TIPOTAB: string;
  NAZIONE: string;
}

interface DatiTabella {
  anagrafica: string;
  cognome: string;
  ruolo: string;
  tipoMag: string;
  gradoMag: number;
  dati: string;
}

@UfficioService()
export class PlaceholderService {
  private nrg?: number;
  private idUdienza?: number;
  private idRicudien?: number;
  private args?: ArgsProvvedimentoInput;
  protected fascicoloObj?: any;
  protected partiProvvedimentoObj?: any;
  protected provvImpugnatoObj?: any;
  protected dataDecisioneObj?: any;
  protected epigrafeObj?: any;
  protected udienzaObj?: any;
  protected testoLiberoObj?: any;
  protected testoOscuratoObj?: any;
  protected introduzioneObj?: any;
  protected motivoRicorsoObj?: any;
  protected finaleDispositivoObj?: any;
  protected textPqmObj?: any;

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource, // private anagraficaDistrettuale: AnagraficaDistrettualeService
    private readonly penaleMotivazioniService: PenaleMotivazioniService,
  ) {}

  async takePlaceholder(json: string): Promise<RegExpMatchArray[]> {
    const regex = new RegExp(
      '@@@(db|args)?\\.?(\\w+)(\\.(\\w+))?(\\.(\\w+))?@@@',
      'g',
    );
    return [...json.matchAll(regex)];
  }

  /**
   *
   * @param nrg
   * @param idUdienza
   * @param template JSON generazione doc
   * @param argsPlaceholder
   * @returns mappa delle variabili
   */
  async resolveVariables(
    nrg: number,
    idRicudien: number,
    idUdienza: number,
    template: string,
    argsPlaceholder: ArgsProvvedimentoInput,
    cached?: boolean,
  ): Promise<any> {
    this.idRicudien = idRicudien;
    this.nrg = nrg;
    this.idUdienza = idUdienza;
    this.args = argsPlaceholder;
    const variab: any = {};
    const placeholder = await this.takePlaceholder(template);
    for (const parts of placeholder) {
      if (parts[1] === 'db') {
        const v = await this.resolveDb(parts[2], parts[4], parts[6], cached);
        if (v != null) variab[parts[0] as string] = v;
      } else if (parts[1] === 'args') {
        const v = await this.resolveArg(argsPlaceholder, parts[2]);
        if (v != null) variab[parts[0] as string] = v;
      } else if (parts[2] === 'dataCreazione') {
        const today = new Date();
        variab[parts[0] as string] =
          today.getDate() +
          '-' +
          (today.getMonth() + 1) +
          '-' +
          today.getFullYear();
      }
    }

    return variab;
  }

  async resolveDb(
    entityName: string,
    field: string,
    subField: string,
    cached = true,
  ) {
    // @ts-ignore
    if (!cached || this[entityName + 'Obj'] == null) {
      // @ts-ignore
      const rfn = this[entityName];
      if (
        entityName === 'fascicolo' ||
        entityName === 'partiProvvedimento' ||
        entityName === 'provvImpugnato'
      ) {
        // @ts-ignore
        this[entityName + 'Obj'] = rfn
          .bind(this, this.nrg, this.idRicudien)
          .apply();
      } else if (
        entityName === 'testoLibero' ||
        entityName === 'testoOscurato' ||
        entityName === 'introduzione' ||
        entityName === 'motivoRicorso' ||
        entityName === 'finaleDispositivo' ||
        entityName === 'textPqm' ||
        entityName === 'dataDecisione'
      ) {
        // @ts-ignore
        this[entityName + 'Obj'] = rfn.bind(this, this.args.idProvv).apply();
      } else {
        // @ts-ignore
        this[entityName + 'Obj'] = rfn
          .bind(this, this.idUdienza, this.nrg)
          .apply();
      }
    }
    // @ts-ignore
    const queryResult = await this[entityName + 'Obj'];
    if (queryResult != null) {
      if (field != null) {
        if (subField == null) {
          return Reflect.get(queryResult, field);
        } else {
          if (
            Array.isArray(Reflect.get(queryResult, field)) &&
            Reflect.get(queryResult, field)[0] != null
          ) {
            if (subField?.toLowerCase().includes('udienza')) {
              const date = new Date(
                Reflect.get(Reflect.get(queryResult, field)[0], subField),
              );
              return (
                date.getDate() +
                '-' +
                (date.getMonth() + 1) +
                '-' +
                date.getFullYear()
              );
            }
            return Reflect.get(Reflect.get(queryResult, field)[0], subField);
          }
          return Reflect.get(Reflect.get(queryResult, field), subField);
        }
      } else {
        return queryResult;
      }
    } else {
      return null;
    }
  }

  async resolveArg(argsPlaceholder: any, field: string): Promise<any> {
    if (field in argsPlaceholder) {
      if (field === 'dataTermineNote' && argsPlaceholder[field] !== null) {
        const dataParts = argsPlaceholder[field].split('T')[0].split('-');
        return dataParts[2] + '-' + dataParts[1] + '-' + dataParts[0];
      }
      if (field === 'dataDecisione' && argsPlaceholder[field] !== null) {
        return (
          'Così è deciso, ' +
          moment(argsPlaceholder[field]).format('DD/MM/YYYY')
        );
      }
      if (field === 'tipologiaProvvedimento') {
        return argsPlaceholder[field].replace('MINUTA_', '');
      }
      return argsPlaceholder[field];
    }
  }

  async fascicolo(nrg: number) {
    return await this.connection.getRepository(PenaleTRicorsoEntity).findOne({
      where: { nrg },
      relations: {
        doveFto: true,
        doveSta: true,
        sezione: true,
        tipoRicorso: true,
      },
    });
  }

  async udienza(idUdienza: number, nrg: number) {
    const ud: PenaleTUdienza = (await this.connection
      .getRepository(PenaleTUdienzaEntity)
      .findOne({
        where: { idUdien: idUdienza },
        relations: {
          tipoUdienza: true,
          sezione: true,
          aula: true,
        },
      })) as PenaleTUdienza;
    let collegio = await this.connection
      .getRepository(PenaleCollegioEntity)
      .find({
        where: { idUdienza: idUdienza, inUdienza: 1 },
      });
    collegio = collegio.filter(c => c.tipoMag !== 'RI' && c.tipoMag !== 'PM');
    const collegioPresidenti = collegio.filter(c => c.tipoMag === 'PRE');
    if (collegioPresidenti?.length !== 1) {
      throw new GenericErrorException(
        'Nel collegio non è stato trovato il Presidente',
        CodeErrorEnumException.COLLEGIO_ERROR,
      );
    }
    const idMagPresidente = collegioPresidenti[0].idMagis;
    collegio = collegio.filter(
      c =>
        c.tipoMag === 'PRE' ||
        (c.tipoMag !== 'PRE' && c.idMagis !== idMagPresidente),
    );
    const ricUdien = await this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: { nrg: nrg, idUdienza: idUdienza },
      });
    const datiTabella: DatiTabella[] = [];
    let index = 0;
    const idEstensoreMagis = await this.getIdEstensore(nrg, idUdienza);
    for (const m of collegio) {
      const mag = (await this.connection
        .getRepository(PenaleTMagisEntity)
        .findOne({
          where: { idMagis: m.idMagis },
        })) as PenaleTMagis;
      mag.anagraficaMagistrato = (await this.connection
        .getRepository(PenaleAnagmagisEntity)
        .findOneBy({ idAnagmagis: mag.idAnagmagis })) as PenaleAnagmagis;
      mag.anagraficaMagistrato.nome += ' ' + mag.anagraficaMagistrato.cognome;
      let ruolo = '';
      if (m.tipoMag === 'PRE' && m.idMagis === idEstensoreMagis) {
        ruolo = '- Presidente Relatore -';
      } else if (m.tipoMag === 'PRE') {
        ruolo = '- Presidente -';
      } else if (
        ricUdien?.idRelatore === m.idMagis &&
        idMagPresidente !== ricUdien?.idRelatore
      ) {
        ruolo = '- Relatore -';
      } else if (
        m.idMagis === idEstensoreMagis &&
        idMagPresidente !== idEstensoreMagis
      ) {
        ruolo = '- Estensore -';
      }
      datiTabella.push({
        anagrafica: mag.anagraficaMagistrato.nome!,
        cognome: mag.anagraficaMagistrato.cognome!,
        tipoMag: m.tipoMag,
        ruolo: ruolo,
        gradoMag: m.gradoMag,
        dati: '',
      });
      index += 1;
    }
    datiTabella.sort((a, b) => {
      if (a.tipoMag === 'PRE') {
        if (b.tipoMag === 'PRE') {
          if (a.gradoMag === b.gradoMag) {
            return a.cognome.localeCompare(b.cognome);
          } else {
            return a.gradoMag > b.gradoMag ? 1 : -1;
          }
        } else {
          return -1;
        }
      }
      if (a.gradoMag === b.gradoMag) {
        return a.cognome.localeCompare(b.cognome);
      } else {
        return a.gradoMag > b.gradoMag ? 1 : -1;
      }
    });
    const datiUdienza = await this.getDatiUdienza(ud, ricUdien?.idRicudien);

    while (datiTabella.length < datiUdienza.length) {
      datiTabella.push({
        anagrafica: '',
        cognome: '',
        tipoMag: '',
        ruolo: '',
        gradoMag: 0,
        dati: '',
      });
    }

    for (let i = 0; i < datiUdienza.length; i++) {
      datiTabella[i].dati = datiUdienza[i];
    }
    return datiTabella;
  }

  async getDatiUdienza(
    udienza: PenaleTUdienza,
    idRicUdien: number | undefined,
  ) {
    const dati = [];

    let sezionale = '';
    if (idRicUdien) {
      sezionale = (await this.getSezionale(idRicUdien)) ?? '';
    }
    const primaLinea =
      (this.args?.tipologiaProvvedimento === 'MINUTA_ORDINANZA' ||
      this.args?.tipologiaProvvedimento === 'ORDINANZA'
        ? 'Ord. n. sez. '
        : 'Sent. n. sez. ') + sezionale;
    dati.push(primaLinea);

    const secondaLinea =
      (udienza.tipoUdienza.sigla === 'PU' ? 'UP' : 'CC') +
      ' - ' +
      moment(udienza.dataUdienza).format('DD/MM/YYYY');
    dati.push(secondaLinea);

    const terzaLinea =
      'R.G.N. ' + this.args?.numRuolo + '/' + this.args?.anRuolo;
    dati.push(terzaLinea);

    if (idRicUdien) {
      const semplificataQuery = await this.connection.query(
        'SELECT SEMPLIFICATA FROM PENALE_T_ESITO where id_ricudien = :idRicudien',
        [idRicUdien],
      );
      const motSemplificata =
        semplificataQuery?.length > 0 ? semplificataQuery[0].SEMPLIFICATA : '0';
      if (motSemplificata === '1') {
        dati.push('Motivazione Semplificata');
      }
    }

    return dati;
  }

  async getSezionale(idRicUdienza: number) {
    const sentenza = await this.calcolaSentenza(idRicUdienza);
    if (sentenza?.sentenza) {
      const [anno, numero] = Utils.calcolaIdSezionale(sentenza.sentenza + '');
      if (numero && anno) {
        return numero + '/' + anno;
      }
    }
  }

  private async calcolaSentenza(
    idRicUdienza: number,
  ): Promise<PenaleTSentenzaEntity | null> {
    const esito = await this.connection
      .getRepository(PenaleTEsitoEntity)
      .findOneBy({ idRicUdienza: idRicUdienza });
    if (esito?.idEsito) {
      const esitoSent = await this.connection
        .getRepository(PenaleTEsitoSentEntity)
        .findOneBy({ idEsito: esito.idEsito });
      if (esitoSent?.idSent) {
        return await this.connection
          .getRepository(PenaleTSentenzaEntity)
          .findOne({
            where: { idSent: esitoSent.idSent },
            relations: {
              idTipoSent: true,
            },
          });
      }
    }
    return null;
  }

  async testoLibero(idProvv: string) {
    const testoLibero = await this.connection.query(
      'select * from PENALE_PROVV_EDITOR PPE where    PPE.ID_PROVV =  :idProvv ',
      [idProvv],
    );
    const testo = testoLibero[0];
    return testo.TEXT_LIBERO.toString()
      .replace('<br/>', '\n ')
      .replace('<br>', '\n ')
      .replace(/<br(.*?)>/g, '\n ');
  }

  async testoOscurato(idProvv: string) {
    const testoLibero = await this.connection.query(
      'select * from PENALE_PROVV_EDITOR PPE where    PPE.ID_PROVV =  :idProvv ',
      [idProvv],
    );
    const testo = testoLibero[0];
    return testo.TEXT_OSCURATO.toString()
      .replace('<br/>', '\n ')
      .replace('<br>', '\n ')
      .replace(/<br(.*?)>/g, '\n ');
  }

  async introduzione(idProvv: string) {
    const testoLibero = await this.connection.query(
      'select * from PENALE_PROVV_EDITOR PPE where    PPE.ID_PROVV =  :idProvv ',
      [idProvv],
    );
    const testo = testoLibero[0];
    return testo.INTRODUZIONE?.toString()
      .replace('<br/>', '\n ')
      .replace('<br>', '\n ')
      .replace(/<br(.*?)>/g, '\n ');
  }

  async motivoRicorso(idProvv: string) {
    const testoLibero = await this.connection.query(
      'select * from PENALE_PROVV_EDITOR PPE where    PPE.ID_PROVV =  :idProvv ',
      [idProvv],
    );
    const testo = testoLibero[0];
    return testo.MOTIVO_RICORSO?.toString()
      .replace('<br/>', '\n ')
      .replace('<br>', '\n ')
      .replace(/<br(.*?)>/g, '\n ');
  }

  async finaleDispositivo(idProvv: string) {
    const testoLibero = await this.connection.query(
      'select * from PENALE_PROVV_EDITOR PPE where    PPE.ID_PROVV =  :idProvv ',
      [idProvv],
    );
    const testo = testoLibero[0];
    return testo.FINALE_DISPOSITIVO?.toString()
      .replace('<br/>', '\n ')
      .replace('<br>', '\n ')
      .replace(/<br(.*?)>/g, '\n ');
  }

  async textPqm(idProvv: string) {
    const testoLibero = await this.connection.query(
      'select * from PENALE_PROVV_EDITOR PPE where    PPE.ID_PROVV =  :idProvv ',
      [idProvv],
    );
    const testo = testoLibero[0];
    return testo.PQM?.toString()
      .replace('<br/>', '\n ')
      .replace('<br>', '\n ')
      .replace(/<br(.*?)>/g, '\n ');
  }

  async provvImpugnato(nrg: number, idRicUdienza: number) {
    const penaleMotivazioni =
      await this.penaleMotivazioniService.findByRicudien(idRicUdienza);
    if (penaleMotivazioni.length > 0) {
      const motivazione = penaleMotivazioni[0];
      return motivazione.provvedimento ?? '';
    }

    const provvImp = await this.connection.query(
      "select p.ID_PROVV, (select descrizione from penale_param where id_param = ri.tiporic) as TIPORIC, to_char(DATAPROVV,'dd/mm/yyyy') AS DATAPROVV, p.ID_TIPOPROVV, p.NRG, p.IMPUGNATO, p.GRADOPROVV, p.ID_AULO, \n" +
        '            (select y.localita from penale_aulo y where p.id_aulo=y.ID_AULO) LOCALITA, \n' +
        '            (select x.id_autorita from penale_t_aulo x  where x.id_aulo = p.id_aulo) ID_AUTORITA, \n' +
        "            (select a.sigla from penale_param a   where a.id_param = p.id_tipoprovv and a.tipotab= 'TIPOPROVV') DESC_PROVV,  \n" +
        "            (select i.descrizione from penale_param i   where i.id_param = p.gradoprovv and i.tipotab= 'GRADOPROVV') DESC_GRADO_PROVV, \n" +
        '              i.descrizione as DESC_AUTORITA    from penale_t_provved p,         penale_t_aulo aa,          penale_param i, \n' +
        '                   penale_t_ricorso ri  where ri.nrg = :nrg  and ri.nrg = p.nrg  and p.impugnato = 1 \n' +
        "            and p.id_aulo = aa.ID_AULO  and i.tipotab= 'AUTORITA'  and i.id_param = aa.id_autorita",
      [nrg],
    );
    const provvImpugnato = provvImp[0];

    const mappAutorita: { [key: string]: string } = {
      CORTE_APPELLO: "Corte d'appello",
      CORTE_ASSISE: "Corte d'assise",
      CORTE_ASSISE_APPELLO: "Corte d'assise d'appello",
    };

    // Provvedimento
    let provved = '';
    if (provvImpugnato != null) {
      const tipoRic = provvImpugnato.TIPORIC;
      if (tipoRic.includes('CC') || tipoRic.includes('CG')) {
        // mev 257
        provved = 'con ';
      } else {
        provved = 'avverso ';
      }

      if (provvImpugnato.DESC_PROVV === 'DE') {
        provved += 'il decreto';
      }
      if (provvImpugnato.DESC_PROVV === 'SO') {
        provved += 'la sentenza/ordinanza';
      }
      if (provvImpugnato.DESC_PROVV === 'OR') {
        provved += "l'ordinanza";
      }
      if (provvImpugnato.DESC_PROVV === 'AL') {
        provved += 'il provvedimento';
      }
      if (provvImpugnato.DESC_PROVV === 'SE') {
        provved += 'la sentenza';
      }

      provved += ' del ' + provvImpugnato.DATAPROVV;

      if (provvImpugnato.DESC_AUTORITA.includes('CORTE')) {
        provved +=
          ' della ' +
          (mappAutorita[provvImpugnato.DESC_AUTORITA.replaceAll(' ', '_')] ??
            provvImpugnato.DESC_AUTORITA) +
          ' di ' +
          (await this.formatLocation(provvImpugnato.LOCALITA)) +
          '';
      } else if (
        provvImpugnato.DESC_AUTORITA.includes('PREFETTURA') ||
        provvImpugnato.DESC_AUTORITA.includes('QUESTURA') ||
        provvImpugnato.DESC_AUTORITA.includes('PROC')
      ) {
        provved +=
          ' della ' +
          provvImpugnato.DESC_AUTORITA +
          ' di ' +
          (await this.formatLocation(provvImpugnato.LOCALITA)) +
          '';
      } else {
        provved +=
          ' del ' +
          provvImpugnato.DESC_AUTORITA +
          ' di ' +
          (await this.formatLocation(provvImpugnato.LOCALITA)) +
          '';
      }
    }
    return provved;
  }

  async atti(nrg: number, idRicUdienza: number, deplano?: string) {
    const penaleMotivazioni =
      await this.penaleMotivazioniService.findByRicudien(idRicUdienza);
    if (penaleMotivazioni.length > 0) {
      const motivazione = penaleMotivazioni[0];
      return motivazione.atti ?? '';
    }
    const dati = await this.connection.query(
      "select dovesta, tipoud, coll.ID_MAGIS as id_pres, ricud.ID_RELATORE, anag.nome || ' ' || anag.cognome as relatore, anagPM.nome || ' ' || anagPM.cognome as pm, anagRIS.nome || ' ' || anagRIS.cognome as riserva, verbale.DESC_CONCLUSIONI_PG\n" +
        'from penale_ricorso ric\n' +
        'join PENALE_RICUDIEN ricud on ric.nrg = ricud.nrg\n' +
        'join penale_udienza ud on ricud.ID_UDIEN = ud.ID_UDIEN\n' +
        'join PENALE_MAGIS magis on magis.ID_MAGIS = ricud.ID_RELATORE\n' +
        'join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS\n' +
        "join PENALE_COLLEGIO coll on coll.ID_UDIEN = ud.ID_UDIEN and coll.TIPOMAG = 'PRE'\n" +
        "left join PENALE_COLLEGIO collPM on collPM.ID_UDIEN = ud.ID_UDIEN and collPM.TIPOMAG = 'PM'\n" +
        'left join PENALE_MAGIS magisPM on magisPM.ID_MAGIS = collPM.ID_MAGIS\n' +
        'left join PENALE_ANAGMAGIS anagPM on anagPM.ID_ANAGMAGIS = magisPM.ID_ANAGMAGIS\n' +
        "left join PENALE_COLLEGIO collRIS on collRIS.ID_UDIEN = ud.ID_UDIEN and collRIS.TIPOMAG = 'RIS'\n" +
        'left join PENALE_MAGIS magisRIS on magisRIS.ID_MAGIS = collRIS.ID_MAGIS\n' +
        'left join PENALE_ANAGMAGIS anagRIS on anagRIS.ID_ANAGMAGIS = magisRIS.ID_ANAGMAGIS\n' +
        'left join PENALE_VERBALE_RICORSO verbale on verbale.ID_RICUDIEN = ricud.ID_RICUDIEN\n' +
        'where ric.nrg = :nrg',
      [nrg],
    );
    if (!dati || dati.length === 0) {
      throw new GenericErrorException(
        'Nel collegio non è stato trovato il Presidente',
        CodeErrorEnumException.COLLEGIO_ERROR,
      );
    }

    const datiNecessari = dati[0];

    const sezione = datiNecessari.DOVESTA;
    const tipoUd = datiNecessari.TIPOUD;
    const idPres = datiNecessari.ID_PRES;
    const idRelatore = datiNecessari.ID_RELATORE;
    const relatore = this.capitalize(datiNecessari.RELATORE);
    const pm = datiNecessari.PM;
    const riserva = datiNecessari.RISERVA;
    const descCoclusioniPg = datiNecessari.DESC_CONCLUSIONI_PG;

    const descConclusioniPG = descCoclusioniPg
      ? descCoclusioniPg
          .toLowerCase()
          .replace(/proc\. gen\./g, 'Sostituto Procuratore generale')
          .replace(
            /sostituto procuratore generale/,
            (match: string) => match.charAt(0).toUpperCase() + match.slice(1),
          )
      : '';

    // Atti
    let atti = '';
    if (sezione.includes('S7')) {
      if (deplano === '0')
        atti += '\ndato avviso alle parti;\n';
      atti += 'udita la relazione svolta dal ';
      if (idPres !== idRelatore) {
        atti += 'Consigliere ';
      } else {
        atti += 'Presidente ';
      }
      atti += relatore + ';';
    } else if (tipoUd.includes('PU')) {
      atti =
        'visti gli atti, il provvedimento impugnato e il ricorso;\nudita la relazione svolta dal ';
      if (idPres !== idRelatore) {
        if (sezione.includes('SU')) {
          atti += 'Componente ';
        } else {
          atti += 'Consigliere ';
        }
      } else {
        atti += 'Presidente ';
      }
      atti +=
        relatore +
        ';\nudito il Pubblico Ministero, in persona del Sostituto Procuratore';
      if (pm) {
        atti += pm;
      } else if (riserva) {
        atti += riserva;
      }
      atti += '\nche ha concluso chiedendo\n';
    } else {
      atti += 'udita la relazione svolta dal ';
      if (idPres !== idRelatore) {
        atti += 'Consigliere ';
      } else {
        atti += 'Presidente ';
      }
      //TODO per card cambiare PG in una Frase
      atti +=
        relatore +
        ';\nlette/sentite le conclusioni del Sostituto Procuratore generale ';
      if (pm) {
        atti += pm;
      }
    }
    if (descConclusioniPG) {
      atti += '\n' + descConclusioniPG;
    }

    return atti;
  }

  async formatLocation(city: string) {
    if (city) {
      const lowercaseCity = city.toLowerCase();
      return lowercaseCity.charAt(0).toUpperCase() + lowercaseCity.slice(1);
    }
    return city;
  }

  async partiProvvedimento(nrg: number, idRicUdienza: number) {
    let ricorrenti: Parte[] = [];
    let nonRicorrenti: Parte[] = [];
    let partiContro: Parte[] = [];
    let pm: Parte[] = [];
    let corteAppPG = '';
    let tribunalePM = '';

    let noRic = '';
    let ricor = '';
    let PmPiuC = '';
    let pContro = '';
    let finalString = '';

    const penaleMotivazioni =
      await this.penaleMotivazioniService.findByRicudien(idRicUdienza);
    if (penaleMotivazioni.length > 0) {
      const motivazione = penaleMotivazioni[0];
      noRic = motivazione.nonRicorrenti ?? '';
      ricor = motivazione.ricorrenti ?? '';
      pContro = motivazione.partiContro ?? '';
      finalString += (motivazione.proposta ?? '').split('&')[0] + '\n';
      finalString += pContro;
      finalString += ricor;
      finalString += noRic;
      return finalString;
    }

    const tipoRicQuery = await this.connection.query(
      'SELECT SIGLA FROM PENALE_PARAM p join PENALE_T_RICORSO ric on p.id_param = ric.tiporic where ric.nrg = :nrg',
      [nrg],
    );
    const tipoRic = tipoRicQuery[0].SIGLA;

    const provvImp = await this.connection.query(
      'SELECT NUMPROVV AS NUMERO, PARAM1.DESCRIZIONE NATURA, PARAM1.SIGLA SIGLANAT, PARAM2.DESCRIZIONE AS AUTORITA,\n' +
        "             to_char(DATAPROVV,'dd/mm/yyyy') AS DATADEP, PENALE_AULO.LOCALITA AS LOCALITA,\n" +
        '             (SELECT SIGLA FROM PENALE_PARAM P WHERE P.ID_PARAM = PENALE_PROVVED.GRADOPROVV) GRADO,\n' +
        "             (SELECT 'CORTE D''APPELLO DI ' || AR.LOCALITA FROM PENALE_AULO A2,PENALE_AULORIF AR\n" +
        '            \t\t\tWHERE A2.ID_AULO=PENALE_AULO.ID_AULO AND A2.ID_AULORIFCA=AR.ID_AULORIF) corteAppPG, \n' +
        "             (SELECT 'TRIBUNALE DI ' || AR.LOCALITA FROM PENALE_AULO A2,PENALE_AULORIF AR\n" +
        '            \t\t\tWHERE A2.ID_AULO=PENALE_AULO.ID_AULO AND A2.ID_AULORIFTR=AR.ID_AULORIF) tribunalePM \n' +
        '            FROM PENALE_PROVVED, PENALE_PARAM PARAM1, PENALE_AULO, PENALE_PARAM PARAM2 \n' +
        "            WHERE IMPUGNATO = 1 AND PARAM1.ID_PARAM = ID_TIPOPROVV AND PENALE_AULO.ID_AULO= PENALE_PROVVED.ID_AULO AND PENALE_AULO.AUTORITA = PARAM2.SIGLA AND PARAM2.TIPOTAB='AUTORITA' AND NRG = :nrg",
      [nrg],
    );
    corteAppPG = this.formatCorteApp(provvImp[0].CORTEAPPPG);
    tribunalePM = provvImp[0].TRIBUNALEPM;

    /*--PARTI CHE TROVO A SINISTRA IN PARTI_CONTRO: (POSSONO ESSERE PG O PC...)*/
    pm = await this.connection.query(
      "SELECT NRG, TIPOFIG, PP.DESCRIZIONE, COGNOME, NOME, PP.TIPOTAB, TO_CHAR(DATANASC,'dd/mm/yyyy') AS DATANASC, LUOGONASC, numord,id_parte, min(PC.ID_PARTE_2) cntr, a.ricorrente,NAZIONE.DESCRIZIONE AS NAZIONE\n" +
        '            \t\t FROM PENALE_PARTI A\n' +
        '            \t\t JOIN PENALE_ANAGPARTI ON A.ID_ANAGPARTE = PENALE_ANAGPARTI.ID_ANAGPARTE\n' +
        "            \t     JOIN PENALE_PARAM PP ON A.TIPOFIG = PP.SIGLA AND PP.TIPOTAB in ( 'CONTROPARTE', 'FIGURA')\n" +
        '            \t     JOIN PENALE_PARTI_CONTRO PC ON PC.ID_PARTE_1=A.ID_PARTE\n' +
        '            \t\t LEFT JOIN PENALE_PARAM NAZIONE ON NAZIONE.ID_PARAM = PENALE_ANAGPARTI.NAZIONALITA\n' +
        '            \t\t where nrg = :nrg\n' +
        "            \t\t AND SECRETATA='0'\n" +
        "            \t\t AND A.TIPOFIG<>'UC' AND A.TIPOFIG<>'NN'\n" +
        '             GROUP BY NRG, TIPOFIG, PP.DESCRIZIONE, COGNOME, NOME, PP.TIPOTAB, DATANASC, LUOGONASC, numord,id_parte,a.ricorrente,NAZIONE.DESCRIZIONE\n' +
        '             ORDER BY NUMORD, ID_PARTE',
      [nrg],
    );
    /*quelli che stanno a destra (id_parti_2) in parti_contro*/
    partiContro = await this.connection.query(
      "SELECT distinct NRG, TIPOFIG, PP.DESCRIZIONE, COGNOME, NOME, PP.TIPOTAB, TO_CHAR(DATANASC,'dd/mm/yyyy') AS DATANASC, LUOGONASC, numord,id_parte, min(PC.ID_PARTE_2) cntr, a.ricorrente,NAZIONE.DESCRIZIONE AS NAZIONE\n" +
        '            \t\t FROM PENALE_PARTI A\n' +
        '            \t\t JOIN PENALE_ANAGPARTI ON A.ID_ANAGPARTE = PENALE_ANAGPARTI.ID_ANAGPARTE\n' +
        "            \t\t JOIN PENALE_PARAM PP ON A.TIPOFIG = PP.SIGLA and  PP.tipotab in ('FIGURA','CONTROPARTE')\n" +
        '            \t\t JOIN PENALE_PARTI_CONTRO PC ON PC.ID_PARTE_2=A.ID_PARTE\n' +
        '            \t\t LEFT JOIN PENALE_PARAM NAZIONE ON NAZIONE.ID_PARAM = PENALE_ANAGPARTI.NAZIONALITA\n' +
        '            \t\t WHERE NRG = :nrg\n' +
        "            \t\t AND SECRETATA='0'\n" +
        "            \t\t AND A.TIPOFIG<>'UC' AND A.TIPOFIG<>'NN'\n" +
        '            GROUP BY NRG, TIPOFIG, PP.DESCRIZIONE, COGNOME, NOME, PP.TIPOTAB, DATANASC, LUOGONASC, numord,id_parte, a.ricorrente,NAZIONE.DESCRIZIONE\n' +
        '            ORDER BY NUMORD, ID_PARTE',
      [nrg],
    );
    /*ricorrenti che non sono contro nessuno*/
    ricorrenti = await this.connection.query(
      "SELECT NRG, TIPOFIG, PP.DESCRIZIONE, COGNOME, NOME, PP.TIPOTAB, TO_CHAR(DATANASC,'dd/mm/yyyy') AS DATANASC, LUOGONASC, numord,id_parte,RICORRENTE,NAZIONE.DESCRIZIONE AS NAZIONE\n" +
        '            \t\t FROM PENALE_PARTI A\n' +
        '            \t\t JOIN PENALE_ANAGPARTI ON A.ID_ANAGPARTE = PENALE_ANAGPARTI.ID_ANAGPARTE\n' +
        "            \t\t JOIN PENALE_PARAM PP ON A.TIPOFIG = PP.SIGLA                        AND PP.TIPOTAB = 'FIGURA'\n" +
        '            \t\t LEFT JOIN PENALE_PARAM NAZIONE ON NAZIONE.ID_PARAM = PENALE_ANAGPARTI.NAZIONALITA\n' +
        '            \t\t WHERE NRG = :nrg\n' +
        "            \t\t\t\t\t\tAND RICORRENTE='1'\n" +
        '                                  AND NOT EXISTS ( SELECT 1 FROM PENALE_PARTI_CONTRO PC WHERE PC.ID_PARTE_1 = A.ID_PARTE)\n' +
        "            \t\t\t\t\t    AND SECRETATA='0'\n" +
        "            \t\t\t\t\t   AND A.TIPOFIG<>'UC' AND A.TIPOFIG<>'NN'  ORDER BY RICORRENTE DESC, NUMORD, ID_PARTE",
      [nrg],
    );
    /*non ricorrenti che non sono contro nessuno*/
    nonRicorrenti = await this.connection.query(
      " SELECT NRG, TIPOFIG, PP.DESCRIZIONE, COGNOME, NOME, PP.TIPOTAB, TO_CHAR(DATANASC,'dd/mm/yyyy') AS DATANASC, LUOGONASC, numord,id_parte,RICORRENTE,NAZIONE.DESCRIZIONE AS NAZIONE\n" +
        '            \t\t FROM PENALE_PARTI A\n' +
        '            \t\t JOIN PENALE_ANAGPARTI ON A.ID_ANAGPARTE = PENALE_ANAGPARTI.ID_ANAGPARTE\n' +
        "            \t\t JOIN PENALE_PARAM PP ON A.TIPOFIG = PP.SIGLA AND PP.TIPOTAB = 'FIGURA'\n" +
        '            \t\t LEFT JOIN PENALE_PARAM NAZIONE ON NAZIONE.ID_PARAM = PENALE_ANAGPARTI.NAZIONALITA\n' +
        '            \t\t WHERE NRG = :nrg\n' +
        "            \t\t\t\t\t\tAND RICORRENTE='0'\n" +
        '                                  AND NOT EXISTS ( SELECT 1 FROM PENALE_PARTI_CONTRO PC WHERE PC.ID_PARTE_1 = A.ID_PARTE OR PC.ID_PARTE_2=A.ID_PARTE)\n' +
        "            \t\t\t\t\t    AND SECRETATA='0'\n" +
        "            \t\t\t\t\t   AND A.TIPOFIG<>'UC' AND A.TIPOFIG<>'NN'  ORDER BY RICORRENTE DESC, NUMORD, ID_PARTE",
      [nrg],
    );

    if (ricorrenti.length > 1) {
      finalString += 'sui ricorsi proposti da:\n';
    } else {
      finalString += 'sul ricorso proposto da:\n';
    }

    if (
      tipoRic.includes('RO') ||
      tipoRic.includes('PS') ||
      tipoRic.includes('RA') ||
      tipoRic.includes('NP') ||
      tipoRic.includes('EM') ||
      tipoRic.includes('SP') ||
      tipoRic.includes('MR') ||
      tipoRic.includes('AE') ||
      tipoRic.includes('CP') ||
      tipoRic.includes('IE') ||
      tipoRic.includes('AL') ||
      tipoRic.includes('OR') ||
      tipoRic.includes('PA') ||
      tipoRic.includes('CA') ||
      tipoRic.includes('EP')
    ) {
      if (
        tipoRic.includes('RA') ||
        tipoRic.includes('MR') ||
        tipoRic.includes('AL') ||
        tipoRic.includes('OR')
      ) {
        for (const p of pm) {
          const parte = this.leggiParte(p);
          const tipoTab = parte.TIPOTAB;
          const tipoFig = parte.TIPOFIG;
          const dataNasc = parte.DATANASC;
          const luogoNasc = parte.LUOGONASC;
          const cognome = this.capitalize(parte.COGNOME);
          const nome = this.capitalize(parte.NOME);
          let denominazione =
            tipoTab === 'CONTROPARTE'
              ? nome
              : cognome + (nome ? ' ' + nome : '');

          if (tipoFig.includes('PG')) {
            if (corteAppPG !== '') {
              denominazione += ' presso ' + corteAppPG;
            }
          } else if (tipoFig.includes('PMT') || tipoFig.includes('PMP')) {
            // se PMP o PMT
            if (tribunalePM !== '') {
              denominazione += ' ' + tribunalePM;
            }
          }

          if (tipoFig.includes('PMT') || tipoFig.includes('FIG')) {
            PmPiuC = 'PMT';
            pContro += denominazione;
            if (luogoNasc != null || dataNasc != null) pContro += ' nato';

            if (luogoNasc != null) {
              pContro += ' a ' + luogoNasc;
            }
            if (dataNasc != null) {
              pContro += ' il ' + dataNasc;
            }

            pContro += '\n';
          } else {
            pContro += denominazione;

            if (luogoNasc != null || dataNasc != null) pContro += ' nato';

            if (luogoNasc != null) {
              pContro += ' a ' + luogoNasc;
            }
            if (dataNasc != null) {
              pContro += ' il ' + dataNasc;
            }

            pContro += ' parte offesa nel procedimento\n';
          }
        }
        if (PmPiuC.includes('PMT')) {
          if (nonRicorrenti.length > 0) {
            pContro += 'nel procedimento a carico di:';
          } else {
            pContro += 'nei confronti di:\n';
          }
        } else if (pContro !== '') {
          pContro += 'c/\n';
        }
      } else {
        for (const p of pm) {
          const parte = this.leggiParte(p);
          const tipoTab = parte.TIPOTAB;
          const tipoFig = parte.TIPOFIG;
          const dataNasc = parte.DATANASC;
          const luogoNasc = parte.LUOGONASC;
          const cognome = this.capitalize(parte.COGNOME);
          const nome = this.capitalize(parte.NOME);
          let denominazione =
            tipoTab === 'CONTROPARTE'
              ? nome
              : cognome + (nome ? ' ' + nome : '');

          if (tipoFig.includes('PG')) {
            if (corteAppPG !== '') {
              denominazione += ' presso ' + corteAppPG;
            }
          } else if (tipoFig.includes('PMT') || tipoFig.includes('PMP')) {
            // se PMP o PMT
            if (tribunalePM !== '') {
              denominazione += ' ' + tribunalePM;
            }
          }

          if (tipoFig.includes('PC')) {
            pContro += 'dalla parte civile ';
          }
          pContro += denominazione;
          if (luogoNasc != null || dataNasc != null) pContro += ' nato';
          if (luogoNasc != null) {
            pContro += ' a ' + luogoNasc;
          }
          if (dataNasc) {
            pContro += ' il ' + dataNasc;
          }

          pContro += '\n';
        }

        if (pContro !== '') {
          if (!tipoRic.includes('PA')) {
            if (
              ricorrenti.length > 0 ||
              (nonRicorrenti != null && nonRicorrenti.length > 0) ||
              (nonRicorrenti.length == 0 &&
                ricorrenti.length == 0 &&
                partiContro.length > 0)
            ) {
              pContro += 'nel procedimento a carico di:\n';
            } else {
              pContro += 'nei confronti di:\n';
            }
          }
        }
      }

      noRic += this.readParti(partiContro);

      if (nonRicorrenti.length > 0) {
        noRic += 'inoltre:\n';
      }
    } else {
      if (tipoRic.includes('CC') || tipoRic.includes('CG')) {
        if (tipoRic.includes('CC')) {
          finalString = 'sul conflitto di competenza sollevato da:\n';
        } else {
          finalString = 'sul conflitto di giurisdizione sollevato da:\n';
        }
        ricor += this.readParti(ricorrenti);
        noRic += this.readParti(partiContro);
      } else if (tipoRic.includes('IZ')) {
        finalString = 'sulla rogatoria internazionale\n';
        ricor += this.readParti(ricorrenti);
        noRic += this.readParti(partiContro);
      } else {
        if (tipoRic.includes('RR')) {
          finalString = 'vista la richiesta di rimessione proposta da:\n';
          for (const p of pm) {
            const parte = this.leggiParte(p);
            const nome = parte.NOME;
            const tipoFig = parte.TIPOFIG;
            if (tipoFig.includes('PG')) {
              pContro += nome + '\n';
            }
            if (pContro !== '') {
              if (nonRicorrenti.length > 1) {
                pContro += 'nel procedimento a carico di:';
              }
            }
          }
          pContro += this.readParti(partiContro);
        }
      }
    }
    if (
      tipoRic.includes('CC') ||
      tipoRic.includes('CG') ||
      tipoRic.includes('IZ')
    ) {
      // non faccio nulla in quanto già inseriti nelle parti contro
    } else {
      ricor += this.readParti(ricorrenti);
      if (ricorrenti.length > 0 && (partiContro.length > 0 || pContro !== '')) {
        ricor += 'nel procedimento a carico ';
        if (ricorrenti.length == 1) {
          ricor += "di quest'ultimo" + '\n\n';
        } else if (ricorrenti.length > 1) {
          ricor += 'di questi ultimi' + '\n\n';
        }
      }
    }

    // Non Ricorrenti
    noRic += this.readParti(nonRicorrenti);
    finalString += pContro;
    finalString += ricor;
    finalString += noRic;
    return finalString;
  }

  formatCorteApp(string: string) {
    const words = string.toLowerCase().split(' ');
    words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
    words[words.length - 1] =
      words[words.length - 1].charAt(0).toUpperCase() +
      words[words.length - 1].slice(1);
    return words.join(' ');
  }

  readParti(parti: Parte[]) {
    let finalString = '';
    for (const par of parti) {
      const parte = this.leggiParte(par);
      const dataNasc = parte.DATANASC;
      const luogoNasc = parte.LUOGONASC;
      const cognome = this.capitalize(parte.COGNOME);
      const nome = this.capitalize(parte.NOME);
      finalString += cognome + (nome ? ' ' + nome : '');

      if (luogoNasc != null || dataNasc != null) finalString += ' nato';
      if (luogoNasc != null) {
        finalString += ' a ' + luogoNasc;
      }
      if (dataNasc != null) {
        finalString += ' il ' + dataNasc;
      }

      finalString += '\n';
    }
    return finalString;
  }

  async getPresidente(idUdienza: number, nrg: number) {
    const collegio = await this.connection
      .getRepository(PenaleCollegioEntity)
      .findBy({ idUdienza: idUdienza });
    for (const m of collegio) {
      const mag = (await this.connection
        .getRepository(PenaleTMagisEntity)
        .findOne({
          where: { idMagis: m.idMagis },
        })) as PenaleTMagis;
      mag.anagraficaMagistrato = (await this.connection
        .getRepository(PenaleAnagmagisEntity)
        .findOneBy({ idAnagmagis: mag.idAnagmagis })) as PenaleAnagmagis;
      mag.anagraficaMagistrato.nome += ' ' + mag.anagraficaMagistrato.cognome;
      if (m.tipoMag === 'PRE') {
        return { name: mag.anagraficaMagistrato.nome, id: m.idMagis };
      }
    }
    return { name: '' };
  }

  async getEstensore(id: number | undefined, nrg: number, idUdien: number) {
    try {
      const idEstensoreMagis = await this.getIdEstensore(nrg, idUdien);
      if (id === idEstensoreMagis) return '';
      const mag = (await this.connection
        .getRepository(PenaleTMagisEntity)
        .findOne({
          where: { idMagis: idEstensoreMagis },
        })) as PenaleTMagis;
      mag.anagraficaMagistrato = (await this.connection
        .getRepository(PenaleAnagmagisEntity)
        .findOneBy({ idAnagmagis: mag.idAnagmagis })) as PenaleAnagmagis;
      mag.anagraficaMagistrato.nome += ' ' + mag.anagraficaMagistrato.cognome;
      return mag.anagraficaMagistrato.nome;
    } catch (e) {
      console.log(e);
    }
  }

  private async getIdEstensore(nrg: number, idUdien: number) {
    const getIdEstensore = await this.connection
      .getRepository(PenaleCollegioEntity)
      .createQueryBuilder('coll')
      .distinct(true)
      .select('TSET.ID_ESTENSORE')
      .innerJoinAndMapMany(
        'coll.magistrati',
        PenaleTMagisEntity,
        'magis',
        'coll.idMagis = magis.idMagis',
      )
      .innerJoinAndMapMany(
        'coll.magistratiAnagrafica',
        PenaleAnagmagisEntity,
        'anag',
        'anag.idAnagmagis = magis.idAnagmagis',
      )
      .innerJoinAndMapMany(
        'coll.ricorsoUdienza',
        PenaleTRicorsoUdienzaEntity,
        'PTR',
        'coll.idUdienza = PTR.idUdienza',
      )
      .innerJoinAndMapMany(
        'coll.esito',
        PenaleTEsitoEntity,
        'PTE',
        'PTE.ID_RICUDIEN = PTR.ID_RICUDIEN',
      )
      .innerJoinAndMapMany(
        'coll.esitoSent',
        PenaleTEsitoSentEntity,
        'PTS',
        'PTE.ID_ESITO = PTS.ID_ESITO',
      )
      .innerJoinAndMapMany(
        'coll.sentenza',
        PenaleTSentenzaEntity,
        'TSET',
        'TSET.ID_SENT = PTS.ID_SENT',
      )
      .where('TSET.ID_ESTENSORE = magis.ID_MAGIS AND PTR.nrg = :nrg ' +
          'AND PTR.idUdienza = :idUdien', {
        nrg: nrg,
        idUdien: idUdien
      })
      .getRawOne();
    const idEstensoreMagis = getIdEstensore.ID_ESTENSORE as number;
    return idEstensoreMagis;
  }

  async dataDecisione(idProvv: string) {
    if (!idProvv) {
      return (
        '\nCosì è deciso, ' + moment(new Date()).format('DD/MM/YYYY') + '\n'
      );
    }
    const dataDecisione = await this.connection.query(
      "select TO_CHAR(DATA_DECISIONE,'dd/mm/yyyy') AS DATA_DECISIONE from PENALE_PROVVEDIMENTI PPE where    PPE.ID_PROVV =  :idProvv ",
      [idProvv],
    );
    const data = dataDecisione[0];
    return '\nCosì è deciso, ' + data.DATA_DECISIONE.toString() + '\n';
  }

  async epigrafe(idUdienza: number, nrg: number) {
    let idUdien = idUdienza;
    if (!idUdienza) {
      const ric = await this.connection
        .getRepository(PenaleTRicorsoUdienzaEntity)
        .findOneBy({ nrg: nrg });
      idUdien = ric?.idUdienza ?? idUdienza;
    }

    const { name: pres, id } = await this.getPresidente(idUdien, nrg);
    const estensore = await this.getEstensore(id, nrg,  idUdien);

    const finalEpigrafe = [];

    if (estensore) {
      finalEpigrafe.push(
        {
          colonna1: 'Il Consigliere estensore',
          colonna2: 'Il Presidente',
        },
        {
          colonna1: estensore,
          colonna2: pres,
        },
      );
    } else {
      finalEpigrafe.push(
        {
          colonna1: 'Il Presidente',
          colonna2: '',
        },
        {
          colonna1: pres,
          colonna2: '',
        },
      );
    }

    return finalEpigrafe;
  }

  leggiParte(parte: Parte) {
    const p: Parte = { ...parte };
    if (parte.TIPOTAB === 'CONTROPARTE') {
      p.NOME = parte.DESCRIZIONE;
    } else if (p.NAZIONE) {
      p.LUOGONASC += ' (' + p.NAZIONE + ')';
    }
    return p;
  }

  capitalize(name: string) {
    if (name) {
      return name
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    return name;
  }
}
