export class UtilQuery {
  static allProvvedimentiTemineDepositoQuery: string =
    ' SELECT' +
    ' cu.ID_UDIEN AS idUdien,' +
    ' ric.nrgreale,' +
    ' dp.FK_IDCAT,' +
    ' cu.dataUd,' +
    ' dp.DATA_DEPOSITO,' +
    ' CASE' +
    " WHEN dp.STATO= 'MINUTA_DA_MODIFICARE' THEN (SELECT DATE_CHANGE FROM ( SELECT DATE_CHANGE  FROM PENALE_PROVV_CHANGE_STATUS ppcs WHERE ID_PROVV = dp.ID_PROVV and STATO='MINUTA_DA_MODIFICARE' ORDER By DATE_CHANGE DESC) WHERE  ROWNUM = 1) + 30" +
    " WHEN dp.STATO = 'MINUTA_MODIFICATA' THEN (SELECT DATE_CHANGE FROM ( SELECT DATE_CHANGE  FROM PENALE_PROVV_CHANGE_STATUS ppcs WHERE ID_PROVV = dp.ID_PROVV and STATO='MINUTA_MODIFICATA' ORDER By DATE_CHANGE DESC) WHERE  ROWNUM = 1) + 30" +
    ' WHEN dp.FK_IDCAT IS NULL THEN cu.dataUd + 30' +
    ' ELSE dp.DATA_DEPOSITO + 90' +
    ' END AS dataScadenza,' +
    ' dp.STATO  AS statoProv,' +
    ' dp.ID_PROVV AS idProvv,' +
    ' cu.ID_TIPOUD  AS tipoUd,' +
    ' (' +
    ' SELECT' +
    ' SIGLA' +
    ' FROM' +
    ' PENALE_PARAM' +
    ' WHERE' +
    ' ID_PARAM = cu.ID_SEZIONE) AS sezione,' +
    ' (' +
    ' SELECT SIGLA FROM PENALE_PARAM WHERE ID_PARAM = cu.ID_AULA) AS aula,' +
    ' cr.NUMORD AS numChiamata,' +
    ' cr.nrg,' +
    " TO_NUMBER(LTRIM (SUBSTR (ric.nrgreale, 5, 6), '0')) AS numero," +
    ' substr(ric.NRGREALE, 0, 4) AS anno,' +
    ' dp.STATO AS statoProvvLav' +
    ' FROM' +
    ' PENALE_RICUDIEN cr' +
    ' JOIN PENALE_T_UDIENZA cu ON' +
    ' cu.ID_UDIEN = cr.ID_UDIEN' +
    ' JOIN PENALE_RICORSO ric ON' +
    ' cr.NRG = ric.NRG' +
    ' LEFT JOIN PENALE_PROVVEDIMENTI dp ON' +
    ' dp.NRG = ric.NRG' +
    ' LEFT JOIN PENALE_T_MAGIS pt on( pt.ID_MAGIS  = cr.ID_RELATORE) ' +
    ' LEFT JOIN PENALE_ANAGMAGIS pa on( pa.ID_ANAGMAGIS = pt.ID_ANAGMAGIS) ' +
    " LEFT JOIN PENALE_COLLEGIO pc  on( pc.ID_UDIEN  = cr.ID_UDIEN AND pc.TIPOMAG= 'PRE')" +
    ' LEFT JOIN PENALE_T_MAGIS pt2 on( pt2.ID_MAGIS  = pc.ID_MAGIS) ' +
    ' LEFT JOIN PENALE_ANAGMAGIS pa2 ON ( pa2.ID_ANAGMAGIS = pt2.ID_ANAGMAGIS) ' +
    ' WHERE(' +
    ' pa.CODICE_FISCALE = :cf' +
    ' OR Pa2.CODICE_FISCALE = :cf)';
  static getallStatusProvv =
    'SELECT ppcs.* FROM PENALE_PROVV_CHANGE_STATUS ppcs WHERE ppcs.ID_PROVV IN (SELECT *  FROM TABLE(get_provvedimenti_collegati(:idProvv))) ORDER BY ppcs.DATE_CHANGE DESC';
  static getallStatusProvvCountWithStatus =
    'SELECT count(*) as countElement FROM PENALE_PROVV_CHANGE_STATUS ppcs WHERE ppcs.ID_PROVV IN (SELECT *  FROM TABLE(get_provvedimenti_collegati(:idProvv))) AND  PPCS.STATO IN (:statoSearch1, :statoSearch2, :statoSearch3) ORDER BY ppcs.DATE_CHANGE DESC';
  static allProvvedimentiTemineDepositoWithPeriodQuery: string =
    'SELECT' +
    ' *' +
    ' FROM' +
    ' ( ' +
    UtilQuery.allProvvedimentiTemineDepositoQuery +
    ' ) ' +
    'WHERE ' +
    " trunc(dataScadenza) BETWEEN TO_DATE(:START,  'DD/MM/YYYY') AND TO_DATE(:END,  'DD/MM/YYYY')";

  static queryPerCercareNellaDataUdienzaNRGECt =
    'SELECT *  FROM (SELECT ' +
    '    DISTINCT  ' +
    '    "UDIE"."ID_UDIEN" AS ID_UDIENZA, "RIC"."NRG" AS nrg, "UDIE"."DATAUD" AS DATA_UD, \' \' || (SELECT pp.DESCRIZIONE FROM PENALE_PARAM pp WHERE pp.ID_PARAM  = UDIE.ID_SEZIONE)  ' +
    "    ||' ' || (SELECT pp.DESCRIZIONE FROM PENALE_PARAM pp WHERE pp.ID_PARAM  = UDIE.ID_TIPOUD)  || ' ' || " +
    '    (SELECT pp.DESCRIZIONE FROM PENALE_PARAM pp WHERE pp.ID_PARAM  = UDIE.ID_AULA)  ' +
    "    || ' ' || RIC.NRG || ' ' || TO_CHAR(UDIE.DATAUD, 'DD-MM-YYYY') || ' ' || TO_NUMBER(LTRIM (SUBSTR (ric.nrgreale, 5, 6), '0')) || '/' " +
    '   || substr(ric.NRGREALE, 0, 4) AS RICERCA ' +
    'FROM ' +
    '    "PENALE_T_RICORSO" "RIC" ' +
    'INNER JOIN "PENALE_T_RICUDIEN" "RICUDIEN" ON ' +
    '    "RIC"."NRG" = "RICUDIEN"."NRG" ' +
    'INNER JOIN "PENALE_T_UDIENZA" "UDIE" ON ' +
    '    "RICUDIEN"."ID_UDIEN" = "UDIE"."ID_UDIEN" ' +
    'WHERE pn.nrg = RIC.nrg) ' +
    'WHERE UPPER(RICERCA) LIKE UPPER(:term))';
}
