import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('UFFICI') //nome tabella su schema oracle
export class PenaleUfficioEntity {
  @PrimaryColumn({ name: 'CODICE_DISTRETTO' })
  codiceDistretto: string;

  @Column({ name: 'DESC_DISTRETTO' })
  descDistretto: string;

  @Column({ name: 'CODICE_UFFICIO' })
  codiceUfficio: string;
  @Column({ name: 'DESC_UFFICIO' })
  descrizioneUfficio: string;
  @Column({ name: 'CITTA' })
  citta: string;
  @Column({ name: 'PROVINCIA' })
  provincia: string;
  @Column({ name: 'CODICE_GL' })
  codiceGL: string;
}
