import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_VERBALE_UDIENZA') //nome tabella su schema oracle
export class PenaleVerbaleUdienzaEntity {
  @PrimaryColumn({ name: 'ID_VERBALE_UD' })
  idVerbaleUdienza: number;

  @Column({ name: 'ID_UDIENZA' })
  idUdienza: number;

  @Column({
    name: 'TESTO_DI_APERTURA',
    type: 'clob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  testoApertura: string;
  @Column({
    name: 'TESTO_DI_CHIUSURA',
    type: 'clob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  testoChiusura: string;
}
