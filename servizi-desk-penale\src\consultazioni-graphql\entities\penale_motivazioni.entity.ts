import { Entity, PrimaryColumn, Column } from 'typeorm';

@Entity('PENALE_MOTIVAZIONI')
export class PenaleMotivazioniEntity {
  @PrimaryColumn({
    name: 'ID_MOTIVAZIONE',
    type: 'number',
    precision: 12,
    scale: 0,
  })
  idMotivazione: number;

  @Column({ name: 'ID_RICUDIEN', type: 'number', precision: 12, scale: 0 })
  idRicudien: number;

  @Column({ name: 'PROPOSTA', type: 'varchar2', length: 4000, nullable: true })
  proposta: string | null;

  @Column({
    name: 'PARTI_CONTRO',
    type: 'varchar2',
    length: 4000,
    nullable: true,
  })
  partiContro: string | null;

  @Column({ name: 'RICORRENTI', type: 'clob', nullable: true })
  ricorrenti: string | null;

  @Column({
    name: 'NON_RICORRENTI',
    type: 'varchar2',
    length: 4000,
    nullable: true,
  })
  nonRicorrenti: string | null;

  @Column({
    name: 'PROVVEDIMENTO',
    type: 'varchar2',
    length: 4000,
    nullable: true,
  })
  provvedimento: string | null;

  @Column({ name: 'ATTI', type: 'varchar2', length: 4000, nullable: true })
  atti: string | null;

  @Column({
    name: 'INTRODUZIONE',
    type: 'varchar2',
    length: 4000,
    nullable: true,
  })
  introduzione: string | null;

  @Column({ name: 'MOTIVAZIONI', type: 'clob', nullable: true })
  motivazioni: string | null;

  @Column({ name: 'FINALE', type: 'clob', nullable: true })
  finale: string | null;

  @Column({ name: 'TESTO_PQM', type: 'varchar2', length: 4000, nullable: true })
  testoPqm: string | null;

  @Column({ name: 'OPERATORE', type: 'number', precision: 12, scale: 0 })
  operatore: number;

  @Column({ name: 'DATA_INSERIMENTO', type: 'date' })
  dataInserimento: Date;

  @Column({ name: 'DATA_AGGIORNAMENTO', type: 'date', nullable: true })
  dataAggiornamento: Date | null;

  @Column({
    name: 'NOTE_DIFENSORI',
    type: 'varchar2',
    length: 4000,
    nullable: true,
  })
  noteDifensori: string | null;
}
