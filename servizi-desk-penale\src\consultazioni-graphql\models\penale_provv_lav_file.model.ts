import { Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PenaleProvvLavFile {
  @Field(type => ID)
  idCategoria: string;

  @Field(type => String)
  idProvvedimento: string;
  @Field(type => String)
  nomeFile: string;

  @Field(type => String)
  mimeType: string;
  @Field(type => Boolean)
  signed: boolean;
  @Field(type => String)
  tipoFile: string;
  @Field(type => Date)
  createAt: Date;

  @Field(type => Boolean)
  oscurato: boolean;
  public constructor(init?: Partial<PenaleProvvLavFile>) {
    Object.assign(this, init);
  }
}
