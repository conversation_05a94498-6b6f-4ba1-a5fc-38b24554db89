import {
  BeforeInse<PERSON>,
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm';
import { ProvvedimentoLavorazioneEntity } from './provvedimentoLavorazione.entity';
import { v4 as uuid4 } from 'uuid';
import ColumnBooleanTransformer from '../../consultazioni-graphql/entities/utility/column-boolean-transformer';

@Entity({ name: 'PROVV_LAV_FILE' })
export class ProvvLavFileEntity {
  @PrimaryColumn({ name: 'IDCAT' })
  idCat: string;

  @BeforeInsert()
  generateUuid() {
    this.idCat = uuid4().replace(/-/g, '');
  }

  @Column({ name: 'IDPROVV' })
  idProvv: string;

  @Column({ name: 'NOMEFILE' })
  nomeFile: string;

  @Column({ name: 'CONTENT', type: 'blob' })
  content: Buffer;

  @Column({ name: 'MIMETYPE' })
  mimeType: string;

  @Column({ name: 'SIGNED' })
  signed: number;

  @Column({ name: 'TIPOFILE' })
  tipoFile: string;
  @Column({
    name: 'OSCURATO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  oscurato: boolean;

  @ManyToOne(() => ProvvedimentoLavorazioneEntity)
  @JoinColumn({ name: 'IDPROVV', referencedColumnName: 'idProvvLav' })
  provv: ProvvedimentoLavorazioneEntity;

  public toString = (): string => {
    return `${JSON.stringify(this)}`;
  };
}
