import {
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Logger,
  Param,
  Post,
  StreamableFile,
  Query,
} from '@nestjs/common';
import { PresidenteDto } from './entities/dto/presidente-dto';
import { ProvvedimentoPresidenteService } from './provvedimento-presidente.service';
import { ProvvLavorazioneService } from './provvLavorazione.service';
import { AuthService } from '../auth/auth.service';
import { ProvvedimentiService } from './provvedimenti.service';
import {
  DownloadCodaDepositoInput,
  FirmaPresidenteInput,
} from './entities/dto/firma-provvLavorazione.input';
import { Utils } from '../utils/utils';
import { ProvvedimentoNotFoundException } from '../exceptions/provvedimento-not-found.exception';
import { PenaleProvvedimenti } from 'src/consultazioni-graphql/models/penale_provvedimenti.model';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import {FirmaRemotaError2Exception} from "../exceptions/firma-remota-error2.exception";
import { Log } from 'src/decorators/log.decorator';

@Controller('presidente')
@ApiBearerAuth('access-token')
@ApiTags('provvedimenti-presidente')
export class ProvvedimentoPresidenteController {
  private logger = new Logger(ProvvedimentoPresidenteController.name);

  constructor(
    private readonly provvLavorazService: ProvvLavorazioneService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly authService: AuthService,
    private readonly provvedimentoPresidenteService: ProvvedimentoPresidenteService,
  ) {}

  @Get('/verifica/:idProvv')
  async getPdfByIdProvv(
    @Param('idProvv') idProvv: string,
  ): Promise<StreamableFile | undefined> {
    const provv =
      await this.provvedimentoPresidenteService.getProvvLavorazioneByIdProvv(
        idProvv,
      );
    if (provv?.idProvvedimento) {
      const file =
        await this.provvLavorazService.getFileProvvedimentoPDFNonOscurato(
          provv?.idProvvedimento,
        );
      if (file) {
        this.logger.debug(
          `Download file per il provvedimento con  idProvv: ${idProvv}`,
        );
        return new StreamableFile(file?.content, { type: 'application/pdf' });
      }
    }
    this.logger.warn(`Provvedimento o file non trovato. idProvv: ${idProvv}`);
    throw new InternalServerErrorException(`Provvedimento o file non trovato.`);
  }

  @Post('/richiestaModifica/:idProvv')
  async richiestaModifica(
    @Param('idProvv') idProvv: string,
    @Body() presidenteDto: PresidenteDto,
  ) {
    const idAutore = await this.authService.getCurrentId();
    const provv =
      await this.provvedimentoPresidenteService.getProvvLavorazioneByIdProvv(
        idProvv,
      );
    if (provv) {
      await this.provvedimentoPresidenteService.checkOperation(provv);
      if (Utils.isOrdinanzaOSentenza(provv.tipo)) {
        // sono nel caso che sto facendo una richiesta di modifica di ordinanza o sentenza
        return await this.provvedimentoPresidenteService.richiestaModificaOrdinanzaOSentenza(
          provv,
          idAutore,
          presidenteDto.note ?? '',
        );
      }
      // sono nel caso di richiesta di modifica di una minuta
      this.logger.log(
        `Provvedimento richiesta modifica. idProvv: ${idProvv} idAutore: ${idAutore}`,
      );
      return await this.provvedimentoPresidenteService.richiestaModifica(
        provv,
        idAutore,
        presidenteDto.tipoModifica,
        presidenteDto.note ?? '',
      );
    }
    this.logger.warn(`Provvedimento non trovato. idProvv: ${idProvv}`);
    throw new ProvvedimentoNotFoundException('Provvedimento non trovato');
  }

  @Post('/verificato/:idProvv')
  async verificaProvvedimento(
    @Param('idProvv') idProvv: string,
    @Query('disableWarn') disableWarn?: boolean,
  ) {
    await this.verificaProvv(idProvv, disableWarn);
  }

  @Post('/verificaTutti')
  async verificaTutti(@Body() inputProvvDaVerificare: string[]) {
    const provvVerificati = new Array<PenaleProvvedimenti>();
    for (let i = 0; i < inputProvvDaVerificare.length; i++) {
      const idProvv = inputProvvDaVerificare[i];
      try {
        const provv = await this.verificaProvv(idProvv);
        provvVerificati.push(provv);
      } catch (e) {
        this.logger.warn(`Errore nella verifica del provvedimento: ${idProvv}`);
      }
    }
    return provvVerificati;
  }

  @Post('/firmaEDeposita')
  async firmaEDepositaPresidente(
    @Body() inputCodaDeposito: FirmaPresidenteInput,
  ) {
    this.logger.log(
      `Inizio firma e deposita del provvedimento, ${inputCodaDeposito?.daDepositare}`,
    );

    try {
      const currentUser = await this.authService.getCurrentUser();
      const resultList = await this.provvedimentoPresidenteService.firmaCodeDiDeposito(
        currentUser,
        inputCodaDeposito,
      );

      this.logger.log(`Fine firma e deposita del provvedimento`);
      return resultList;
    } catch (e) {
      this.logger.error('Errore nella firma della coda di deposito:', e);
      throw this.handleFirmaError(e);
    }
  }

  private handleFirmaError(error: any): FirmaRemotaError2Exception {
    const message = error.message || '';

    // Gestione specifica per PDF corrotto (codice 134)
    if (message.includes('Codice:  134')) {
      return new FirmaRemotaError2Exception(
        'Verificare integrità dei file allegati del deposito - Contattare il CED',
        CodeErrorEnumException.PDF_CORRUPTED_SIGN_ERROR,
      );
    }

    // Gestione per altri errori di firma
    if (message.includes('SignatureException')) {
      const errorMessage = this.extractErrorMessage(message) || 'Errore durante la firma digitale';
      return new FirmaRemotaError2Exception(
        `${errorMessage} - Contattare il CED`,
        CodeErrorEnumException.FIRMA_ERROR,
      );
    }

    // Gestione generica
    const errorMessage = this.extractErrorMessage(message) || 'Errore durante la firma e deposito';
    return new FirmaRemotaError2Exception(
      `${errorMessage} - Contattare il CED`,
      CodeErrorEnumException.FIRMA_ERROR,
    );
  }

  private extractErrorMessage(message: string): string | null {
    if (!message) return null;

    const dashIndex = message.indexOf('-');
    if (dashIndex >= 0) {
      return message.substring(dashIndex + 1).trim();
    }

    return message;
  }

  @Post('/downloadCodaDeposito')
  async downloadCodaDeposito(
    @Body() inputDownloadCodaDeposito: DownloadCodaDepositoInput,
  ) {
    const downloadCodaDiDeposito =
      await this.provvedimentoPresidenteService.downloadCodaDeposito(
        inputDownloadCodaDeposito,
      );
    this.logger.log(
      `Download coda di deposito per i provvedimenti: ${inputDownloadCodaDeposito}`,
    );
    return downloadCodaDiDeposito;
  }

  @Delete('/provvedimentoCodaDeposita/:idProvv')
  async provvedimentoCodaDeposita(@Param('idProvv') idProvv: string) {
    const currentUser = await this.authService.getCurrentUser();
    if (currentUser && idProvv) {
      return this.provvedimentoPresidenteService.deleteProvvedimento(
        idProvv,
        currentUser,
      );
    }
    this.logger.warn(
      `Provvedimento non trovato. idProvv: ${idProvv} o utente non loggato`,
    );
    throw new InternalServerErrorException(
      'Identificativo provvedimento non valorizzato o utente non loggato.',
    );
  }

  private async verificaProvv(idProvv: string, disableWarn?: boolean) {
    this.logger.log(
      `Richiesta di verifica per provvedimento idProvv:${idProvv} ${
        disableWarn ? 'i messaggi di warning non saranno resituiti' : ''
      }`,
    );
    const provv =
      await this.provvedimentoPresidenteService.getProvvLavorazioneByIdProvv(
        idProvv,
      );
    if (provv) {
      const check = await this.provvedimentoPresidenteService.checkOperation(
        provv,
        disableWarn,
      );
      //Nel caso in cui è stato indicato di nascondere i warning e il provvvedimento risulta essere già lavorato
      // (controllo non passato - check == false), non viene restituita l'eccezione
      if (disableWarn && !check) {
        return provv;
      }
      await this.provvedimentoPresidenteService.aggiugiAllaCodaDiFirma(
        idProvv,
        await this.authService.getCurrentUser(),
        await this.authService.getCurrentId(),
      );
      this.logger.log(`Provvedimento verificato. idProvv: ${idProvv}`);
      return provv;
    }
    this.logger.warn(`Provvedimento non trovato. idProvv: ${idProvv}`);
    throw new GenericErrorException(
      'Provvedimento non trovato',
      CodeErrorEnumException.PROVV_NOT_FOUND,
    );
  }
}
