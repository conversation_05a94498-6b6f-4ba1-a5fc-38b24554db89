import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
  Scope,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Reflector, REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { TokenExpiredException } from '../exceptions/TokenExpiredException';
import { Utils } from '../../utils/utils';
import { HEADER_COD_FISCALE } from '../../constants';
import { UtentiQueryAuthService } from '../../utenti-auth/utenti-query-auth.service';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedCustomException } from '../../exceptions/unauthorized-custom.exception';
import { MagistratoNotFoundException } from '../../exceptions/magistrato-not-found.exception';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const jwksClient = require('jwks-rsa');
const scopeDeskCassp : string= 'deskcassp';
const client = jwksClient({
  jwksUri: process.env.jwksUri,
  requestHeaders: {}, // Optional
  timeout: 500000, // Defaults to 30s
});

@Injectable({ scope: Scope.REQUEST })
export class AuthGuard implements CanActivate {
  private logger = new Logger(AuthGuard.name);

  constructor(
    @Inject(REQUEST)
    private readonly request: Request,
    private jwtService: JwtService,
    private reflector: Reflector,
    private readonly utentiQueryAuthService: UtentiQueryAuthService,
    private configService: ConfigService,
  ) {}

  async getPublicKey() {
    const key = await client.getSigningKey();
    const signingKey = key.getPublicKey();
    return signingKey;
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      // 💡 See this condition
      return true;
    }

    const isDebug = this.configService.get('app.isDebug') as string;
    const header = this.request.headers[HEADER_COD_FISCALE] as string;
    if (Utils.isBoolean(isDebug)) {
      await this.checkFiscalCode(header);
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request || this.request);
    if (!token) {
      this.logger.warn("token non valorizzato nell'header");
      throw new UnauthorizedException('CODE_UNAUTHORIZED');
    }

    try {
      const payload: any = this.jwtService.decode(token);
      console.log('payload', payload);
      const publicKey = await this.getPublicKey();
      //console.log('publicKey', publicKey);
      const promise = await this.jwtService.verifyAsync(token, {
        publicKey: publicKey,
      });
      // controllo sullo scope di validaziono del token richiesto da MS
      if (!payload.scp?.includes(scopeDeskCassp)) return false;
    } catch (e) {
      if (e?.name == 'TokenExpiredError') {
        this.logger.warn('Il token è scaduto');
        throw new TokenExpiredException('CODE_TOKEN_EXPIRED');
      } else if (e?.name == 'JsonWebTokenError') {
        this.logger.error('Il token non è valido', e);
        throw new UnauthorizedException('CODE_UNAUTHORIZED');
      }
    }
    return true;
  }

  private async checkFiscalCode(fiscalCode: string) {
    const magisByCf = await this.utentiQueryAuthService.getMagisByCf(
      fiscalCode,
    );
    if (!magisByCf) {
      this.logger.log('Magistrato non registrato nel sic penale');
      throw new MagistratoNotFoundException(
        "Magistrato non registrato nel sic penale'",
      );
    }
    const utenteByCf = await this.utentiQueryAuthService.getUtenteByCf(
      fiscalCode,
    );
    if (!utenteByCf) {
      this.logger.log('Utente non registrato nel sic penale');
      throw new UnauthorizedCustomException(
        'Utente non registrato nel sic penale',
        401,
      );
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request?.headers.authorization?.split(' ') ?? [];
    if (type && token) {
      return type === 'Bearer' ? token : undefined;
    }
    return undefined;
  }
}
