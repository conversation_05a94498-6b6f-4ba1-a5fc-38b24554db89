import { Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { DataSource, EntityManager, In } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleProvvChangeStatusEntity } from '../consultazioni-graphql/entities/penale_provv_change_status.entity';
import { CreateProvvedimentiChangeStatusInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-change-status.input';
import { AuthService } from '../auth/auth.service';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { UtilQuery } from '../utils/util-query';

@UfficioService()
export class ProvvedimentoChangeStatusService {
  private logger = new Logger(ProvvedimentoChangeStatusService.name);

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly authService: AuthService,
  ) {}

  changeStatus(): Promise<PenaleProvvChangeStatusEntity[]> {
    this.logger.log(`lista di tutti gli stati del provvedimento`);
    return this.connection.getRepository(PenaleProvvChangeStatusEntity).find();
  }

  changeStatusById(
    idProvvedimentoChangeStatus: string,
  ): Promise<PenaleProvvChangeStatusEntity | null> {
    this.logger.log(
      `Stato del provvedimento. idProvvedimentoChangeStatus: ${idProvvedimentoChangeStatus}`,
    );
    return this.connection
      .getRepository(PenaleProvvChangeStatusEntity)
      .findOneBy({ idProvvedimentoChangeStatus: idProvvedimentoChangeStatus });
  }

  async changeStatusByIdProvvedimento(
    idProvvedimento: string,
    nrg: number,
  ): Promise<PenaleProvvChangeStatusEntity[] | null> {
    this.logger.log(
      `Stato del provvedimento. idProvvedimento: ${idProvvedimento}, nrg: ${nrg}`,
    );
    return this.changeStatusByIdsProvvedimento([idProvvedimento], nrg);
  }

  async changeStatusByIdsProvvedimento(
    ids: string[],
    nrg: number,
  ): Promise<PenaleProvvChangeStatusEntity[] | null> {
    this.logger.log(
      `Stato del provvedimenti. idsProvvedimento: ${ids}, nrg: ${nrg}`,
    );
    const isRelatoreDeludienza = await this.authService.isRelatoreforIdUdienza(
      nrg,
    );
    const isEstensoreDeludienza = await this.authService.isEstensoreforNrg(nrg);
    const provvedimentiStatus = await this.connection
      .getRepository(PenaleProvvChangeStatusEntity)
      .find({
        where: { idProvvedimento: In(ids) },
        order: { dateChange: 'ASC' },
      });
    if (isRelatoreDeludienza || isEstensoreDeludienza) {
      return provvedimentiStatus.filter(
        stato => stato.stato != ProvvedimentiStatoEnum.CODA_DI_FIRMA,
      );
    }
    return provvedimentiStatus;
  }

  async createProvvedimentoChangeStatus(
    changeStatus: CreateProvvedimentiChangeStatusInput,
    entityManager?: EntityManager,
  ): Promise<string | null> {
    const idAutore = await this.authService.getCurrentId();
    this.logger.log(
      `Inserimento change status per il provvedimentoId: ${changeStatus.idProvvedimento} , status: ${changeStatus.stato}`,
    );

    if (changeStatus?.idProvvedimento) {
      const changeStatusEntity = new PenaleProvvChangeStatusEntity({
        dateChange: new Date(),
        idAutore: idAutore || 0,
        stato: changeStatus.stato,
        prevStato: changeStatus.prevStato,
        idProvvedimento: changeStatus.idProvvedimento,
      });
      if (entityManager) {
        const penaleProvvChangeStatusEntity = await entityManager
          .getRepository(PenaleProvvChangeStatusEntity)
          .save(changeStatusEntity);
        return penaleProvvChangeStatusEntity.idProvvedimentoChangeStatus;
      }
      const result = await this.connection
        .getRepository(PenaleProvvChangeStatusEntity)
        .save(changeStatusEntity);
      return result.idProvvedimentoChangeStatus;
    }
    this.logger.error(
      `Errore nel change status per il provvedimento, id provvedimento non trovato. provvedimentoId: ${changeStatus.idProvvedimento} , status: ${changeStatus.stato}`,
    );
    throw new InternalServerErrorException('Id provvedimento non valorizzato');
  }

  async changeStatusByIdProvvAndLast(idProvvedimento: string) {
    const promise = await this.connection
      .getRepository(PenaleProvvChangeStatusEntity)
      .findOne({
        where: { idProvvedimento: idProvvedimento },
        order: {
          dateChange: 'DESC',
        },
      });
    this.logger.log(
      `Ultimo stato del provvedimento. provvedimentoId: ${idProvvedimento}, stato:${promise?.stato}, dateChange:${promise?.dateChange}`,
    );
    return promise;
  }
  async changeStatusListByIdProvvAndLast(idProvvedimento: string) {
    const promise = await this.connection
      .getRepository(PenaleProvvChangeStatusEntity)
      .find({
        where: { idProvvedimento: idProvvedimento },
        order: {
          dateChange: 'DESC',
        },
      });
    this.logger.log(
      `Lista dei stati del provvedimento. provvedimentoId: ${idProvvedimento}`,
    );
    return promise;
  }
  async checkIsRevisione(idProvv: string): Promise<boolean> {
    const [queryString, parameters] =
      this.connection.driver.escapeQueryWithParameters(
        UtilQuery.getallStatusProvvCountWithStatus,
        {
          idProvv: idProvv,
          statoSearch1: ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE,
          statoSearch2: ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE,
          statoSearch3: ProvvedimentiStatoEnum.BUSTA_RIFIUTATA,
        },
        {},
      );
    const promise = await this.connection.query(queryString, parameters);

    this.logger.log(`Conto le richieste modifiche. nrg: ${idProvv}`);
    return promise?.length > 0 && parseInt(promise[0].COUNTELEMENT) > 0;
  }
  async deleteByIdProvv(idProvv: string) {
    const changeStatus = await this.changeStatusByIdProvvAndLast(idProvv);
    if (changeStatus?.idProvvedimentoChangeStatus) {
      return await this.connection
        .getRepository(PenaleProvvChangeStatusEntity)
        .delete({
          idProvvedimentoChangeStatus: changeStatus.idProvvedimentoChangeStatus,
        });
    }
    this.logger.log(`Change status del provvedimeto: ${idProvv} non trovato`);
  }
}
