import { NotFoundException } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import { PenaleAnagmagis } from '../models/penale_anagmagis.model';
import { AnagraficaMagistratiService } from '../../anagrafica-magistrati/anagrafica-magistrati.service';

@Resolver(() => PenaleAnagmagis)
export class PenaleAnagmagisResolver {
  constructor(
    private readonly anagraficaMagisService: AnagraficaMagistratiService,
  ) {}

  @Query(() => PenaleAnagmagis, { name: 'anagraficaMagistrato' })
  async anagraficaMagistrato(@Args('id') id: number): Promise<PenaleAnagmagis> {
    const parti = await this.anagraficaMagisService.anagraficaMagistrato(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleAnagmagis], { name: 'anagraficaMagistrati' })
  anagraficaMagistrati(): Promise<PenaleAnagmagis[]> {
    return this.anagraficaMagisService.anagraficaMagistrati();
  }
}
