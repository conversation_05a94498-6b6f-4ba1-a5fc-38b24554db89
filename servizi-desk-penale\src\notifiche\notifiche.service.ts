import { Inject } from '@nestjs/common';
import { DataSource, ILike } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleNoticheEntity } from '../consultazioni-graphql/entities/penale_notiche.entity';
import { CreateNotificheInput } from '../consultazioni-graphql/entities/dto/create-notifiche.input';
import { NotificaTipoEnum } from '../consultazioni-graphql/entities/enumaration/notifica-tipo.enum';
import { PenaleTRicorsoEntity } from '../consultazioni-graphql/entities/penale_t_ricorso.entity';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { PenaleTUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_udienza.entity';
import { PenaleParamEntity } from '../consultazioni-graphql/entities/penale_param.entity';
import { PenaleRicercaRiunitiEntity } from '../consultazioni-graphql/entities/penale_v_ricerca_riuniti.entity';

@UfficioService()
export class NotificheService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  notifiche(
    startOffset: number,
    pageSize: number,
  ): Promise<[PenaleNoticheEntity[], number]> {
    console.log('startOffset: and limit:', startOffset, pageSize);
    return this.connection
      .getRepository(PenaleNoticheEntity)
      .createQueryBuilder(
        'notificheList',
      ) /*.addOrderBy(orderField, sortDirection)*/
      .offset(startOffset)
      .limit(pageSize)
      .getManyAndCount();
  }

  notificheById(idNot: string): Promise<PenaleNoticheEntity | null> {
    return this.connection.getRepository(PenaleNoticheEntity).findOne({
      where: { idNotifica: idNot },
    });
  }

  notificheByIdUtente(
    idUtente: number,
    startOffset: number,
    pageSize: number,
    term: string | undefined | null,
    read: boolean | undefined | null,
  ): Promise<[PenaleNoticheEntity[], number]> {
    //TODO DA modificare usare l'idUdien nella tabella
    if (!term || term.trim() == '') {
      const penaleNoticheEntitySelectQueryBuilder1 = this.connection
        .getRepository(PenaleNoticheEntity)
        .createQueryBuilder('notificheById')
        .leftJoinAndMapOne(
          'notificheById.riunitiView',
          PenaleRicercaRiunitiEntity,
          'riunitiView',
          'riunitiView.ID_RICUDIEN IN (SELECT ricUdien.ID_RICUDIEN from PENALE_T_RICUDIEN ricUdien where notificheById.ID_UDIEN = ricUdien.ID_UDIEN and notificheById.NRG = ricUdien.NRG)',
        )
        .andWhere({
          idUtente: idUtente,
        })
        .andWhere(
          'TRUNC(notificheById.CREATE_AT) > TRUNC(ADD_MONTHS(sysdate, -3))',
        );
      if (read != undefined || read != null) {
        penaleNoticheEntitySelectQueryBuilder1.andWhere({ read: read });
      }
      return penaleNoticheEntitySelectQueryBuilder1
        .orderBy('CREATE_AT', 'DESC')
        .offset(startOffset)
        .limit(pageSize)
        .getManyAndCount();
    }
    const internalQuery = this.connection
      .createQueryBuilder()
      .select(
        '    "UDIE"."ID_UDIEN" AS ID_UDIENZA, "RIC"."NRG" AS nrg, "UDIE"."DATAUD" AS DATA_UD, \' \' || (SELECT pp.DESCRIZIONE FROM PENALE_PARAM pp WHERE pp.ID_PARAM  = UDIE.ID_SEZIONE)  ' +
          "    ||' ' || (SELECT pp.DESCRIZIONE FROM PENALE_PARAM pp WHERE pp.ID_PARAM  = UDIE.ID_TIPOUD)  || ' ' || " +
          '    (SELECT pp.DESCRIZIONE FROM PENALE_PARAM pp WHERE pp.ID_PARAM  = UDIE.ID_AULA)  ' +
          "    || ' ' || RIC.NRG || ' ' || TO_CHAR(UDIE.DATAUD, 'DD-MM-YYYY') || ' ' || TO_NUMBER(LTRIM (SUBSTR (ric.nrgreale, 5, 6), '0')) || '/' " +
          '   || substr(ric.NRGREALE, 0, 4) AS RICERCA',
      )
      .distinct(true)
      .from(PenaleTRicorsoEntity, 'RIC')
      .innerJoin(
        PenaleTRicorsoUdienzaEntity,
        'RICUDIEN',
        'RIC.NRG = RICUDIEN.NRG ',
      )
      .innerJoin(
        PenaleTUdienzaEntity,
        'UDIE',
        'RICUDIEN.ID_UDIEN = UDIE.ID_UDIEN',
      )
      .where(
        'notificheById.nrg = RIC.nrg AND notificheById.idUdienza = RICUDIEN.ID_UDIEN AND notificheById.ID_UTENTE_TO_SEND = :idUtente',
        { idUtente: idUtente },
      );
    const penaleNoticheEntitySelectQueryBuilder = this.connection
      .getRepository(PenaleNoticheEntity)
      .createQueryBuilder('notificheById')
      .andWhere({
        idUtente: idUtente,
      })
      .andWhere(
        'TRUNC(notificheById.CREATE_AT) > TRUNC(ADD_MONTHS(sysdate, -3))',
      )
      .andWhere({ descrizione: ILike(`%${term}%`) })
      .orWhereExists(
        this.connection
          .createQueryBuilder()
          .select('*')
          .from('(' + internalQuery.getQuery() + ')', 'RICER')
          .where('UPPER(RICER.RICERCA) LIKE UPPER(:term)', {
            term: `%${term}%`,
            idUtente: idUtente,
          }),
      );
    if (read != undefined || read != null) {
      penaleNoticheEntitySelectQueryBuilder.andWhere({ read: read });
    }
    const manyAndCount = penaleNoticheEntitySelectQueryBuilder
      .orderBy('CREATE_AT', 'DESC')
      .offset(startOffset)
      .limit(pageSize)
      .getManyAndCount();
    return manyAndCount;
  }

  async createNotifiche(
    not: CreateNotificheInput,
    idAutore: number,
  ): Promise<string | null> {
    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(PenaleNoticheEntity)
      .values({
        dataCreazione: new Date(),
        idUtente: idAutore,
        descrizione: not.descrizione,
        nrg: not.nrg,
        idUdienza: not.idUdien,
        read: false,
        tipo: not.tipo ?? NotificaTipoEnum.PROV,
      })
      .returning(['idNotifica'])
      .execute();
    const x = result.raw[0];
    return x[0];
  }

  async updateNotificaToRead(idNotifica: string) {
    await this.connection
      .createQueryBuilder()
      .update(PenaleNoticheEntity)
      .set({ read: true })
      .where('idNotifica= :idNotifica', { idNotifica: idNotifica })
      .execute();
  }

  async notificheByIdUtenteNotRead(idUtente: number) {
    return this.connection.getRepository(PenaleNoticheEntity).findBy({
      idUtente: idUtente,
      read: false,
    });
  }
  async notificheByIdUtenteTotalElement(idUtente: number) {
    return this.connection
      .getRepository(PenaleNoticheEntity)
      .createQueryBuilder('notificheById')
      .andWhere({
        idUtente: idUtente,
      })
      .andWhere(
        'TRUNC(notificheById.CREATE_AT) > TRUNC(ADD_MONTHS(sysdate, -3))',
      )
      .getCount();
  }

  async notificheNotReadByUser(currentId: number) {
    return await this.connection
      .getRepository(PenaleNoticheEntity)
      .createQueryBuilder('notificheById')
      .andWhere({
        idUtente: currentId,
        read: false,
      })
      .andWhere(
        'TRUNC(notificheById.CREATE_AT) > TRUNC(ADD_MONTHS(sysdate, -3))',
      )
      .getCount();
  }
}
