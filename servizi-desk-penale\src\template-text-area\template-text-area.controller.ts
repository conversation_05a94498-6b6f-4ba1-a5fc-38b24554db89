import { Body, Controller, Get, Logger, Post } from '@nestjs/common';
import { TemplateTextAreaService } from './template-text-area.service';
import { TemplateInputDto } from './template-input.dto';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { TemplateTxtAreaDto } from './dto/template_txt_area.dto';
import { ApiOkResponseArray } from '../decorators/ApiOkResponseArray';
import process from 'process';

@ApiTags('template')
@Controller('template')
@ApiBearerAuth('access-token')
export class TemplateTextAreaController {
  private logger = new Logger(TemplateTextAreaController.name);
  constructor(
    private readonly templateTextAreaService: TemplateTextAreaService,
  ) {}

  @Post('/search')
  @ApiOkResponseArray(
    TemplateTxtAreaDto,
    'Restituisce tutti i template inseriti nel database.',
  )
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 433, description: 'Internal Server error.' })
  async searchTemplate(
    @Body() templateInputDto: TemplateInputDto,
  ): Promise<Array<TemplateTxtAreaDto> | undefined> {
    try {
      const templateByParam =
        await this.templateTextAreaService.getTemplateByParam(templateInputDto);
      templateByParam.forEach(v => (v.description = v.description || ''));
      return templateByParam;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  @Post('/insert')
  @ApiOkResponse({
    status: 200,
    type: TemplateTxtAreaDto,
    description: 'Modifica un  elemento nella tabella dominio',
  })
  async insert(@Body() presidenteDto: TemplateInputDto) {
    try {
      return this.templateTextAreaService.insert(presidenteDto);
    } catch (e) {
      this.logger.error(e);
    }
  }
  @Get()
  @ApiOkResponseArray(
    TemplateTxtAreaDto,
    'Restituisce tutti i template inseriti nel database.',
  )
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 433, description: 'Internal Server error.' })
  async getAllTemplate(): Promise<Array<TemplateTxtAreaDto> | undefined> {
    try {
      const allTemplate = await this.templateTextAreaService.getAllTemplate();
      allTemplate.forEach(v => (v.description = v.description || ''));
      return allTemplate;
    } catch (e) {
      this.logger.error(e);
    }
  }
}
