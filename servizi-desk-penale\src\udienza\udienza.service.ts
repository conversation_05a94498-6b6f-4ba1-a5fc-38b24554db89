import { Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_udienza.entity';
import { UtilQuery } from '../utils/util-query';
import * as moment from 'moment/moment';
import { PlainObjectToNewEntityTransformer } from 'typeorm/query-builder/transformer/PlainObjectToNewEntityTransformer';
import { PenaleTUdienzaEntityFake } from '../consultazioni-graphql/entities/penale_udienza_fake.entity';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';
import { PenaleTEsitoSentEntity } from '../consultazioni-graphql/entities/penale_t_esito_sent.entity';
import { PenaleParamEntity } from '../consultazioni-graphql/entities/penale_param.entity';
import { PenaleCollegioEntity } from '../consultazioni-graphql/entities/penale_collegio.entity';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';
import {PaginationQueryArgs} from "../relay-pagination/pagination-query.args";
import {DEFAULT_PAGE_SIZE, endCursor, startCursor} from "../relay-pagination/pagination-utils";

@UfficioService()
export class UdienzaService {
  private logger = new Logger(UdienzaService.name);
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  udienze(): Promise<PenaleTUdienzaEntity[]> {
    const udienze = this.connection.getRepository(PenaleTUdienzaEntity).find({
      relations: {
        tipoUdienza: true,
        sezione: true,
        aula: true,
      },
    });
    return udienze;
  }

  async udienzePerEstensore(cf: string): Promise<PenaleTUdienzaEntity[]> {
    try {
      const udienze = await this.connection
        .getRepository(PenaleTUdienzaEntity)
        .createQueryBuilder('UDIE')
        .innerJoin(
          PenaleTRicorsoUdienzaEntity,
          'RICUDIEN',
          'RICUDIEN.ID_UDIEN = UDIE.ID_UDIEN',
        )
        .innerJoin(
          PenaleTEsitoEntity,
          'ESITO',
          'RICUDIEN.ID_RICUDIEN = ESITO.ID_RICUDIEN',
        )
        .leftJoin(
          PenaleTEsitoSentEntity,
          'ESITOSENT',
          'ESITO.ID_ESITO = ESITOSENT.ID_ESITO',
        )
        .innerJoinAndMapOne(
          'UDIE.tipoUdienza',
          PenaleParamEntity,
          'tipoUd',
          'tipoUd.ID_PARAM = UDIE.ID_TIPOUD',
        )
        .innerJoinAndMapOne(
          'UDIE.sezione',
          PenaleParamEntity,
          'tipoSez',
          'tipoSez.ID_PARAM = UDIE.ID_SEZIONE',
        )
        .innerJoinAndMapOne(
          'UDIE.aula',
          PenaleParamEntity,
          'aula',
          'aula.ID_PARAM = UDIE.ID_AULA',
        )
        .leftJoinAndMapMany(
          'UDIE.sentenza',
          PenaleTSentenzaEntity,
          'TSET',
          'TSET.ID_SENT = ESITOSENT.ID_SENT',
        )
        .where(
          '(ESITOSENT.ID_SENT IS NOT NULL OR RICUDIEN.ID_ESITO IS NOT NULL ) AND' +
            '  (TSET.ID_ESTENSORE IN  (SELECT ptm.ID_MAGIS  FROM PENALE_T_MAGIS ptm JOIN PENALE_ANAGMAGIS pa on( pa.ID_ANAGMAGIS = ptm.ID_ANAGMAGIS) WHERE pa.CODICE_FISCALE  = :cf))',
          { cf: cf },
        )
        .getMany();
      /*  .find({
                      relations: {
                        tipoUdienza: true,
                        sezione: true,
                        aula: true,
                      },
                    });*/
      return udienze;
    } catch (e) {
      this.logger.error('errore nella query di ricerca!', e);
      throw new InternalServerErrorException(e);
    }
  }

  async getIdMagisByCf(cf: string): Promise<Array<number>> {
    const [queryString, parameters] =
      this.connection.driver.escapeQueryWithParameters(
        '  SELECT ptm.ID_MAGIS  FROM PENALE_T_MAGIS ptm JOIN PENALE_ANAGMAGIS pa on( pa.ID_ANAGMAGIS = ptm.ID_ANAGMAGIS) WHERE pa.CODICE_FISCALE  = :cf ',
        { cf: cf },
        {},
      );

    const promise = await this.connection.query(queryString, parameters);
    return promise.map((r: { ID_MAGIS: any }) => r.ID_MAGIS);
  }
  async udienzePerRelatoreEstensore(
    cf: string,
  ): Promise<PenaleTUdienzaEntity[]> {
    try {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);
      currentDate.setDate(1);
      const endDate = moment(currentDate);
      endDate.add(-1, 'd');
      endDate.add(1, 'M');
      this.logger.debug('endDate:', endDate.format('DD/MM/YYYY'));
      const startDate = moment(currentDate);
      startDate.add(-6, 'M');
      this.logger.debug('startDate:', startDate.format('DD/MM/YYYY'));
      const idMagisList = await this.getIdMagisByCf(cf);
      const udienze = await this.connection
        .getRepository(PenaleTUdienzaEntity)
        .createQueryBuilder('UDIE')
        .innerJoin(
          PenaleTRicorsoUdienzaEntity,
          'RICUDIEN',
          'RICUDIEN.ID_UDIEN = UDIE.ID_UDIEN',
        )
        .innerJoin(
          PenaleTEsitoEntity,
          'ESITO',
          'RICUDIEN.ID_RICUDIEN = ESITO.ID_RICUDIEN',
        )
        .leftJoin(
          PenaleTEsitoSentEntity,
          'ESITOSENT',
          'ESITO.ID_ESITO = ESITOSENT.ID_ESITO',
        )
        .innerJoinAndMapOne(
          'UDIE.tipoUdienza',
          PenaleParamEntity,
          'tipoUd',
          'tipoUd.ID_PARAM = UDIE.ID_TIPOUD',
        )
        .innerJoinAndMapOne(
          'UDIE.sezione',
          PenaleParamEntity,
          'tipoSez',
          'tipoSez.ID_PARAM = UDIE.ID_SEZIONE',
        )
        .innerJoinAndMapOne(
          'UDIE.aula',
          PenaleParamEntity,
          'aula',
          'aula.ID_PARAM = UDIE.ID_AULA',
        )
        .leftJoinAndMapMany(
          'UDIE.sentenza',
          PenaleTSentenzaEntity,
          'TSET',
          'TSET.ID_SENT = ESITOSENT.ID_SENT',
        )
        .leftJoin(
          PenaleParamEntity,
          'PP',
          'TSET.ID_TIPOSENT = PP.ID_PARAM',
        )
        .where(
          '((ESITOSENT.ID_SENT IS NOT NULL AND "PP"."SIGLA" IN (\'SE\', \'OR\')) OR "ESITO".ESITO in (1425,1424,1421, 742787)) AND' +
            '  (RICUDIEN.ID_RELATORE IN (:...idMagisList)' +
            ' OR TSET.ID_ESTENSORE IN  (:...idMagisList))',
          { idMagisList: idMagisList },
        )
        .andWhere(
          "trunc(UDIE.DATAUD) BETWEEN TO_DATE(:START,  'DD/MM/YYYY') AND TO_DATE(:END,  'DD/MM/YYYY')",
          {
            START: startDate.format('DD/MM/YYYY'),
            END: endDate.format('DD/MM/YYYY'),
          },
        )
        .getMany();
        
      return udienze.map(udi => {
        const estensoreList = udi?.sentenza?.map(s => s.idEstentore);
        const pubblicazioneList = udi?.sentenza?.map(s => s.dataPubblicazione);
        udi.isEstensore =
          estensoreList && estensoreList.length > 0
            ? idMagisList.some(idMagis => estensoreList.includes(idMagis))
            : false;
        udi.allPubblicate = pubblicazioneList
          ? !pubblicazioneList.some(dataPubl => !dataPubl)
          : false;
        return udi;
      });
    } catch (e) {
      this.logger.error('errore nella query di ricerca!', e);
      throw new InternalServerErrorException(e);
    }
  }
  
  async getTermineUdienzaWithPeriod(
    cf: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Array<any> | null> {
    const [queryString, parameters] =
      this.connection.driver.escapeQueryWithParameters(
        UtilQuery.allProvvedimentiTemineDepositoWithPeriodQuery,
        {
          cf: cf,
          START: moment(startDate).format('DD/MM/YYYY'),
          END: moment(endDate).format('DD/MM/YYYY'),
        },
        {},
      );

    const promise = this.connection
      .getRepository(PenaleTUdienzaEntity)
      .query(queryString, parameters);
    const metadataEntity = this.connection.getMetadata(
      PenaleTUdienzaEntityFake,
    );
    const transformerEntity = new PlainObjectToNewEntityTransformer();

    const penaleTUdienzaEntity: PenaleTUdienzaEntityFake =
      metadataEntity.create(this.connection.createQueryRunner());
    const penaleTUdienzaEntityList = await promise;

    const listsResult: PenaleTUdienzaEntityFake[] = [];
    penaleTUdienzaEntityList.map((res: any) => {
      const anagrammaticModel: any = {};
      metadataEntity.columns.forEach(value => {
        if (value.givenDatabaseName) {
          // console.log(anan[value.givenDatabaseName])
          const pror = value.propertyName;
          anagrammaticModel[pror] = res[value.givenDatabaseName];
          this.logger.error(
            anagrammaticModel[pror],
            res[value.givenDatabaseName],
          );
        }
      });
      const penaleTUdienzaEntityFake = transformerEntity.transform(
        penaleTUdienzaEntity,
        res,
        metadataEntity,
      );
      listsResult.push(penaleTUdienzaEntityFake);
    });

    return listsResult;
  }

  udienza(idUdienza: number): Promise<PenaleTUdienzaEntity | null> {
    return this.connection.getRepository(PenaleTUdienzaEntity).findOne({
      where: { idUdien: idUdienza },
      relations: {
        tipoUdienza: true,
        sezione: true,
        aula: true,
      },
    });
  }

  async udienzaPerRelatoreByIdUdienza(
    cf: string,
    idUdienza: number,
    paginationArgs?: PaginationQueryArgs,
  ): Promise<PenaleTUdienzaEntity | null> {
    try {
      const from = paginationArgs ? startCursor(paginationArgs) : 0;
      const to = paginationArgs ? endCursor(paginationArgs) : DEFAULT_PAGE_SIZE;
      const offset = Number.parseInt(from + '') || 0;
      const records = Number.parseInt(to) || 5;
      const udienze = await this.connection
        .getRepository(PenaleTUdienzaEntity)
        .createQueryBuilder('UDIE')
        .innerJoinAndSelect(
          PenaleTRicorsoUdienzaEntity,
          'RICUDIEN',
          'RICUDIEN.ID_UDIEN = UDIE.ID_UDIEN',
        )
        .innerJoinAndSelect(
          PenaleTEsitoEntity,
          'ESITO',
          'RICUDIEN.ID_RICUDIEN = ESITO.idRicUdienza',
        )
        .leftJoinAndSelect(
          PenaleTEsitoSentEntity,
          'ESITOSENT',
          'ESITO.ID_ESITO = ESITOSENT.ID_ESITO',
        )
        .innerJoinAndMapOne(
          'UDIE.tipoUdienza',
          PenaleParamEntity,
          'tipoUd',
          'tipoUd.ID_PARAM = UDIE.ID_TIPOUD',
        )
        .innerJoinAndMapOne(
          'UDIE.sezione',
          PenaleParamEntity,
          'tipoSez',
          'tipoSez.ID_PARAM = UDIE.ID_SEZIONE',
        )
        .innerJoinAndMapOne(
          'UDIE.aula',
          PenaleParamEntity,
          'aula',
          'aula.ID_PARAM = UDIE.ID_AULA',
        )
        .where(
          '(ESITOSENT.ID_SENT IS NOT NULL OR RICUDIEN.ID_ESITO IS NOT NULL ) AND UDIE.idUdien = :idUdien',
          {
            idUdien: idUdienza,
          },
        )
        .getOne();
      return udienze;
    } catch (e) {
      this.logger.error('errore nella query di ricerca!', e);
      throw new InternalServerErrorException(e);
    }
  }

  async udienzaPerPresidenteByIdUdienza(
    cf: string,
    idUdienza: number,
  ): Promise<PenaleTUdienzaEntity | null> {
    try {
      /**
       * select count(*) from PENALE_COLLEGIO coll
       *                 join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
       *                 join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
       *                 JOIN PENALE_T_RICUDIEN PTR on coll.ID_UDIEN = PTR.ID_UDIEN
       * where anag.CODICE_FISCALE = '****************' and PTR.ID_RELATORE = magis.ID_MAGIS;
       */
      const udienze = await this.connection
        .getRepository(PenaleTUdienzaEntity)
        .createQueryBuilder('UDIE')
        .innerJoin(
          PenaleTRicorsoUdienzaEntity,
          'RICUDIEN',
          '(RICUDIEN.ID_UDIEN = UDIE.ID_UDIEN )',
        )
        .innerJoin(
          PenaleCollegioEntity,
          'COLL',
          "(COLL.ID_UDIEN = UDIE.ID_UDIEN AND COLL.TIPOMAG='PRE' AND COLL.ID_MAGIS IN (SELECT ptm.ID_MAGIS  FROM PENALE_T_MAGIS ptm JOIN PENALE_ANAGMAGIS pa on( pa.ID_ANAGMAGIS = ptm.ID_ANAGMAGIS) WHERE pa.CODICE_FISCALE  = :cf))",
        )
        .innerJoin(
          PenaleTEsitoEntity,
          'ESITO',
          'RICUDIEN.ID_RICUDIEN = ESITO.ID_RICUDIEN',
        )
        .leftJoin(
          PenaleTEsitoSentEntity,
          'ESITOSENT',
          'ESITO.ID_ESITO = ESITOSENT.ID_ESITO',
        )
        .innerJoinAndMapOne(
          'UDIE.tipoUdienza',
          PenaleParamEntity,
          'tipoUd',
          'tipoUd.ID_PARAM = UDIE.ID_TIPOUD',
        )
        .innerJoinAndMapOne(
          'UDIE.sezione',
          PenaleParamEntity,
          'tipoSez',
          'tipoSez.ID_PARAM = UDIE.ID_SEZIONE',
        )
        .innerJoinAndMapOne(
          'UDIE.aula',
          PenaleParamEntity,
          'aula',
          'aula.ID_PARAM = UDIE.ID_AULA',
        )
        .where(
          '(ESITOSENT.ID_SENT IS NOT NULL OR RICUDIEN.ID_ESITO IS NOT NULL )AND UDIE.idUdien = :idUdien',
          {
            cf: cf,
            idUdien: idUdienza,
          },
        )
        .getOne();

      return udienze;
    } catch (e) {
      this.logger.error('errore nella query di ricerca!', e);
      throw new InternalServerErrorException(e);
    }
  }
}
