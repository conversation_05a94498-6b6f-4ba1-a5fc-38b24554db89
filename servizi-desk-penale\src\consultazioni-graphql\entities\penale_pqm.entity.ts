import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_PQM') //nome tabella su schema oracle
export class PenalePqmEntity {
  @PrimaryColumn({ name: 'ID_PQM' })
  idPqm: number;

  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OPERATORE' })
  operatore: number;

  @Column({ name: 'OGGI' })
  oggi: Date;

  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;

  @Column({ name: 'ID_TIPOUD' })
  idTipoUd: number;

  @Column({ name: 'ID_SEZIONE' })
  idSezione: number;

  @Column({ name: 'SENTENZA' })
  sentenza: number;

  @Column({ name: 'TESTO' })
  testo: string;

  @Column({ name: 'ANNOTAZIONI' })
  annotazioni: string;
}
