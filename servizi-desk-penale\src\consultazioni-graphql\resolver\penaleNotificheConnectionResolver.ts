import { NotFoundException } from '@nestjs/common';
import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import {
  PenaleNotifiche,
  PenaleNotificheConnection,
} from '../models/penale_notifiche.model';
import { NotificheService } from '../../notifiche/notifiche.service';
import { CreateNotificheInput } from '../entities/dto/create-notifiche.input';
import { PaginationQueryArgs } from '../../relay-pagination/pagination-query.args';
import {
  computePageInfo,
  endCursor,
  startCursor,
} from '../../relay-pagination/pagination-utils';
import { AuthService } from '../../auth/auth.service';
import { NotificheToReadInput } from '../entities/dto/notifiche-to-read-input';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { TRicorsoService } from '../../fascicolo/t-ricorso.service';
import { Utils } from '../../utils/utils';
import { UdienzaService } from '../../udienza/udienza.service';
import { ProvvedimentiService } from '../../provvedimenti/provvedimenti.service';
import { GenericErrorException } from '../../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../../exceptions/code-error-enum.exception';

@Resolver(() => PenaleNotificheConnection)
export class PenaleNotificheConnectionResolver {
  constructor(
    private readonly notificheService: NotificheService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly ricorsoService: TRicorsoService,
    private readonly udienzaService: UdienzaService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly authService: AuthService,
  ) {}

  @Query(() => PenaleNotifiche, { name: 'notifica' })
  async notifica(@Args('id') id: string): Promise<PenaleNotifiche> {
    const provvediemento = await this.notificheService.notificheById(id);
    if (!provvediemento) {
      throw new NotFoundException(id);
    }
    return provvediemento;
  }

  @Query(() => Int, { name: 'countNotificheNotRead' })
  async countNotificheNotRead(): Promise<number> {
    const currentId = await this.authService.getCurrentId();
    return await this.notificheService.notificheNotReadByUser(currentId);
  }

  @Query(() => PenaleNotificheConnection, {
    name: 'notificheByCurrentUserPopover',
  })
  async notificheByCurrentUserPopover(
    @Args() paginationArgs: PaginationQueryArgs,
  ): Promise<PenaleNotificheConnection> {
    paginationArgs.first = 5;
    paginationArgs.read = true;
    const penaleNotificheConnectionPromise = await this.notificheByIdUtene(
      paginationArgs,
    );
    paginationArgs.read = false;
    const penalenotifiche2: PenaleNotificheConnection =
      await this.notificheByIdUtene(paginationArgs);
    penaleNotificheConnectionPromise.edges.push(...penalenotifiche2.edges);
    return penaleNotificheConnectionPromise;
  }
  @Query(() => PenaleNotificheConnection, { name: 'notificheByCurrentUser' })
  async notificheByIdUtene(
    @Args() paginationArgs: PaginationQueryArgs,
  ): Promise<PenaleNotificheConnection> {
    const currentId = await this.authService.getCurrentId();
    const from = startCursor(paginationArgs);
    const to = endCursor(paginationArgs);
    const offset = Number.parseInt(from) || 0;
    const records = Number.parseInt(to) || 5;
    const [notificheResult, total] =
      await this.notificheService.notificheByIdUtente(
        currentId,
        offset,
        records,
        paginationArgs?.term,
        paginationArgs.read,
      );
    const penaleNotificheResult = new Array<PenaleNotifiche>();
    for (const penaleNoticheEntity of notificheResult) {
      const penaleNotifiche = new PenaleNotifiche({ ...penaleNoticheEntity });
      if (penaleNoticheEntity?.nrg) {
        const ricorso = await this.ricorsoService.ricorsoFindByNrg(
          penaleNoticheEntity.nrg,
        );
        if (penaleNoticheEntity?.idUdienza) {
          const udienza = await this.udienzaService.udienza(
            penaleNoticheEntity?.idUdienza,
          );
          penaleNotifiche.dataUdienza = udienza?.dataUdienza;
          penaleNotifiche.tipoUdienza = udienza?.tipoUdienza;
          penaleNotifiche.coleggio = udienza?.aula;
          penaleNotifiche.sezione = udienza?.sezione;
          //TODO DA RICHIVERE
          let provvedimento =
            await this.provvedimentiService.provvedimentoByIdUdienAndNrgModifyAndIdAutore(
              penaleNoticheEntity.idUdienza,
              penaleNoticheEntity.nrg,
              currentId,
            );
          provvedimento =
            provvedimento ??
            (await this.provvedimentiService.provvedimentoByIdUdienAndNrgModify(
              penaleNoticheEntity.idUdienza,
              penaleNoticheEntity.nrg,
            ));
          penaleNotifiche.tipoProvvedimento = provvedimento?.tipo;
          penaleNotifiche.inizioUdienza = udienza?.inizioUdienza;
          penaleNotifiche.fineUdienza = udienza?.fineUdienza;
        }
        if (ricorso?.nrgReale) {
          const [anno, numero] = Utils.calcoloNumeroFascicolo(
            ricorso.nrgReale.toString(),
          );
          penaleNotifiche.annoFascicolo = anno;
          penaleNotifiche.numeroFascicolo = numero;
        }
        const isEstensore =
          await this.authService.isEstensoreforNrgAndIdUdienza(
            penaleNoticheEntity.nrg,
            penaleNoticheEntity.idUdienza,
          );
        penaleNotifiche.isEstensore = isEstensore;
        penaleNotifiche.isPrincipale =
          penaleNoticheEntity.riunitiView?.isPrincipale || false;
      }

      penaleNotificheResult.push(penaleNotifiche);
    }
    const connection = new PenaleNotificheConnection();
    connection.pageInfo = computePageInfo(from, `${offset + records}`, total);
    connection.aggregate = {
      count: penaleNotificheResult.length,
      total,
      unread: await this.notificheService.notificheNotReadByUser(currentId),
      totalElement: await this.notificheService.notificheByIdUtenteTotalElement(
        currentId,
      ),
    };
    const start =
      Number.parseInt(
        connection?.pageInfo?.startCursor
          ? connection?.pageInfo?.startCursor
          : '0',
      ) + 1;
    const edges = penaleNotificheResult.map((fe, index) => ({
      cursor: `${start + index}`,
      node: fe,
    }));
    connection.edges = edges;
    return connection;
  }

  @Mutation(() => PenaleNotifiche, { name: 'creaNotifica' })
  async creaNotifica(@Args('notifica') notifica: CreateNotificheInput) {
    const idAutore = await this.authService.getCurrentId();
    const provvedimentoId = await this.notificheService.createNotifiche(
      notifica,
      idAutore,
    );
    if (provvedimentoId) {
      return await this.notificheService.notificheById(provvedimentoId);
    }
    throw new GenericErrorException(
      'Provvedimento non inserito',
      CodeErrorEnumException.PROVV_NOT_CREATED,
    );
  }

  @Mutation(() => PenaleNotifiche, { name: 'unNotificaToRead' })
  async updateNotificaRead(@Args('id') id: string) {
    await this.notificheService.updateNotificaToRead(id);
    if (id) {
      return await this.notificheService.notificheById(id);
    }
    throw new GenericErrorException(
      'Provvedimento non inserito',
      CodeErrorEnumException.PROVV_NOT_CREATED,
    );
  }

  @Mutation(() => Boolean, { name: 'updateReadNotifyList' })
  async updateReadNotifyList(
    @Args('segnaLette') segnaLette: NotificheToReadInput,
  ) {
    if (segnaLette.ids) {
      for (const id of segnaLette.ids) {
        await this.notificheService.updateNotificaToRead(id);
      }
      return true;
    }
    throw new GenericErrorException(
      'Provvedimento non inserito',
      CodeErrorEnumException.PROVV_NOT_CREATED,
    );
  }

  @Mutation(() => Boolean, { name: 'updateReadAllNotify' })
  async updateReadAllNotify() {
    const currentId = await this.authService.getCurrentId();
    const penaleNoticheEntities =
      await this.notificheService.notificheByIdUtenteNotRead(currentId);
    if (penaleNoticheEntities) {
      for (const penaleNoticheEntity of penaleNoticheEntities) {
        await this.notificheService.updateNotificaToRead(
          penaleNoticheEntity.idNotifica,
        );
      }
      return true;
    }
    throw new GenericErrorException(
      'Provvedimento non inserito',
      CodeErrorEnumException.PROVV_NOT_CREATED,
    );
  }

  @Query(returns => PenaleNotificheConnection, { name: 'notifiche' })
  async notifiche(
    @Args() paginationArgs: PaginationQueryArgs,
  ): Promise<PenaleNotificheConnection> {
    const currentId = await this.authService.getCurrentId();
    const from = startCursor(paginationArgs);
    const to = endCursor(paginationArgs);
    const offset = Number.parseInt(from) || 0;
    const records = Number.parseInt(to) || 5;
    const [notificheResult, total] = await this.notificheService.notifiche(
      offset,
      records,
    );
    const connection = new PenaleNotificheConnection();
    connection.pageInfo = computePageInfo(from, `${offset + records}`, total);
    connection.aggregate = {
      count: notificheResult.length,
      total,
      unread: await this.notificheService.notificheNotReadByUser(currentId),
    };
    const start =
      Number.parseInt(
        connection?.pageInfo?.startCursor
          ? connection?.pageInfo?.startCursor
          : '0',
      ) + 1;
    const edges = notificheResult.map((fe, index) => ({
      cursor: `${start + index}`,
      node: new PenaleNotifiche({
        idNotifica: fe.idNotifica,
        dataCreazione: fe.dataCreazione,
        descrizione: fe.descrizione,
        nrg: fe.nrg,
        idUtente: fe.idUtente,
        read: fe.read,
        tipo: fe.tipo,
      }),
    }));
    connection.edges = edges;
    return connection;
  }
}
