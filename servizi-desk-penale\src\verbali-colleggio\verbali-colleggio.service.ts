import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleVerbaleColleggioEntity } from '../consultazioni-graphql/entities/penale_verbale_colleggio.entity';

@UfficioService()
export class VerbaliColleggioService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  colleggioVervali(): Promise<PenaleVerbaleColleggioEntity[]> {
    return this.connection.getRepository(PenaleVerbaleColleggioEntity).find();
  }

  colleggioVerbale(
    idUdienza: number,
    idMAgis: number,
  ): Promise<PenaleVerbaleColleggioEntity | null> {
    return this.connection
      .getRepository(PenaleVerbaleColleggioEntity)
      .findOneBy({ idUdienza: idUdienza, idAnagraficaMagistrato: idMAgis });
  }
}
