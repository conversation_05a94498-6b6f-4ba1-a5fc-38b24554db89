import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleAnagraficaParti } from './penale_anagparti.model';
import { PenaleDifenparti } from './penale_difenparti.model';
import { PenaleParam } from './penale_param.model';
import { PenaleParteLegate } from './penale_parte_legate.model';

@ObjectType()
export class PenaleTParti {
  @Field(type => ID)
  idParte: number;
  anagraficaParte?: PenaleAnagraficaParti;

  @Field(type => Int)
  nrg: number;
  @Field(type => String)
  altri?: string;

  tipoFig?: PenaleParam;

  @Field(type => Int)
  idTipoFig: number;
  @Field(type => Int)
  idFunzione: number;
  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  operatore: number;
  @Field(type => Boolean)
  ricorrente: boolean;
  @Field(type => Date)
  dtinizStato: Date;
  @Field(type => String)
  penaSup5: string;
  @Field(type => String)
  statoParte?: string;
  @Field(type => Date)
  datarresto?: Date;
  @Field(type => Date)
  dataScarcerazione?: Date;
  @Field(type => Date)
  dataDecor?: Date;
  @Field(type => Int)
  tipoLegame?: number;
  @Field(type => Int)
  numOrdine?: number;
  @Field(type => Int)
  idAnagraficaParte?: number;
  @Field(type => String)
  stralcio?: string;
  @Field(type => String)
  secretata?: string;
  @Field(type => Int)
  uffins?: PenaleParam;
  @Field(type => Int)
  idParamfig?: number;
  @Field(type => Int)
  privacy?: number;
  @Field(type => Int)
  glbDtime?: number;
  @Field(type => String)
  art161?: string;
  @Field(type => String)
  art159?: string;
  @Field(type => String)
  art165?: string;

  difensori?: PenaleDifenparti[];

  parteLegata?: PenaleParteLegate;

  displayParti?: string;
  public constructor(init?: Partial<PenaleTParti>) {
    Object.assign(this, init);
  }
}
