import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenalePartiEntity } from '../consultazioni-graphql/entities/penale_parti.entity';

@UfficioService()
export class PartiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  parti(): Promise<PenalePartiEntity[]> {
    return this.connection.getRepository(PenalePartiEntity).find({
      relations: {
        tipoFig: true,
        uffins: true,
      },
    });
  }

  parte(idParte: number): Promise<PenalePartiEntity | null> {
    return this.connection.getRepository(PenalePartiEntity).findOne({
      where: { idParte: idParte },
      relations: {
        tipoFig: true,
        uffins: true,
      },
    });
  }

  async ricorsoFindByNrg(nrg: number): Promise<PenalePartiEntity[] | null> {
    return this.connection.getRepository(PenalePartiEntity).find({
      where: { nrg },
      relations: {
        tipoFig: true,
        uffins: true,
      },
    });
  }
}
