import { UfficioService } from '../multi-tenant/ufficio-service.decorator';

import * as fs from 'fs';
import { ProvvedimentiTipoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import { Logger } from '@nestjs/common';

export class FascicoloInfo {
  filename: string;
  folder: string;
  extesion: string;
  tipoProvv: ProvvedimentiTipoEnum;
  nrg: number;
  anno: number;
  numero: number;
  numeroOrdine: number;
  public toString = (): string => {
    return `${JSON.stringify(this)}`;
  };
}

export class UdienzaInfo {
  anno: number;
  numero: number;
  numeroOrdine: number;
  nrg: number;
  dataUdienza: Date;
  sezione: string;
  aula: string;
  tipoUdienza: string;
  public toString = (): string => {
    return `${JSON.stringify(this)}`;
  };
}

export class PilotInfo {
  udienzaInfo: UdienzaInfo;
  fascicoliInfos: FascicoloInfo[];
  public toString = (): string => {
    return `${JSON.stringify(this)}`;
  };
}

@UfficioService()
export class CreateHtmlIndexService {
  private logger = new Logger(CreateHtmlIndexService.name);
  footer = '</body></html>';
  tableStart = '<table style="width:100%; padding: 15px">';
  tableEnd = '</table>';

  createPilotForIntestazione(pilotInfo: PilotInfo) {
    this.logger.log(
      `Creazione dell'indice.html per le intestazioni. nrg: ${pilotInfo?.udienzaInfo?.nrg}`,
    );
    const dataUdienza = pilotInfo
      ? pilotInfo.udienzaInfo?.dataUdienza
      : new Date();
    const header = `<!DOCTYPE html><html><head><style>body{ padding-left: 2rem; padding-right: 2rem;} table, th, td {text-align: center; border: 0.5px solid grey;border-collapse: collapse;}</style></head><body><h1>ELENCO DELLE INTESTAZIONE DELL'UDIENZA 
    - ${dataUdienza.toLocaleDateString('it-IT')} 
      ${
        pilotInfo.udienzaInfo.sezione +
        ' | ' +
        pilotInfo.udienzaInfo.tipoUdienza +
        ' | COLLEGIO  ' +
        pilotInfo.udienzaInfo.aula
      }
    </h1>`;
    const tableHeader =
      '<tr ><th style="width: 20%">N. Ord</th><th style="width: 20%">NRG</th><th style="width: 25%">Tipologia provv.</th><th style="width: 35%">Instestazione</th></tr>';

    let content = '';
    for (const ric of pilotInfo.fascicoliInfos) {
      content += `<tr><td>${ric.numeroOrdine}</td><td>${
        ric.numero + '/' + ric.anno
      }</td><td>${ric.tipoProvv}</td><td>
<a href="${ric.folder}/${ric.filename}${ric.extesion}">${
        ric.filename
      }</a> </td></tr>`;
    }
    content =
      header +
      this.tableStart +
      tableHeader +
      content +
      this.tableEnd +
      this.footer;
    const pilotPath = 'indice.html';
    fs.writeFileSync(pilotPath, content, 'utf-8');
    const bufferIndice = Buffer.from(fs.readFileSync(pilotPath, 'utf-8'));
    this.logger.log(`Indice.html creato correttamente con i dati:${pilotInfo}`);
    return bufferIndice;
  }

  creaPilotPerCodaDeposito(pilotInfos: PilotInfo[]) {
    this.logger.log(
      `Creazione dell'indice.html per la codaDeposito. nrg: ${
        pilotInfos?.length > 0 ? pilotInfos[0]?.udienzaInfo?.nrg : ''
      }`,
    );
    const pilotPath = 'indice.html';
    let generaleContent = '';
    const headerIniziali = `
                <!DOCTYPE html>
                <html><head>
                    <style> body{ padding-left: 2rem; padding-right: 2rem;} table, th, td {text-align: center; border: 0.5px solid grey;border-collapse: collapse;}</style>
                </head>
                <body>
                <div style="width: 100%; padding: 15px">
                    <h1>ELENCO DELLA CODA DI DEPOSITO SCARICATA IL ${new Date().toLocaleDateString(
                      'it-IT',
                    )} 
                    </h1>
                    </div>
                `;

    for (const pilotInfo of pilotInfos) {
      this.logger.log(
        `inserisco la tabella per il deposito nella index.html. dati: ${pilotInfo}`,
      );
      const dataUdienza = pilotInfo?.udienzaInfo?.dataUdienza
        ? pilotInfo.udienzaInfo?.dataUdienza
        : new Date();
      const header = `
                <div style="width: 100%; padding: 15px">
                    <h2> N.R.G. ${
                      pilotInfo.udienzaInfo?.numero +
                      '/' +
                      pilotInfo.udienzaInfo?.anno
                    } UDIENZA DEL
                     ${
                       pilotInfo.udienzaInfo
                         ? dataUdienza.toLocaleDateString('it-IT')
                         : ''
                     }    ${
        pilotInfo.udienzaInfo.sezione +
        ' | ' +
        pilotInfo.udienzaInfo.tipoUdienza +
        ' | COLLEGIO  ' +
        pilotInfo.udienzaInfo.aula
      }
                    </h2>
                 </div>
                `;
      const tableHeader =
        '<tr><th style="width: 25%">NRG</th><th style="width: 25%">Tipologia provv.</th><th style="width: 50%">File</th></tr>';

      let content = '';
      for (const ric of pilotInfo.fascicoliInfos) {
        content += `<tr><td>${ric.numero + '/' + ric.anno}
                            </td><td>${ric.tipoProvv}
                            </td><td>
                            <a href="${ric.folder}/${ric.filename}${
          ric.extesion
        }">
                             ${ric.filename}</a> 
                            </td></tr>`;
      }
      generaleContent =
        generaleContent +
        header +
        this.tableStart +
        tableHeader +
        content +
        this.tableEnd;
    }
    fs.writeFileSync(
      pilotPath,
      headerIniziali + generaleContent + this.footer,
      'utf-8',
    );
    const bufferIndice = Buffer.from(fs.readFileSync(pilotPath, 'utf-8'));

    this.logger.log(
      `Indice.html creato correttamente per la coda deposito con i dati:${pilotInfos}`,
    );
    return bufferIndice;
  }
}
