import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleParamEntity } from '../consultazioni-graphql/entities/penale_param.entity';

@UfficioService()
export class PenaleParamService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  allParams(): Promise<PenaleParamEntity[]> {
    return this.connection.getRepository(PenaleParamEntity).find();
  }

  getParam(idParam: number): Promise<PenaleParamEntity | null> {
    return this.connection
      .getRepository(PenaleParamEntity)
      .findOneBy({ idParam: idParam });
  }
  getParamBySiglaPerSospensione(
    sigla: string,
  ): Promise<PenaleParamEntity | null> {
    return this.connection
      .getRepository(PenaleParamEntity)
      .findOneBy({ sigla: sigla, tipoTab: 'SOSPENSIONE' });
  }
}
