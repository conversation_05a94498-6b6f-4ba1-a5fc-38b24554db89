import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_VERBALE_COLLEGIO') //nome tabella su schema oracle
export class PenaleVerbaleColleggioEntity {
  @PrimaryColumn({ name: 'ID_UDIENZA' })
  idUdienza: number;

  @PrimaryColumn({ name: 'ID_ANAGMAGIS' })
  idAnagraficaMagistrato: number;

  @PrimaryColumn({ name: 'ID_ANAGMAGIS_NEW' })
  idAnagraficaMagistratoNew: number;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'DATA_INSERIMENTO' })
  dataInserimento: Date;
  @Column({ name: 'DATA_AGGIORNAMENTO' })
  dataAggiornamento: Date;
}
