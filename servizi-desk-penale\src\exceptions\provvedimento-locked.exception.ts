import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class ProvvedimentoLockedException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
  ) {
    super(
      response,
      CodeErrorEnumException.PROVV_LOCKED,
      NSTypeErrorEnum.WARN,
      options,
    );
  }
}
