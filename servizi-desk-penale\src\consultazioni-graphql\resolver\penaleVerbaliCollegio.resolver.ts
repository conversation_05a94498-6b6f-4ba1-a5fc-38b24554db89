import { NotFoundException } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import { CollegioService } from '../../collegio/collegio.service';
import { MagistratiService } from '../../magistrati/magistrati.service';
import { VerbaliColleggioService } from '../../verbali-colleggio/verbali-colleggio.service';
import { PenaleColleggioDetails } from '../models/penale_colleggio_details.model';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { AnagraficaMagistratiService } from '../../anagrafica-magistrati/anagrafica-magistrati.service';
import { PenaleCollegioDetailsModel } from '../models/dto/penale_t_collegio_details.model';
import { PenaleTMagisDeatils } from '../models/dto/penale_t_magis_details.model';
import { PenaleAnagmagisDetails } from '../models/dto/penale_anagmagis_details.model';
import { AuthService } from '../../auth/auth.service';

@Resolver(() => PenaleColleggioDetails)
export class PenaleVerbaliCollegioResolver {
  constructor(
    private readonly collegioService: CollegioService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly verbaliColleggioService: VerbaliColleggioService,
    private readonly anagraficaMagistratiService: AnagraficaMagistratiService,
    private readonly magistratiService: MagistratiService,
    private readonly authService: AuthService,
  ) {}

  @Query(() => PenaleColleggioDetails, { name: 'colleggioDetails' })
  async collegioByNrgAndIdUdienza(
    @Args('id') id: number,
    @Args('nrg') nrg: number,
  ): Promise<PenaleColleggioDetails | null> {
    return await this.getColelggioByIdUdienzaENrg(id, nrg);
  }

  @Query(() => PenaleColleggioDetails, { name: 'colleggioDetailsByOrdine' })
  async colleggioDetailsByOrdine(
    @Args('id') id: number,
    @Args('ordine') ordine: number,
  ): Promise<PenaleColleggioDetails | null> {
    const [nrgForProvvedimenti, idRicUdienza] =
      await this.ricorsoUdienzaService.getNrgForProvvedimenti(id, ordine);
    return await this.getColelggioByIdUdienzaENrg(id, nrgForProvvedimenti);
  }

  private async getColelggioByIdUdienzaENrg(id: number, nrg: number) {
    const colleggiosEntity = await this.collegioService.collegioByIdUdienza(id);
    if (!colleggiosEntity) {
      throw new NotFoundException(id);
    }
    const colleggioDetails = new PenaleColleggioDetails();
    const colleggios = new Array<PenaleCollegioDetailsModel>();
    for (const u of colleggiosEntity) {
      const penaleCollegio = new PenaleCollegioDetailsModel({ ...u });
      const magistrato = new PenaleTMagisDeatils({
        ...(await this.magistratiService.magistratiServiceByIdMagis(
          penaleCollegio.idMagis,
        )),
      });
      penaleCollegio.magistrato = magistrato;
      if (magistrato.idAnagmagis) {
        const magistratoDaSostituire =
          await this.verbaliColleggioService.colleggioVerbale(
            id,
            magistrato.idAnagmagis,
          );
        if (magistrato?.idAnagmagis) {
          const anagrafica = new PenaleAnagmagisDetails({
            ...(await this.anagraficaMagistratiService.anagraficaMagistrato(
              magistratoDaSostituire
                ? magistratoDaSostituire.idAnagraficaMagistratoNew
                : magistrato?.idAnagmagis,
            )),
          });
          magistrato.anagraficaMagistrato = anagrafica;
          penaleCollegio.magistrato = magistrato;
        }
        if (
          penaleCollegio.tipoMag !== 'RI' &&
          penaleCollegio.tipoMag !== 'PM'
        ) {
          colleggios.push(penaleCollegio);
        }
      }
    }
    const ricorsoUdienza =
      await this.ricorsoUdienzaService.ricorsoUdienzaByNrgAndIdUdienza(id, nrg);

    if (ricorsoUdienza?.idRelatore) {
      const magistratoRelatore =
        await this.magistratiService.magistratiServiceByIdMagis(
          ricorsoUdienza?.idRelatore,
        );
      const presidente = colleggios.find(pre => pre.tipoMag === 'PRE');
      if (presidente) {
        const idDuplicato = colleggios
          .map(coll => {
            if (coll.tipoMag !== 'PRE') {
              return coll.idMagis;
            }
          })
          .indexOf(presidente?.idMagis);
        if (idDuplicato >= 0) {
          colleggios.splice(idDuplicato, 1);
        }
      }
      const idexRelatore = colleggios
        .map(coll => coll.magistrato?.idAnagmagis)
        .indexOf(magistratoRelatore?.idAnagmagis);
      colleggioDetails.relatore = new PenaleTMagisDeatils({
        ...magistratoRelatore,
      });
      //TODO da cambiare prendere una lista di estensore
      const idEstensore = await this.authService.getIdMagisEstensoreForNrg(nrg, id);
      if (idEstensore) {
        colleggios.forEach(col => {
          if (col.idMagis == idEstensore) {
            col.isEstensore = true;
          }
        });
      }
      if (idexRelatore >= 0) {
        colleggios[idexRelatore].isRelatore = true;
      } else {
        const penaleCollegio1 = colleggios[0];
        const relatoreColleggio = new PenaleCollegioDetailsModel({
          ...penaleCollegio1,
        });
        relatoreColleggio.magistrato = colleggioDetails.relatore;
        colleggios.push(relatoreColleggio);
      }
    }
    // relatore.tipoMag = new PenaleParam();
    if (colleggios && colleggios.length > 0) {
      colleggios.sort((a, b) => {
        if (a.tipoMag === 'PRE' || b.tipoMag === 'PRE') {
          if (b.tipoMag === 'PRE') {
            return b.tipoMag === 'PRE' && a.tipoMag !== 'PRE'
              ? 1
              : a.gradoMag > b.gradoMag
              ? 1
              : -1;
          } else {
            return -1;
          }
        }
        /* if (a.isRelatore || b.isRelatore) {
                        if (b.isRelatore) {
                          return b.isRelatore && !a.isRelatore
                            ? 1
                            : a.gradoMag > b.gradoMag
                            ? 1
                            : -1;
                        } else {
                          return b.tipoMag === 'PRE' ? 1 : -1;
                        }
                      }*/
        if (a.gradoMag !== b.gradoMag) {
          return a.gradoMag < b.gradoMag ? -1 : 1;
        }
        if (
          a.magistrato?.anagraficaMagistrato?.cognome &&
          b.magistrato?.anagraficaMagistrato?.cognome
        ) {
          return a.magistrato?.anagraficaMagistrato?.cognome >
            b.magistrato?.anagraficaMagistrato?.cognome
            ? 1
            : -1;
        }

        return 1;
        // a parita di grado va preso il cognome
      });
    }
    colleggioDetails.colleggioMagistrati = colleggios;
    return colleggioDetails;
  }
}
