import { ApiProperty } from '@nestjs/swagger';

export class President<PERSON><PERSON><PERSON> {
  @ApiProperty()
  idProvv?: string;
  @ApiProperty()
  nrg?: number;
  @ApiProperty()
  note?: string;
  @ApiProperty()
  numeroFascicolo?: number | null;
  @ApiProperty()
  annoFscicolo?: number | null;
  tipoModifica?: PresidenteTipoModificaEnum;
}
export enum PresidenteTipoModificaEnum {
  RICHIESTA_MODIFICA = 'RICHIESTA_MODIFICA',
  MINUTA_MODIFICATA_PRESIDENTE = 'MINUTA_MODIFICATA_PRESIDENTE',
}
