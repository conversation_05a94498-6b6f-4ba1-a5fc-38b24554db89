import { ObjectType, Field, ArgsType, PartialType } from '@nestjs/graphql';
import { CodeDepositoDto } from './dto/code_deposito_dto.model';
import { Role } from '../../auth/role.enum';
import { EnumDefinitionFactory } from '@nestjs/graphql/dist/schema-builder/factories/enum-definition.factory';
import { OutputTypeFactory } from '@nestjs/graphql/dist/schema-builder/factories/output-type.factory';
import { Exclude } from 'class-transformer';

@ObjectType() //nome tabella su schema oracle
export class CodeDeposito {
  @Field(type => String)
  idProvv: string;

  @Field(type => String)
  cf: string;

  @Field({ nullable: true })
  ruolo?: Role;
  codeDepositoDto?: CodeDepositoDto;

  public constructor(init?: Partial<CodeDeposito>) {
    Object.assign(this, init);
  }
}
