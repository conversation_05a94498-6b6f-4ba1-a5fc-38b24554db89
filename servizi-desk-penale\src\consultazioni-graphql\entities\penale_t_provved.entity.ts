import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, OneToOne, PrimaryColumn } from 'typeorm';
import ColumnBooleanStringTransformer from './utility/column-boolean-string-transformer';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_T_PROVVED') //nome tabella su schema oracle
export class PenaleTProvvedEntity {
  @PrimaryColumn({ name: 'ID_PROVV' })
  idProvvedimento: number;

  @Column({ name: 'NUMPROVV' })
  numProvv: number;

  @Column({ name: 'NRG' })
  nrg: number;
  @Column({
    name: 'IMPUGNATO',
    type: 'varchar',
    width: 1,
    transformer: new ColumnBooleanStringTransformer(),
  })
  impugnato: boolean;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'ID_AULO' })
  idAulo: number;

  @Column({ name: 'DATAPROVV' })
  dataProvv: Date;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_TIPOPROVV' })
  tipoProvv: PenaleParamEntity;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'GRADOPROVV' })
  gradoProvv: PenaleParamEntity;

  isDuplicato: boolean;
  public constructor(init?: Partial<PenaleTProvvedEntity>) {
    Object.assign(this, init);
  }
}
