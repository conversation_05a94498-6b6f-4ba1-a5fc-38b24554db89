import { Column, Entity, PrimaryColumn } from 'typeorm';
import { registerEnumType } from '@nestjs/graphql';

export enum TipoOscuramentoEnum {
  NESSUNO = 'Nessuno',
  LEGGE = 'Legge',
  UFFICIO = 'Ufficio',
  PARTE = 'Parte',
}
registerEnumType(TipoOscuramentoEnum, {
  name: 'TipoOscuramentoEnum',
});

@Entity('CSPBACKEND_TIMBRIPUBB', { schema: 'MIGRA' })
export class CspbackendTimbripubbEntity {
  @PrimaryColumn({ name: 'ID' })
  id: string;

  @Column({ name: 'NUMRIC' })
  numric: string;

  @Column({ name: 'TIPOOSCURAMENTO' })
  tipoOscuramento: TipoOscuramentoEnum;

  @Column({ name: 'NUMRACCGEN' })
  numraccgen: string;

  @Column({ name: 'NUMSEZIONALE' })
  numsezionale: string;

  @Column({ name: 'DATAPUBBLICAZIONE' })
  dataPubblicazione: Date;

  @Column({ name: 'IDCATATTO' })
  idcatatto: string;

  @Column({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'IDCATATTOOSCURATO' })
  idcatattooscurato: string;

  @Column({ name: 'ID_UDIEN', default: -1 })
  idUdien: number;
}
