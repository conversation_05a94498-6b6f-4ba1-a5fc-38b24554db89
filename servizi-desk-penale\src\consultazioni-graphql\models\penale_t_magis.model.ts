import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleAnagmagis } from './penale_anagmagis.model';
import { PenaleParam } from './penale_param.model';

@ObjectType()
export class PenaleTMagis {
  @Field(type => ID)
  idMagis: number;

  @Field(type => Int)
  ufficio: number;
  tipoMag?: PenaleParam;
  @Field(type => Int)
  @Field(type => Date)
  dataInizio: Date;
  @Field(type => Date)
  dataFine?: Date;
  @Field(type => Int)
  grado: number;
  @Field(type => Int)
  idFunzione?: number;
  @Field(type => Int)
  operatore?: number;
  @Field(type => Date)
  oggi?: Date;
  @Field(type => Int)
  codMag?: number;
  @Field(type => Int)
  idAnagmagis?: number;
  anagraficaMagistrato?: PenaleAnagmagis;
  @Field(type => Int)
  glbDtime?: number;

  public constructor(init?: Partial<PenaleTMagis>) {
    Object.assign(this, init);
  }
}
