import { registerEnumType } from '@nestjs/graphql';
import { ProvvedimentiTipoEnum } from '../../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';

export enum TipologiaTemplateTxtArea {
  INTRODUZIONE = 'INTRODUZIONE',
  MOTIVO_DI_RICORSO = 'motivo di ricorso',
  MOTIVAZIONI = 'motivazioni',
  PARTE_FINALE = 'parte finale',
}
registerEnumType(TipologiaTemplateTxtArea, {
  name: 'TipologiaTemplateTxtArea',
});
