import { Column, Entity, PrimaryColumn } from 'typeorm';
import ColumnDateIsoTransformer from './utility/column-date-iso-transformer';

@Entity('PENALE_UDIENZA') //nome tabella su schema oracle
export class PenaleUdienzaEntity {
  @PrimaryColumn({ name: 'ID_UDIEN' })
  idUdienza: number;

  @Column({
    name: 'DATAUD',
    type: 'timestamp with time zone',
    transformer: new ColumnDateIsoTransformer(),
  })
  dataUdienza: Date;

  @Column({ name: 'TIPOUD' })
  tipoUdienza: string;

  @Column({ name: 'SEZIONE' })
  sezione: string;

  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OGGI' })
  oggi: Date;

  @Column({ name: 'OPERATORE' })
  operatore: number;

  @Column({ name: 'AULA' })
  aula: string;

  @Column({ name: 'NOTEPG' })
  notePg: string;

  @Column({ name: 'INIZIOUDIENZA' })
  inizioUdienza: Date;

  @Column({ name: 'FINEU<PERSON>ENZ<PERSON>' })
  fineUdienza: Date;
}
