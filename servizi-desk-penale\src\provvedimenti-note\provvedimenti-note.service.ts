import { Inject } from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleProvvedimentiNoteEntity } from '../consultazioni-graphql/entities/penale_provvedimenti_note.entity';
import { CreateProvvedimentiNoteInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-note.input';
import { AuthService } from '../auth/auth.service';

@UfficioService()
export class ProvvedimentiNoteService {
  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly authService: AuthService,
  ) {}

  provvedimentiNote(): Promise<PenaleProvvedimentiNoteEntity[]> {
    return this.connection.getRepository(PenaleProvvedimentiNoteEntity).find();
  }

  provvedimentoNotaById(
    idProvNota: string,
  ): Promise<PenaleProvvedimentiNoteEntity | null> {
    return this.connection
      .getRepository(PenaleProvvedimentiNoteEntity)
      .findOne({
        where: { idProvvNote: idProvNota },
      });
  }

  async countNote(idProvvedimento: string): Promise<number> {
    return this.connection.getRepository(PenaleProvvedimentiNoteEntity).count({
      where: { idProvvedimento: idProvvedimento },
    });
  }

  provvedimentoNotaByIdProvvedimento(
    idProvvedimento: string,
  ): Promise<PenaleProvvedimentiNoteEntity[] | null> {
    return this.connection.getRepository(PenaleProvvedimentiNoteEntity).find({
      where: { idProvvedimento: idProvvedimento },
      order: {
        dataInserimento: 'DESC',
      },
    });
  }

  async createProvvedimentoNote(
    provvedimento: CreateProvvedimentiNoteInput,
    entityManager?: EntityManager,
  ): Promise<string | null> {
    const idAutore = await this.authService.getCurrentId();
    if (entityManager) {
      const promise = await entityManager
        .getRepository(PenaleProvvedimentiNoteEntity)
        .save(provvedimento);
      return promise.idProvvNote;
    }
    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(PenaleProvvedimentiNoteEntity)
      .values({
        dataInserimento: new Date(),
        idAutore: idAutore,
        note: provvedimento.note,
        idProvvedimento: provvedimento.idProvvedimento,
      })
      .returning(['idProvvNote'])
      .execute();
    const x = result.raw[0];
    return x[0];
  }
}
