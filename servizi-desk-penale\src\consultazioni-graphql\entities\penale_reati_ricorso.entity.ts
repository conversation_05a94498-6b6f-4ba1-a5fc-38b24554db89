import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_REATIRICORSO') //nome tabella su schema oracle
export class PenaleReatiRicorsoEntity {
  @PrimaryColumn({ name: 'ID_REATIRICORSO' })
  idReatiRicorsi: number;
  @Column({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'ID_REATO' })
  idReato: number;

  @Column({ name: 'TIPODATA' })
  tipoData: string;
  @Column({ name: 'ISTPROC' })
  istProc: number;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({
    name: 'PRINCIPALE',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  principale: boolean;
  @Column({ name: 'NOTE' })
  note: string;
  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPOD' })
  tipoD: PenaleParamEntity;
  @Column({ name: 'DATADA' })
  dataDa: Date;
  @Column({ name: 'DATAA' })
  dataA: Date;
}
