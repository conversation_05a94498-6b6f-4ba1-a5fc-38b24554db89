import { Field, InputType, Int } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class CreateNotificheInput {
  @ApiProperty()
  @Field(() => String)
  descrizione: string;
  @ApiProperty()
  @Field(() => String, {
    nullable: true,
  })
  tipo?: string;
  @ApiProperty()
  @Field(() => Int)
  nrg: number;
  @ApiProperty()
  @Field(() => Int)
  idUdien: number;
}
