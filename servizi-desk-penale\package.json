{"name": "servizi-desk-penale", "version": "1.03.00", "description": "Servizi Desk Cassazione Penale", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start --exec \"node --max-http-header-size=40960\"", "start:dev": "nest start --watch --exec \"node --max-http-header-size=40960\"", "start:debug": "nest start --debug --watch --exec \"node --max-http-header-size=40960\"", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^4.6.0", "@nestjs/apollo": "^11.0.5", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.0.0", "@nestjs/graphql": "^12.0.11", "@nestjs/jwt": "^10.1.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/typeorm": "^9.0.1", "@ns/docx-tools-js": "3.0.0", "@ns/verifica-firma-js": "1.0.2", "axios": "^0.26.1", "bignumber.js": "^9.1.1", "cache-manager": "^6.4.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cryptr": "^6.3.0", "graphql": "^16.6.0", "graphql-relay": "^0.10.0", "graphql-upload": "^13.0.0", "js-yaml": "^4.1.0", "jsdom": "21.1.2", "jszip": "^3.10.1", "jwks-rsa": "^3.0.1", "moment": "^2.29.4", "nest-winston": "^1.9.1", "oracledb": "^5.5.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "typeorm": "^0.3.15", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/graphql-upload": "^8.0.11", "@types/jest": "29.5.0", "@types/js-yaml": "^4.0.5", "@types/jsdom": "^21.1.1", "@types/multer": "^1.4.7", "@types/node": "18.15.11", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}