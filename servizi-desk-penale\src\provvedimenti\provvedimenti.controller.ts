import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Post,
  StreamableFile,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { ProvvLavorazioneService } from './provvLavorazione.service';
import { SignatureChecker } from '@ns/verifica-firma-js';
import { ConfigService } from '@nestjs/config';
import * as FormData from 'form-data';
import axios from 'axios';
import { ProvvedimentiService } from './provvedimenti.service';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { FirmaProvvLavorazioneInput } from './entities/dto/firma-provvLavorazione.input';
import { PenaleProvvLavFile } from '../consultazioni-graphql/models/penale_provv_lav_file.model';
import { PenaleProvvLavFileEntity } from '../consultazioni-graphql/entities/penale_provv_lav_file.entity';
import { AuthService } from '../auth/auth.service';
import { GenerateProvvLavorazioneInput } from '../consultazioni-graphql/entities/dto/generate-provvLavorazione.input';
import { ProvvEditorLavorazioneService } from './provvEditorLavorazione.service';
import { PenaleProvvEditor } from '../consultazioni-graphql/models/penale_provv_editor.model';
import { TRicorsoUdienzaService } from '../ricorso-udienza/t-ricorso-udienza.service';
import { VerbaleRicorsoService } from '../verbale-ricorso/verbale-ricorso.service';
import { EsitiRuoloService } from '../esiti/esiti-ruolo.service';
import { SentenzaRuoloService } from '../esiti/sentenza-ruolo.service';
import { VerbaleUdienzaService } from '../verbale-udienza/verbale-udienza.service';
import { InitialRedazioneOnlineModel } from './entities/dto/initial-redazione-online.model';
import { ProvvedimentiTipoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import { SentenzaService } from '../sentenza/sentenza.service';
import { Utils } from '../utils/utils';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';
import { EsitiService } from '../esiti/esiti.service';
import { EsitiSentService } from '../esiti/esiti-sent.service';
import { TRicorsoService } from '../fascicolo/t-ricorso.service';
import { PresidenteDto } from './entities/dto/presidente-dto';
import { ProvvedimentoPresidenteService } from './provvedimento-presidente.service';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { CreateProvvedimentiChangeStatusInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-change-status.input';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { ProvvedimentoRelazioneService } from '../provvedimento-change-status/provvedimento-relazione.service';
import { PlaceholderService } from './placeholder.service';
import { ArgsProvvedimentoInput } from '../consultazioni-graphql/entities/dto/args-provvedimento.input';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';
import { UdienzaService } from '../udienza/udienza.service';
import {
  ContextValueDto,
  ProvvPlaceholderDto,
} from './entities/dto/provv-placeholder-dto';
import { ProvvPlaceholderTextareaService } from './provvPlaceholderTextarea.service';
import * as JSZip from 'jszip';
import {
  CreateHtmlIndexService,
  FascicoloInfo,
  PilotInfo,
  UdienzaInfo,
} from './create-html.index.service';
import * as moment from 'moment';
import { ProvvedimentiEstensoreService } from './provvedimenti-estensore.service';
import { ServiziDepositoException } from '../exceptions/servizi-deposito.exception';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { PenaleProvvedimenti } from '../consultazioni-graphql/models/penale_provvedimenti.model';
import { ProvvedimentoFindService } from './provvedimento-find.service';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { ProvvedimentiOrigineEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-origine.enum';
import { CreateProvvLavorazioneInput } from '../consultazioni-graphql/entities/dto/create-provvLavorazione.input';
import { ArgsDepositoProvvedimentoInput } from './entities/dto/provvLavorazione.input';
import { CspbackendTimbripubbService } from 'src/atti/cspbackend-timbripubb.service';
import { ApiOkResponseArray } from '../decorators/ApiOkResponseArray';
import { RicercaSentenzaService } from 'src/ricerca-sentenza/ricerca-sentenza.service';
import { PenalePqmService } from 'src/penale-pqm/penale-pqm.service';
import { SpoglioService } from '../spoglio/spoglio.service';

@ApiTags('provvedimenti-estensore')
@ApiBearerAuth('access-token')
@Controller('provvedimento')
export class ProvvedimentiController {
  private logger = new Logger(ProvvedimentiController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly provvLavorazService: ProvvLavorazioneService,
    private readonly provvedimentoService: ProvvedimentiService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly verbaleRicorsoService: VerbaleRicorsoService,
    private readonly placeholderService: PlaceholderService,
    private readonly esitiRuoloService: EsitiRuoloService,
    private readonly ruoloService: SentenzaRuoloService,
    private readonly sentenzaService: SentenzaService,
    private readonly verbaleUdienzaService: VerbaleUdienzaService,
    private readonly esitiService: EsitiService,
    private readonly ricorsoService: TRicorsoService,
    private readonly provvedimentoPresidenteService: ProvvedimentoPresidenteService,
    private readonly esitiSentService: EsitiSentService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvEditorLavorazioneService: ProvvEditorLavorazioneService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
    private readonly placeholderTextareaService: ProvvPlaceholderTextareaService,
    private readonly createHtmlIndexService: CreateHtmlIndexService,
    private readonly udienzaService: UdienzaService,
    private readonly provvedimentoFindService: ProvvedimentoFindService,
    private readonly provvedimentiEstensoreService: ProvvedimentiEstensoreService,
    private readonly cspbackendTimbripubbService: CspbackendTimbripubbService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
    private readonly penalePqmService: PenalePqmService,
    private configService: ConfigService,
    private readonly spoglioService: SpoglioService,
  ) { }

  @Get('/getInfoNote/:idProvv')
  @HttpCode(200)
  async getInfoNote(@Param('idProvv') idProvv: string): Promise<any> {
    const provv =
      await this.provvLavorazService.getProvvLavorazioneByIdProvvedimento(
        idProvv,
      );
    if (provv) {
      const ricorsoUdienza = await this.ricorsoService.ricorsoFindByNrg(
        provv.nrg,
      );
      if (ricorsoUdienza?.nrgReale) {
        const [anno, numero] = Utils.calcoloNumeroFascicolo(
          ricorsoUdienza.nrgReale.toString(),
        );
        const dto = new PresidenteDto();
        dto.nrg = provv.nrg;
        dto.annoFscicolo = anno;
        dto.numeroFascicolo = numero;
        dto.idProvv = idProvv;

        return dto;
      }
    }
    throw new InternalServerErrorException('Provvedimento non trovato');
  }

  @Get('/downloadAtto/:iUdienza/:nrg/:isOscurato')
  async downloadAtto(
    @Param('iUdienza') iUdienza: number,
    @Param('nrg') nrg: number,
    @Param('isOscurato') isOscurato: string,
  ): Promise<StreamableFile> {
    const result =
      await this.cspbackendTimbripubbService.getIdCatAttoAndIdCatAttoOscurato(
        nrg,
        iUdienza,
      );

    if (result) {
      const idAtto =
        isOscurato === 'true' ? result.idCatAttoOscurato : result.idCatAtto;
      if (idAtto) {
        return this.cspbackendTimbripubbService.downloadAtto(idAtto);
      }
    }

    throw new NotFoundException('Atto non trovato');
  }

  @Post('/saveEditorAndGeneraDocx/:idProvvLav')
  @HttpCode(200)
  async saveAllAndGeneraPdf(
    @Param('idProvvLav') idProvvLav: string,
    @Body() generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ): Promise<PenaleProvvLavFile | null> {
    this.logger.log(
      'Inizio salvataggio e generazione del pdf idProvvLav:',
      idProvvLav,
    );
    // TODO controllare che arrivano tutti i dati obbligatori
    try {
      generateProvvLavorazioneInput.allegatoOscurato =
        generateProvvLavorazioneInput.argsProvvedimento.generaOscurato;
      const fileProvvedimentoList =
        await this.provvLavorazService.getFileProvvedimentoList(
          idProvvLav,
          'docx',
        );
      const buffer = await this.provvLavorazService.saveContetAndFileProvvDocx(
        generateProvvLavorazioneInput,
        idProvvLav,
        fileProvvedimentoList,
      );

      const fileProvvedimentoListPDf =
        await this.provvLavorazService.getFileProvvedimentoList(
          idProvvLav,
          'pdf',
        );
      if (fileProvvedimentoListPDf && fileProvvedimentoListPDf.length > 0) {
        for (const r of fileProvvedimentoListPDf) {
          if (r.idCategoria) {
            await this.provvLavorazService.deleteFile(r.idCategoria);
          }
        }
      }
      await this.provvLavorazService.generaPdfDaDocx(idProvvLav);
      // aggiungo il provvedimento alla coda di deposito per estensore/relatore
      await this.provvedimentiEstensoreService.createCodeDepositoEstensore(
        generateProvvLavorazioneInput?.addCodeFirma,
        idProvvLav,
      );
      return new PenaleProvvLavFile({ ...buffer });
    } catch (e) {
      this.logger.error(' durante il salvataggio del documento:', e);
      throw new InternalServerErrorException(e);
    }
    // await this.deleteProvvedimentoInBozza(idProvvLav);
    throw new InternalServerErrorException(
      'Nessun file passato o Errore nel caricamento del file.',
    );
  }

  @Post('/saveBozzaDocx/:idProvv')
  @HttpCode(200)
  async saveBozzaDocx(
    @Param('idProvv') idProvv: string,
    @Body() generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ): Promise<PenaleProvvLavFile | null> {
    if (idProvv) {
      try {
        const buffer = await this.generaDocx(
          idProvv,
          generateProvvLavorazioneInput,
        );
        this.logger.log(`Fine aggiornamento bozza. idProvv:${idProvv}`);
        return new PenaleProvvLavFile({ ...buffer });
      } catch (e) {
        this.logger.error('Errore nel salvataggio delle bozza', e);
        throw e;
      }
    }
    throw new GenericErrorException(
      'Id provvedimento non valorizzato.',
      CodeErrorEnumException.FIELD_MANDATORY,
    );
  }

  @Post('saveFileLavorazione/:idProvvLav')
  @UseInterceptors(FilesInterceptor('files'))
  @HttpCode(200)
  async uploadFile(
    @UploadedFiles() files: Array<Express.Multer.File>,
    @Param('idProvvLav') idProvvLav: string,
    @Body('codeFirma') codeFirma: string,
  ) {
    try {
      const provv = await this.provvedimentoService.provvedimentoById(
        idProvvLav,
      );
      if (provv && files) {
        const parsedCoda = JSON.parse(codeFirma);
        await this.provvedimentiEstensoreService.createCodeDepositoEstensore(
          parsedCoda,
          idProvvLav,
        );

        await this.provvLavorazService.deleteOldFilePDF(idProvvLav);
        const promise = await this.provvLavorazService.saveFileProvvPdf(
          files,
          provv,
        );
        return promise;
      }
    } catch (e) {
      this.logger.error(e);
    }
    await this.deleteProvvedimentoInBozza(idProvvLav);
    throw new InternalServerErrorException(
      'Nessun file passato o Errore nel caricamento del file.',
    );
  }

  @Post('/getToPdf')
  @HttpCode(200)
  async getToPdf(
    @Body() body: GenerateProvvLavorazioneInput,
  ): Promise<StreamableFile> {
    const form = new FormData();
    const buffer = await this.provvedimentoService.createProvvedimentoDocx(
      body.nrg,
      body.idUdienza,
      body.argsProvvedimento,
    );
    if (buffer) {
      form.append('file', buffer, {
        filename: 'provvedimento.docx',
        contentType:
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });
      form.append('registro', Buffer.from('CASSPENALE'), {
        contentType: 'application/json',
      });
      const urlserviziDepositi =
        this.configService.get('app.depositiUrl') + '/provvedimenti/to-pdf';
      this.logger.debug(`call servizi depositi:${urlserviziDepositi}`);
      const pdf = await axios
        .post(urlserviziDepositi, form, {
          headers: form.getHeaders(),
          responseType: 'arraybuffer',
        })
        .then(response => {
          if (response.status === 200) {
            return response.data;
          } else {
            throw new ServiziDepositoException('depError');
          }
        })
        .catch(err => {
          if (err.response != undefined)
            throw new ServiziDepositoException(err.response.data);
          else {
            throw new ServiziDepositoException('noServiceError');
          }
        });
      return new StreamableFile(pdf, {
        type: 'application/pdf',
      });
    }
    throw new InternalServerErrorException('Errore nella generazione del PDF');
  }

  @Post('/getToPdf/:idCat')
  @HttpCode(200)
  async getToPdfByIdProvv(
    @Param('idCat') idCat: string,
  ): Promise<StreamableFile> {
    if (idCat) {
      const file = await this.provvLavorazService.getProvvLavorazioneByIdCat(
        idCat,
      );

      if (file?.content) {
        const pdf = await this.provvLavorazService.generatePdf(file.content);
        return new StreamableFile(pdf, {
          type: 'application/pdf',
        });
      }
    } else {
      throw new InternalServerErrorException(
        "L'idProvvedimento deve essere valorizzato",
      );
    }
    throw new InternalServerErrorException('Errore nella generazione del PDF');
  }

  @Post('/depositaProvvedimentoLavorazione')
  @UseInterceptors(FilesInterceptor('files'))
  @HttpCode(200)
  async depositaProvvedimentoLavorazione(
    @Body() depProvvLavorazioneInput: FirmaProvvLavorazioneInput,
    @UploadedFiles() files: Array<Express.Multer.File>,
  ): Promise<string> {
    try {
      const form = new FormData();
      form.append('file', files[0].buffer, {
        filename: 'provvedimento.docx',
        contentType:
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });
      form.append('fileXml', Buffer.from(files[1].buffer), {
        filename: 'datiAtto.xml',
        contentType: 'application/xml',
      });
      const idCatProvvLav =
        await this.provvLavorazService.depositaProvvedimentoCurrent(
          depProvvLavorazioneInput,
          form,
        );

      return idCatProvvLav;
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }

  @Delete('/deleteProvvedimento/:idProvv')
  @HttpCode(200)
  async deleteProvvedimentoInBozza(
    @Param('idProvv') idProvv: string,
  ): Promise<boolean> {
    const penaleProvvedimentiEntityPromise =
      await this.provvLavorazService.deleteProvvLavorazione(idProvv);
    if (penaleProvvedimentiEntityPromise) {
      return true;
    }
    return false;
  }

  @Post('/depositaProvvPersonalizzato')
  @HttpCode(200)
  async depositaProvvProsonalizzato(
    @Body() depProvvProsonalizzatoInput: any,
  ): Promise<string> {
    const codiceFiscale = this.authService.getCurrentUser();
    const form = new FormData();
    let deposito;
    try {
      if (depProvvProsonalizzatoInput.firmato) {
        form.append(
          'filePdf',
          Buffer.from(depProvvProsonalizzatoInput.filePdf),
          {
            filename:
              depProvvProsonalizzatoInput.dispositivoProvvedimento + '.pdf.p7m',
            contentType: 'application/x-pkcs7-mime',
          },
        );

        form.append(
          'fileXml',
          Buffer.from(depProvvProsonalizzatoInput.fileXml),
          {
            filename:
              depProvvProsonalizzatoInput.dispositivoProvvedimento + '.xml.p7m',
            contentType: 'application/x-pkcs7-mime',
          },
        );
        deposito = await this.provvLavorazService.depositaProvvedimento(
          form,
          await codiceFiscale,
          depProvvProsonalizzatoInput,
        );
      } else {
        form.append('file', Buffer.from(depProvvProsonalizzatoInput.filePdf), {
          filename:
            depProvvProsonalizzatoInput.dispositivoProvvedimento + '.pdf',
          contentType: 'application/pdf',
        });
        deposito = await this.provvLavorazService.depositaProvvedimento(
          form,
          await codiceFiscale,
          depProvvProsonalizzatoInput,
        );
      }

      return deposito;
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }

  async getProvvLavorazione(@Param('nrg') nrg: number) {
    try {
      const idAutore = await this.authService.getCurrentId();
      const dati = await this.provvLavorazService.getProvvLavorazione(
        nrg,
        idAutore,
      );
      return dati;
    } catch (e) {
      throw new InternalServerErrorException('errReadProvvDB');
    }
  }

  async getProvvLavorazioneByIdProvv(@Param('idProvv') idProvv: string) {
    try {
      const idAutore = await this.authService.getCurrentId();
      const dati = await this.provvLavorazService.getProvvLavorazioneByIdProvv(
        idProvv,
        idAutore,
      );
      return dati;
    } catch (e) {
      this.logger.error('Errore nell salvataggio del provvedimento', e);
      throw new InternalServerErrorException('errReadProvvDB');
    }
  }

  async getProvvLavorazioneByIdProvvUnlessCF(
    @Param('idProvv') idProvv: string,
  ) {
    try {
      const idAutore = await this.authService.getCurrentId();
      const dati =
        await this.provvLavorazService.getProvvLavorazioneByIdProvvedimento(
          idProvv,
        );
      return dati;
    } catch (e) {
      throw new InternalServerErrorException('errReadProvvDB');
    }
  }

  async getProvvedimentoLavorazioneByIdCat(@Param('idCat') idCat: string) {
    try {
      const idAutore = await this.authService.getCurrentId();
      const dati = await this.provvLavorazService.getProvvLavorazioneByIdCat(
        idCat,
      );
      if (dati) {
        return dati;
      }
    } catch (e) {
      throw new InternalServerErrorException('errReadProvvDB');
    }
    throw new InternalServerErrorException('errReadProvvDB');
  }

  @Get('/downloadDocx/:idProvv')
  async getProvvDocx(
    @Param('idProvv') idProvv: string,
  ): Promise<StreamableFile> {
    const provv = await this.getProvvLavorazioneByIdProvv(idProvv);

    if (provv) {
      const file = await this.provvLavorazService.getFileProvvedimentoByOne(
        idProvv,
        'docx',
      );

      if (file) {
        return new StreamableFile(file.content, {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          disposition: `attachment;filename=${file.nomeFile}`,
        });
      }
    }
    throw new InternalServerErrorException();
  }

  @Get('/downloadDocxForRedazione/:idProvv')
  async downloadDocxForRedazione(
    @Param('idProvv') idProvv: string,
  ): Promise<StreamableFile> {
    const provv = await this.getProvvLavorazioneByIdProvv(idProvv);

    if (provv) {
      const file = await this.provvLavorazService.getFileProvvedimentoByOne(
        idProvv,
        'docx',
      );
      const relazione =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
          idProvv,
        );
      if (relazione) {
        const provvDupliato = await this.getProvvLavorazioneByIdProvv(
          relazione?.idProvvedimentoOrigine,
        );

        // fixme codice inutile
        /*    if (
          provvDupliato?.stato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE  &&
        ) {
          await this.provvedimentoService.changeProvvFromRedazioneToUpload(
            provv,
          );
        }*/
      }
      if (file) {
        return new StreamableFile(file.content, {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          disposition: `attachment;filename=${file.nomeFile}`,
        });
      }
    }
    throw new InternalServerErrorException();
  }

  @Get('/downloadDatiAtto/:nrg')
  async getProvvDatiAtto(@Param('nrg') nrg: number): Promise<StreamableFile> {
    const provv = await this.getProvvLavorazione(nrg);

    if (provv?.idProvvedimento) {
      const file = await this.provvLavorazService.getFileProvvedimentoByOne(
        provv.idProvvedimento,
        'xml',
      );
      if (file) {
        return new StreamableFile(file.content, {
          type: 'application/xml',
        });
      }
    }

    throw new InternalServerErrorException();
  }

  @Get('/getTemplateDocx/:idUdienza/:nrg')
  async getTemplateDocx(
    @Param('idUdienza') idUdienza: number,
    @Param('nrg') nrg: number,
  ): Promise<StreamableFile> {
    const { bufferPromise } = await this.generateTemplateDocxBuffer(
      idUdienza,
      nrg,
    );

    if (bufferPromise) {
      return new StreamableFile(bufferPromise, {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });
    }
    throw new InternalServerErrorException();
  }

  @Get('/downloadDatiAttoByIdProvv/:idProvv')
  async downloadDatiAttoByIdProvv(
    @Param('idProvv') idProvv: string,
  ): Promise<StreamableFile> {
    const provv = await this.getProvvLavorazioneByIdProvv(idProvv);

    if (provv?.idProvvedimento) {
      const file = await this.provvLavorazService.getFileProvvedimentoByOne(
        provv.idProvvedimento,
        'xml',
      );
      if (file) {
        return new StreamableFile(file.content, {
          type: 'application/xml',
        });
      }
    }

    throw new InternalServerErrorException();
  }

  @Post('/getTemplateZip/:idUdienza')
  async getTemplateZip(
    @Param('idUdienza') idUdienza: number,
    @Body()
    list: {
      nrgList: Array<number>;
    },
  ): Promise<StreamableFile> {
    this.logger.log(
      `Richiesta di download archivio intestazione. idUdienza:${idUdienza}, ngrList: ${JSON.stringify(
        list,
      )}`,
    );

    if (list?.nrgList) {
      return this.genereteZipIntestazione(idUdienza, list.nrgList);
    }
    throw new InternalServerErrorException();
  }

  async genereteZipIntestazione(idUdienza: number, nrgs: Array<number>) {
    const zip = new JSZip();
    const pilotInfo = new PilotInfo();
    const fascicoliInfos = new Array<FascicoloInfo>();
    let datiUdienza = new UdienzaInfo();
    const promises: Promise<unknown>[] = [];
    for (const nrg of nrgs) {
      const { udienzaInfo, fascicoloInfo } =
        await this.createBufferIntestazioni(zip, idUdienza, nrg);
      if (fascicoloInfo != null) {
        fascicoliInfos.push(fascicoloInfo);
        datiUdienza = udienzaInfo;
      }
    }
    pilotInfo.udienzaInfo = datiUdienza;
    pilotInfo.fascicoliInfos = fascicoliInfos;
    zip.file(
      'indice.html',
      this.createHtmlIndexService.createPilotForIntestazione(pilotInfo),
    );
    await Promise.all(promises);
    // Aspetta che tutti i download terminino
    const res = await zip.generateAsync({
      type: 'arraybuffer',
    });
    return new StreamableFile(Buffer.from(res), { type: 'application/zip' });
  }

  async createBufferIntestazioni(zip: JSZip, idUdienza: number, nrg: number) {
    const { bufferPromise, udienzaInfo, fascicoloInfo } =
      await this.generateTemplateDocxBuffer(idUdienza, nrg);
    fascicoloInfo.folder = udienzaInfo.numero + '_' + udienzaInfo.anno;
    fascicoloInfo.anno = udienzaInfo.anno;
    fascicoloInfo.numero = udienzaInfo.numero;
    fascicoloInfo.numeroOrdine = udienzaInfo.numeroOrdine;
    fascicoloInfo.extesion = '.docx';
    fascicoloInfo.filename = `Provvedimento_${fascicoloInfo.tipoProvv}_${udienzaInfo.numero}_${udienzaInfo.anno}`;
    const folder = zip.folder(fascicoloInfo.folder);
    const filename = `${fascicoloInfo.filename}${fascicoloInfo.extesion}`;
    folder?.file(`${filename}`, bufferPromise, {
      binary: true,
    });
    return { udienzaInfo, fascicoloInfo };
  }

  @Get('/downloadPdf/:nrg')
  async getPdf(@Param('nrg') nrg: number): Promise<StreamableFile | undefined> {
    const provv = await this.getProvvLavorazione(nrg);
    if (provv?.idProvvedimento) {
      const file = await this.provvLavorazService.getFileProvvedimentoByOne(
        provv?.idProvvedimento,
        'pdf',
      );
      if (file) {
        return new StreamableFile(file?.content, { type: 'application/pdf' });
      }
      throw new InternalServerErrorException();
    }
  }

  @Get('/downloadPdfByIdProvv/:idProvv')
  async getPdfByIdProvv(
    @Param('idProvv') idProvv: string,
  ): Promise<StreamableFile | undefined> {
    const provv = await this.getProvvLavorazioneByIdProvvUnlessCF(idProvv);

    // error handling in case provv is not defined
    if (!provv) {
      throw new InternalServerErrorException(
        `Impossibile recuperare il provvedimento:+${provv}`,
      );
    }

    if (provv?.idProvvedimento) {
      if (
        provv.stato === ProvvedimentiStatoEnum.BOZZA_PRESIDENTE ||
        provv.stato === ProvvedimentiStatoEnum.IN_BOZZA
      ) {
        await this.provvLavorazService.generaPdfDaDocx(provv?.idProvvedimento);
      }
      const file =
        await this.provvLavorazService.getFileProvvedimentoPDFNonOscurato(
          provv?.idProvvedimento,
        );
      if (file) {
        return new StreamableFile(file?.content, { type: 'application/pdf' });
      }
      throw new InternalServerErrorException();
    }
  }

  @Get('/downloadPdfOscuratoByIdProvv/:idProvv')
  async downloadPdfOscuratoByIdProvv(
    @Param('idProvv') idProvv: string,
  ): Promise<StreamableFile | undefined> {
    const provv = await this.getProvvLavorazioneByIdProvvUnlessCF(idProvv);

    // error handling in case provv is not defined
    if (!provv) {
      throw new InternalServerErrorException(
        `Impossibile recuperare il provvedimento:+${provv}`,
      );
    }

    if (provv?.idProvvedimento) {
      if (
        provv.stato === ProvvedimentiStatoEnum.BOZZA_PRESIDENTE ||
        provv.stato === ProvvedimentiStatoEnum.IN_BOZZA
      ) {
        await this.provvLavorazService.generaPdfDaDocx(provv?.idProvvedimento);
      }
      const file =
        await this.provvLavorazService.getFileProvvedimentoPDFOscurato(
          provv?.idProvvedimento,
        );
      if (file) {
        return new StreamableFile(file?.content, { type: 'application/pdf' });
      }
      throw new InternalServerErrorException();
    }
  }

  @Get('/downloadByIdCat/:idCat')
  async downloadByIdCat(
    @Param('idCat') idCat: string,
  ): Promise<StreamableFile | undefined> {
    const provvedimentoLavorazioneByIdCat =
      await this.getProvvedimentoLavorazioneByIdCat(idCat);

    if (provvedimentoLavorazioneByIdCat) {
      return new StreamableFile(provvedimentoLavorazioneByIdCat?.content, {
        type: 'application/' + provvedimentoLavorazioneByIdCat.tipoFile,
      });
    }
    throw new InternalServerErrorException();
  }

  @Post('/verifica')
  async verificaPdf(@Body() file: any) {
    try {
      const signature = new SignatureChecker();
      const checked = true;
      const errorMsg: any = {
        xml: 'xmlNonFirmato',
        pdf: 'pdfNonFirmato',
        pdfXml: 'pdfXmlNonFirmati',
      };
      const allErrors: any[] = [];

      file.base64.forEach((item: any) => {
        const [type, base64] = item.base64.split(',');
        const buf = Buffer.from(base64, 'base64');
        let response;

        if (type === 'data:application/pdf;base64') {
          try {
            response = signature.verifyPdf(buf)[0];
          } catch (e) {
            throw new InternalServerErrorException({
              type: 'error',
              msg: errorMsg.pdf,
            });
          }
        } else {
          try {
            response = signature.verifyP7m(buf);
          } catch (e) {
            throw new InternalServerErrorException({
              type: 'error',
              msg: errorMsg.xml,
            });
          }
        }
        if (!response.integrity) {
          this.logger.warn('Verifica integrità non riuscita');
        }
      });

      if (!checked) {
        if (allErrors.length > 1) {
          return {
            type: 'error',
            msg: errorMsg.pdfXml,
          };
        }
        return {
          type: 'error',
          msg: errorMsg[allErrors[0]],
        };
      }

      return {
        type: 'success',
        msg: 'success',
      };
    } catch (e) {
      return e.response;
    }
  }

  @Get('/getProvvedimentiDaFirmare/:idProvv')
  @ApiOkResponseArray(PenaleProvvLavFile, 'restitusice i file da firmare ')
  async getProvvedimentiDaFirmare(
    @Param('idProvv') idProvv: string,
  ): Promise<PenaleProvvLavFile[]> {
    const newVar =
      await this.provvLavorazService.getFilesProvvedimentoDaFirmare(idProvv);
    const penaleProvvLavFiles = newVar.map((pro: PenaleProvvLavFileEntity) => {
      return new PenaleProvvLavFile({ ...pro });
    });
    return penaleProvvLavFiles;
  }

  @Get('/utenteLoggato')
  async utenteLoggato(): Promise<any> {
    return await this.authService.getCurrentUser();
  }

  /**
   * Viene usanto dal estensore per duplicare un provvedimento sentenza ordinanza o
   * @param idProvv
   */
  @Get('/duplicate/:idProvv')
  @ApiOkResponse({
    status: 200,
    type: String,
    description: "Duplica il provvedimento e restituisce l'id duplicato",
  })
  async duplicateProvvvidemento(
    @Param('idProvv') idProvv: string,
  ): Promise<string> {
    let modificaPresidente = false;
    const { provv, isPresidente, isEstensore } =
      await this.provvedimentoPresidenteService.getProvvIsEstensoreIsPresidente(
        idProvv,
      );
    if (isPresidente) {
      await this.provvedimentoPresidenteService.checkOperation(provv);
    }
    const provvCopiato =
      await this.provvedimentoPresidenteService.creaCopyDelProvvedimento(
        provv,
        isPresidente,
        isEstensore,
      );

    if (provvCopiato) {
      if (isPresidente && !isEstensore) {
        modificaPresidente = true;
      }
      const changeStatusValue = new CreateProvvedimentiChangeStatusInput();

      if (modificaPresidente) {
        provvCopiato.modificaPresidente = true;
      }

      const newStatoPRovv = modificaPresidente
        ? ProvvedimentiStatoEnum.BOZZA_PRESIDENTE
        : ProvvedimentiStatoEnum.IN_BOZZA;
      changeStatusValue.stato = newStatoPRovv;
      changeStatusValue.idAutore = await this.authService.getCurrentId();
      changeStatusValue.prevStato = provvCopiato.stato;
      provvCopiato.stato = newStatoPRovv;
      provvCopiato.dataUltimaModifica = new Date();
      const provvedimentoCopiatoDB =
        await this.provvedimentoService.updateProvvedimentoAll(provvCopiato);
      if (provvedimentoCopiatoDB?.idProvvedimento) {
        const file =
          await this.provvLavorazService.getFileProvvedimentoAllByIdProvv(
            idProvv,
          );
        if (file) {
          await this.provvedimentoPresidenteService.duplicaProvvedimentoFile(
            file,
            provvedimentoCopiatoDB?.idProvvedimento,
          );
        } else {
          throw new GenericErrorException(
            'nessun file da dupplicata',
            CodeErrorEnumException.FILE_NOT_FOUND,
          );
        }
        changeStatusValue.idProvvedimento =
          provvedimentoCopiatoDB.idProvvedimento;
        await this.changeStatusService.createProvvedimentoChangeStatus(
          changeStatusValue,
        );
        // si tratta di un provvedimento creato da una redazione online quindi l'editor va duplicato se vene
        // caricato da locale cioe da file non deve essere duplicato
        if (
          provvedimentoCopiatoDB.origine === ProvvedimentiOrigineEnum.SYSTEM
        ) {
          await this.provvLavorazService.duplicateEditor(
            idProvv,
            provvedimentoCopiatoDB.idProvvedimento,
          );
        }
        await this.provvedimentoRelazioneService.insertRelazioneTraProvv(
          provvedimentoCopiatoDB?.idProvvedimento,
          idProvv,
        );
        return provvCopiato?.idProvvedimento;
      }
    }
    throw new GenericErrorException(
      'Provvedimento non copiato',
      CodeErrorEnumException.PROVV_NOT_DUPLICATED,
    );
  }

  @Get('/getProvvedimentiContentDocx/:idProvv')
  @ApiOkResponse({
    status: 200,
    type: PenaleProvvEditor,
    description: "Restituisce il provvedimento salvata nell'editor",
  })
  async getProvvedimentiContentDocx(
    @Param('idProvv') idProvv: string,
  ): Promise<PenaleProvvEditor | null> {
    const cf = await this.authService.getCurrentUser();
    const provv = await this.provvedimentoService.provvedimentoById(idProvv);
    if (provv) {
      return this.provvedimentoFindService.getEditorOnlineTesti(
        idProvv,
        provv.idUdienza,
        provv.nrg,
        cf,
      );
    }
    throw new InternalServerErrorException('Provvedimento non trovato');
  }

  @Get('/getProvvedimentoOscurato/:idUdienza/:nrg')
  @ApiOkResponse({
    status: 200,
    type: Boolean,
    description: 'Restituisce true se il provvedimento è oscurato',
  })
  async getProvvedimentoOscurato(
    @Param('idUdienza') idUdienza: number,
    @Param('nrg') nrg: number,
  ): Promise<boolean> {
    if (nrg) {
      return await this.provvedimentoService.getProvvedimentoOscuratoSicComplessivo(
        idUdienza,
        nrg,
      );
    }
    return false;
  }

  @Get('/getTipoProvvByIdUdienza/:idUdienza/:ordine')
  @ApiOkResponse({
    status: 200,
    description: 'Restituisce il tipo di provvedimento per Id udienza',
  })
  async getTipoProvvByIdUdienza(
    @Param('idUdienza') idUdienza: number,
    @Param('ordine') ordine: number,
  ): Promise<ProvvedimentiTipoEnum | null> {
    let nrg = null;
    let idRicUdienza = null;
    try {
      [nrg, idRicUdienza] =
        await this.ricorsoUdienzaService.getNrgForProvvedimenti(
          idUdienza,
          ordine,
        );
    } catch (e) {
      this.logger.log('error nella ricerca:', e);
      throw new InternalServerErrorException('Udienza non trovata');
    }
    return this.getTipoProvvByNrg(idUdienza, nrg);
  }

  @Get('/getTipoProvvByNrg/:idUdien/:nrg')
  @ApiOkResponse({
    status: 200,
    description: 'Restituisce il tipo di provvedimento',
  })
  async getTipoProvvByNrg(
    @Param('idUdien') idUdien: number,
    @Param('nrg') nrg: number,
  ): Promise<ProvvedimentiTipoEnum | null> {
    if (nrg) {
      return this.provvedimentoService.getTipoProvvedimento(idUdien, nrg);
    }
    throw new InternalServerErrorException('Nrg non valido.');
  }

  @Get('/getTestiIniziali/:idUdienza/:nrg')
  @ApiOkResponse({
    status: 200,
    type: InitialRedazioneOnlineModel,
    description: 'Restituisce i testi Iniziali',
  })
  async getTestIniziali(
    @Param('idUdienza') idUdienza: number,
    @Param('nrg') nrg: number,
  ): Promise<InitialRedazioneOnlineModel | null> {
    return await this.getTestiIniziali(idUdienza, nrg, false);
  }

  @Get('/getTipoProvvedimentoAndSemplificata/:idUdienza/:nrg')
  async getTipoProvvedimento(
    @Param('idUdienza') idUdienza: number,
    @Param('nrg') nrg: number,
  ): Promise<{
    tipoProvvedimento: ProvvedimentiTipoEnum;
    semplificata: boolean;
  } | null> {
    this.logger.log(
      `Inizio chiamata al servizio getTipoProvvedimentoAndSemplificata con nrg:${nrg}`,
    );
    const currentUserCf = await this.authService.getCurrentUser();
    const tipoAndSemplificataProvv =
      await this.provvedimentoFindService.getTipoAndSemplificataProvv(
        idUdienza,
        nrg,
        currentUserCf,
      );
    this.logger.log(
      `Fine chiamata al servizio getTipoProvvedimentoAndSemplificata con nrg:${nrg} and result:${tipoAndSemplificataProvv}`,
    );
    return tipoAndSemplificataProvv;
  }

  @Get('/placeholder/:idUdienza/:nrg')
  @ApiOkResponse({
    status: 200,
    type: ProvvPlaceholderDto,
    description: 'Restituisce i placeholder dei template email',
  })
  async getProvvPlaceholder(
    @Param('idUdienza') idUdienza: number,
    @Param('nrg') nrg: number,
    intestazione?: boolean,
  ): Promise<ProvvPlaceholderDto> {
    const contextValueDtos: ContextValueDto[] =
      await this.placeholderTextareaService.getPlaceholder(nrg);
    const provvPlaceholderDto = new ProvvPlaceholderDto();
    provvPlaceholderDto.context = contextValueDtos;
    return provvPlaceholderDto;
  }

  @Get('/addCodaDeposito/:idProvv')
  @ApiOkResponse({
    status: 200,
    type: PenaleProvvedimenti,
    description: 'Aggiunge alla code di deposito',
  })
  async addCodaDeposito(
    @Param('idProvv') idProvv: string,
  ): Promise<PenaleProvvedimenti | undefined> {
    this.logger.log(
      `Inizio chiamata al servizio addCodaDeposito con idProvvedimento:${idProvv}`,
    );
    const semplificataETipoProvv =
      await this.provvedimentoFindService.addCodaDeposito(idProvv);
    this.logger.log(
      `Fine chiamata al servizio addCodaDeposito con idProvvedimento:${idProvv} and result:${semplificataETipoProvv}`,
    );
    return semplificataETipoProvv;
  }

  @Get('/generaPdfToDocx/:idProvv')
  @ApiOkResponse({
    status: 200,
    type: Boolean,
    description: 'Restituisce true se genera il pdf',
  })
  async generaPdfToDocx(
    @Param('idProvv') idProvv: string,
  ): Promise<boolean | undefined> {
    this.logger.log(`Genero i pdf per il provvedimento: idProvv:${idProvv}`);
    return this.provvLavorazService.generaPdfDaDocxApi(idProvv);
  }

  private async getTestiIniziali(
    idUdienza: number,
    nrg: number,
    intestazione: boolean,
  ) {
    let idRicUdienza = null;
    try {
      idRicUdienza =
        await this.ricorsoUdienzaService.getIdRicUdienzaByNrgAndIdUdienza(
          idUdienza,
          nrg,
        );
    } catch (e) {
      this.logger.error('error nella ricerca:', e);
      throw new InternalServerErrorException('Udienza non trovata');
    }
    if (nrg) {
      const parti = await this.placeholderService.partiProvvedimento(
        nrg,
        idRicUdienza,
      );
      const provvImpugnato = await this.placeholderService.provvImpugnato(
        nrg,
        idRicUdienza,
      );
      const spoglio = await this.spoglioService.spoglioFindByNrg(nrg);
      const atti = await this.placeholderService.atti(nrg, idRicUdienza, spoglio?.deplano);
      const verbaleRicorso = idRicUdienza
        ? await this.verbaleRicorsoService.verbaleRicorso(idRicUdienza)
        : null;
      const inforTesto = new InitialRedazioneOnlineModel();
      inforTesto.introduzione =
        (!intestazione
          ? parti + '<br/>' + provvImpugnato + '<br/><br/>'
          : '') +
        atti +
        '<br/><br/>' +
        (verbaleRicorso?.testoVerbale
          ? ' <br/><br/> ' +
          verbaleRicorso.testoVerbale
            .trim()
            .toLowerCase()
            .replace(/(^\w{1})|(?<=\.\s+)\w|\s+(?=\w+$)/g, letter =>
              letter.toUpperCase(),
            )
          : '');

      if (!atti.includes('udit')) {
        const verbaleUdienza =
          await this.verbaleUdienzaService.verbaleUdienzaByIdUdienza(idUdienza);
        if (verbaleUdienza?.testoApertura)
          inforTesto.introduzione += verbaleUdienza?.testoApertura;
      }

      inforTesto.introduzione = inforTesto.introduzione.replace(
        new RegExp('\r?\n', 'g'),
        '<br />',
      );
      inforTesto.tipoProvvedimento =
        await this.provvedimentoService.getTipoProvvedimento(idUdienza, nrg);
      inforTesto.oscurato =
        await this.provvedimentoService.getProvvedimentoOscuratoSicComplessivo(
          idUdienza,
          nrg,
        );
      const esito = await this.esitiService.esito(idRicUdienza);
      if (esito) {
        inforTesto.semplificata = esito?.semplificata === '1';
        const sentenza = await this.calcolaSentenza(esito);
        if (sentenza?.sentenza) {
          const [anno, numero] = Utils.calcolaIdSezionale(
            sentenza.sentenza + '',
          );
          if (numero && anno) {
            inforTesto.idSezionale = numero + '/' + anno;
          }
        }
      }
      const esitoRuolo = await this.esitiRuoloService.esitoRuolo(
        nrg,
        idUdienza,
      );
      if (esitoRuolo?.idEsitoRuolo) {
        const ruoloSentezza = await this.ruoloService.sentenza(
          esitoRuolo?.idEsitoRuolo,
        );
        if (ruoloSentezza) {
          const pqm = ruoloSentezza?.pqm?.toLowerCase();
          const char = pqm[0];
          if (pqm) inforTesto.pqm = pqm.replace(char, char.toUpperCase());
        }
      }
      //In caso di assenza del PQM da ruolo Presidente dovremmo necessariamente utilizzare quello di amministratore
      else {
        const esitoSentenza =
          await this.ricercaSentenzaService.sentenzaByIdRicUdien(idRicUdienza);
        if (esitoSentenza && esitoSentenza.idSent) {
          const penaleSentenza = await this.sentenzaService.sentenza(
            esitoSentenza.idSent,
          );
          if (penaleSentenza && penaleSentenza.idPqm) {
            const penalePqm = await this.penalePqmService.penalePqmByIdPqm(
              penaleSentenza.idPqm,
            );
            if (penalePqm) {
              const pqm = penalePqm.testo?.toLowerCase();
              const char = pqm[0];
              if (pqm) inforTesto.pqm = pqm.replace(char, char.toUpperCase());
            }
          }
        }
      }
      inforTesto.tipoProvvedimento =
        inforTesto.tipoProvvedimento || ProvvedimentiTipoEnum.ORDINANZA;
      if (inforTesto.introduzione)
        inforTesto.introduzione = inforTesto.introduzione.replace(
          inforTesto.introduzione[0],
          inforTesto.introduzione[0].toUpperCase(),
        );
      if (inforTesto.motivoRicorso)
        inforTesto.motivoRicorso = inforTesto.motivoRicorso.replace(
          inforTesto.motivoRicorso[0],
          inforTesto.motivoRicorso[0].toUpperCase(),
        );
      if (inforTesto.finaleDispositivo)
        inforTesto.finaleDispositivo = inforTesto.finaleDispositivo.replace(
          inforTesto.finaleDispositivo[0],
          inforTesto.finaleDispositivo[0].toUpperCase(),
        );
      return inforTesto;
    }
    throw new InternalServerErrorException('Errore');
  }

  private async generaDocx(
    idProvv: string,
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ) {
    const fileProvvedimentoList =
      await this.provvLavorazService.getFileProvvedimentoList(idProvv, 'docx');

    const buffer = await this.provvLavorazService.saveContetAndFileProvvDocx(
      generateProvvLavorazioneInput,
      idProvv,
      fileProvvedimentoList,
    );

    if (
      (fileProvvedimentoList.length < 2 &&
        this.isgenerateOscurato(generateProvvLavorazioneInput)) ||
      (fileProvvedimentoList.length > 1 &&
        !this.isgenerateOscurato(generateProvvLavorazioneInput))
    ) {
      const createProvvLavorazioneInput = new CreateProvvLavorazioneInput();
      createProvvLavorazioneInput.nrg = generateProvvLavorazioneInput.nrg;
      createProvvLavorazioneInput.idUdienza =
        generateProvvLavorazioneInput.idUdienza;
      createProvvLavorazioneInput.allegatoOscurato =
        generateProvvLavorazioneInput.allegatoOscurato ||
        generateProvvLavorazioneInput.argsProvvedimento.generaOscurato;
      createProvvLavorazioneInput.argsProvvedimento =
        new ArgsDepositoProvvedimentoInput();
      createProvvLavorazioneInput.argsProvvedimento.anRuolo =
        generateProvvLavorazioneInput.argsProvvedimento.anRuolo;
      createProvvLavorazioneInput.argsProvvedimento.numRuolo =
        generateProvvLavorazioneInput.argsProvvedimento.numRuolo;
      createProvvLavorazioneInput.argsProvvedimento.tipologiaProvvedimento =
        generateProvvLavorazioneInput.argsProvvedimento.tipologiaProvvedimento;
      const bufferDatiAtto = await this.provvedimentoService.generaDatiAtto(
        createProvvLavorazioneInput,
      );
    }
    return buffer;
  }

  private isgenerateOscurato(
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ) {
    return !!(
      generateProvvLavorazioneInput?.allegatoOscurato ||
      generateProvvLavorazioneInput?.argsProvvedimento?.generaOscurato
    );
  }

  private async generateTemplateDocxBuffer(idUdienza: number, nrg: number) {
    const fascicoloInfo = new FascicoloInfo();
    const udienzaInfo = new UdienzaInfo();
    const testIniziali = await this.getTestiIniziali(idUdienza, nrg, true);
    const args = new ArgsProvvedimentoInput();
    let introduzione = testIniziali?.introduzione;

    introduzione = introduzione
      ? introduzione
        .replace('<br/>', '\n ')
        .replace('<br>', '\n ')
        .replace(/<br(.*?)>/g, '\n ')
      : introduzione;

    args.introduzione = '<p>' + introduzione + '</p>';
    args.tipologiaProvvedimento =
      testIniziali?.tipoProvvedimento || ProvvedimentiTipoEnum.ORDINANZA;
    args.pqm = testIniziali?.pqm;
    fascicoloInfo.tipoProvv = args.tipologiaProvvedimento;
    const udienza = await this.udienzaService.udienza(idUdienza);

    udienzaInfo.dataUdienza = udienza?.dataUdienza || new Date();
    args.dataDecisione = udienza?.dataUdienza
      ? moment(udienza?.dataUdienza).add(2, 'h').toDate()
      : new Date();
    const ricorso = await this.ricorsoService.ricorsoFindByNrg(nrg);
    if (!ricorso?.nrgReale) {
      throw new InternalServerErrorException('Ricorso non esistente');
    }

    const ricorsoUdienza =
      await this.ricorsoUdienzaService.ricorsoUdienzaByNrgAndIdUdienza(
        idUdienza,
        nrg,
      );
    udienzaInfo.numeroOrdine = ricorsoUdienza?.numOrdine || 0;
    const [anno, numero] = Utils.calcoloNumeroFascicolo(
      ricorso.nrgReale.toString(),
    );
    udienzaInfo.anno = anno || 0;
    udienzaInfo.numero = numero || 0;
    udienzaInfo.nrg = nrg;
    udienzaInfo.tipoUdienza = udienza?.tipoUdienza?.descrizione || '';
    udienzaInfo.aula = udienza?.aula?.descrizione || '';
    udienzaInfo.sezione = udienza?.sezione?.descrizione || '';
    args.anRuolo = anno || 0;
    args.numRuolo = numero || 0;
    args.generaOscurato = false;
    const bufferPromise = await this.provvedimentoService.createTemplateDocx(
      ricorsoUdienza?.idRicudien || 0,
      nrg,
      idUdienza,
      args,
    );
    return { bufferPromise, udienzaInfo, fascicoloInfo };
  }

  private async calcolaSentenza(
    esito: PenaleTEsitoEntity,
  ): Promise<PenaleTSentenzaEntity | null> {
    if (esito?.idEsito) {
      const esitoSent = await this.esitiSentService.esitoSent(esito.idEsito);
      if (esitoSent?.idSent) {
        return await this.sentenzaService.sentenza(esitoSent.idSent);
      }
    }
    return null;
  }

  @Post('/convertToPdf')
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(200)
  async convertToPdf(
    @UploadedFile() file: Express.Multer.File,
    @Headers('X-Origin') origine?: string,
  ): Promise<StreamableFile> {
    const form = new FormData();
    form.append('file', file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });

    const urlServiziDepositi =
      this.configService.get('app.depositiUrl') + '/provvedimenti/to-pdf';

    try {
      const response = await axios.post(urlServiziDepositi, form, {
        headers: {
          ...form.getHeaders(),
          'Content-Type': 'multipart/form-data',
          ...(origine && { 'X-Origin': origine }),
        },
        responseType: 'arraybuffer',
      });

      if (response.status === 200) {
        return new StreamableFile(response.data, { type: 'application/pdf' });
      } else {
        throw new ServiziDepositoException('depError');
      }
    } catch (err) {
      if (err.response) {
        throw new ServiziDepositoException(err.response.data);
      } else {
        throw new ServiziDepositoException('noServiceError');
      }
    }
  }
}
