import { Logger, NotFoundException } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { CodeDeposito } from '../models/code_deposito.model';
import { CodeDepositoService } from '../../code-deposito/code-deposito.service';
import { CodeDepositoInput } from '../entities/dto/code-deposito.input';
import { AuthService } from '../../auth/auth.service';
import { CodeDepositoDto } from '../models/dto/code_deposito_dto.model';
import { TRicorsoUdienzaService } from 'src/ricorso-udienza/t-ricorso-udienza.service';
import { ProvvedimentiService } from 'src/provvedimenti/provvedimenti.service';
import { PenaleProvvedimentiEntity } from '../entities/penale_provvedimenti.entity';
import { PenaleTRicorsoUdienzaEntity } from '../entities/penale_t_ricudien.entity';
import { PenaleUdienzaService } from 'src/penale-udienza/penale-udienza.service';
import { PenaleUdienzaEntity } from '../entities/penale_udienza.entity';
import { TRicorsoService } from 'src/fascicolo/t-ricorso.service';
import { PenaleTRicorsoEntity } from '../entities/penale_t_ricorso.entity';
import { Utils } from '../../utils/utils';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { ProvvedimentoChangeStatusService } from 'src/provvedimento-change-status/provvedimento-change-status.service';
import { ProvvedimentoRelazioneService } from '../../provvedimento-change-status/provvedimento-relazione.service';
import * as moment from 'moment';
import { ProvvedimentoNotFoundException } from 'src/exceptions/provvedimento-not-found.exception';
import { Role } from '../../auth/role.enum';
import { GenericErrorException } from '../../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../../exceptions/code-error-enum.exception';

@Resolver(() => CodeDeposito)
export class CodeDepositoResolver {
  private logger = new Logger(CodeDepositoResolver.name);
  constructor(
    private readonly codeDepositoService: CodeDepositoService,
    private readonly authService: AuthService,
    private readonly recudienService: TRicorsoUdienzaService,
    private readonly provvedimentoService: ProvvedimentiService,
    private readonly penaleUdienzaService: PenaleUdienzaService,
    private readonly ricorsoService: TRicorsoService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
  ) {}

  @Query(returns => [CodeDeposito], { name: 'codeDeposito' })
  async codeDeposito(): Promise<CodeDeposito[] | null> {
    const code = await this.codeDepositoService.codeDeposito();
    return code;
  }

  @Query(() => CodeDeposito, { name: 'codeDepositoByIdProvvCf' })
  async codeDepositoByIdProvvCf(
    @Args('idProvv') idProvv: string,
  ): Promise<CodeDeposito | null> {
    const cf = await this.authService.getCurrentUser();
    const code = await this.codeDepositoService.codeDepositoByIdProvvCf(
      idProvv,
      cf,
    );
    return code;
  }

  @Query(() => CodeDeposito, { name: 'codeDepositoByIdProvv' })
  async codeDepositoByIdProvv(
    @Args('idProvv') idProvv: string,
  ): Promise<CodeDeposito | null> {
    const code = await this.codeDepositoService.codeDepositoByIdProvv(idProvv);
    return code;
  }

  @Query(() => [CodeDeposito], { name: 'codeDepositoByCf' })
  async codeDepositoByCf(
    @Args('ruolo') ruolo: Role,
  ): Promise<CodeDeposito[] | null> {
    const cf = await this.authService.getCurrentUser();
    const code = await this.codeDepositoService.codeDepositoByCf(cf);
    const codeDTO =
      code?.map(a => {
        const dto = new CodeDeposito({ ...a });
        dto.ruolo = ruolo;
        return dto;
      }) || null;
    return codeDTO;
  }

  @ResolveField('codeDepositoDto', () => CodeDepositoDto, { nullable: true })
  async getInfo(
    @Parent() codeDeposito: CodeDeposito,
  ): Promise<CodeDepositoDto | null> {
    const codeDepositoOutput = new CodeDepositoDto();
    let provvedimento: PenaleProvvedimentiEntity | null = null;
    if (
      codeDeposito.ruolo === Role.RELATORE ||
      codeDeposito.ruolo === Role.ESTENSORE
    ) {
      provvedimento = await this.provvedimentoService.provvedimentoById(
        codeDeposito.idProvv,
        ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
      );
    } else if (codeDeposito.ruolo === Role.PRESIDENTE) {
      provvedimento = await this.provvedimentoService.provvedimentoById(
        codeDeposito.idProvv,
        ProvvedimentiStatoEnum.CODA_DI_FIRMA,
      );
    } else {
      throw new GenericErrorException(
        'Ruolo non gestito',
        CodeErrorEnumException.ROLE_NOT_MANAGED,
      );
    }

    if (provvedimento?.nrg) {
      const ricorsoUdienza: PenaleTRicorsoUdienzaEntity | null =
        await this.recudienService.ricorsoUdienzaByNrgAndIdUdienza(
          provvedimento.idUdienza,
          provvedimento.nrg,
        );
      codeDepositoOutput.numOrdine = ricorsoUdienza?.numOrdine;
      codeDepositoOutput.tipoProvvedimento = provvedimento.tipo;

      const ricorso: PenaleTRicorsoEntity | null =
        await this.ricorsoService.ricorsoFindByNrg(provvedimento?.nrg);
      const [anno, numero] = Utils.calcoloNumeroFascicolo(
        String(ricorso?.nrgReale),
      );
      const newnrg = numero + '/' + anno;
      codeDepositoOutput.nrg = newnrg;

      if (ricorsoUdienza?.idUdienza) {
        const udienza: PenaleUdienzaEntity | null =
          await this.penaleUdienzaService.penaleUdienzaByIdUdienza(
            ricorsoUdienza.idUdienza,
          );

        if (udienza != null) {
          codeDepositoOutput.dataUdienza = udienza?.dataUdienza;
          codeDepositoOutput.udienza =
            udienza?.sezione +
            ', ' +
            moment(udienza.dataUdienza).format('DD/MM/YYYY') +
            ', ' +
            udienza?.tipoUdienza +
            ', ' +
            udienza?.aula;
        }
      }
    } else {
      return null;
    }

    return codeDepositoOutput;
  }

  @Mutation(() => CodeDeposito, { name: 'creaCodeDeposito' })
  async createCodeDeposito(
    @Args('codaDeposito') codaDeposito: CodeDepositoInput,
  ) {
    const coda = await this.codeDepositoService.createCodeDeposito(
      codaDeposito,
    );
    if (coda) {
      return await this.codeDepositoService.codeDepositoByIdProvvCf(
        coda?.idProvv[0],
        coda?.cf[0],
      );
    }
    throw new NotFoundException('Coda deposito non inserito');
  }

  @Mutation(() => Boolean, { name: 'eliminaCodeDeposito' })
  async deleteCodeDeposito(
    @Args('idProvv') idProvv: string,
  ): Promise<boolean | null> {
    const cf = await this.authService.getCurrentUser();
    const codeDeposito = new CodeDeposito();
    codeDeposito.cf = cf;
    codeDeposito.idProvv = idProvv;

    const provvedimentoById = await this.provvedimentoService.provvedimentoById(
      idProvv,
    );

    if (provvedimentoById) {
      const res = await this.codeDepositoService.deleteCodeDeposito(
        codeDeposito,
      );
      const isEstensore = await this.authService.isEstensoreforNrg(
        provvedimentoById?.nrg,
      );
      if (isEstensore) {
        await this.changeStatusService.deleteByIdProvv(idProvv);
        await this.provvedimentoService.updateStatoProvvedimento(
          idProvv,
          ProvvedimentiStatoEnum.IN_BOZZA,
        );
        return res;
      } else {
        const provvedimentoRelatore =
          await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
            idProvv,
          );
        await this.provvedimentoRelazioneService.deleteRelazioneSoruceDestinazione(
          idProvv,
        );
        await this.provvedimentoService.deleteProvvDuplicato(idProvv);
        if (provvedimentoRelatore) {
          const lastState =
            await this.changeStatusService.changeStatusByIdProvvAndLast(
              provvedimentoRelatore?.idProvvedimentoOrigine,
            );
          const stato = lastState
            ? lastState.stato
            : ProvvedimentiStatoEnum.MINUTA_ACCETTATA;
          await this.provvedimentoService.updateStatoProvvedimento(
            provvedimentoRelatore?.idProvvedimentoOrigine,
            stato,
          );
          const provvCompleto =
            await this.provvedimentoService.provvedimentoById(
              provvedimentoRelatore?.idProvvedimentoOrigine,
            );
          if (provvCompleto) {
            await this.recudienService.updateStatoProvvedimentoRicorsoUdienza(
              provvCompleto.idUdienza,
              provvCompleto?.nrg,
              stato,
            );
          }
        }
        return res;
      }
    }
    this.logger.log('Provvedimento non trovato. idProvv: ', idProvv);
    throw new ProvvedimentoNotFoundException('Provvedimento non trovato');
  }
}
