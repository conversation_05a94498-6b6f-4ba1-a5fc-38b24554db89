import { Module } from '@nestjs/common';
import { MultiTenantModule } from 'src/multi-tenant/multi-tenant.module';
import { RiunitiService } from './riuniti.service';
import { AuthModule } from '../auth/auth.module';
import { RicorsoUdienzaModule } from '../ricorso-udienza/ricorso-udienza.module';

@Module({
  imports: [
    AuthModule,
    RicorsoUdienzaModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [RiunitiService],
  exports: [RiunitiService],
})
export class RiunitiModule {}
