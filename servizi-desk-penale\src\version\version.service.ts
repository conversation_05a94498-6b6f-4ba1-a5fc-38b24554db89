import { Injectable, OnModuleInit, Logger, Inject } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { Log } from 'src/decorators/log.decorator';

@Injectable()
@Log()
export class VersionService implements OnModuleInit {
  private version: string;
  private serviziDepositoVersion: string;
  private readonly logger = new Logger(VersionService.name);
  private lastVersionCheck: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minuti in millisecondi
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 secondo

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    // Carico solo la versione dell'app all'avvio
    await this.loadVersion();
  }

  private async loadVersion() {
    try {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      const packageJson = await fs.readFile(packageJsonPath, 'utf-8');
      const { version } = JSON.parse(packageJson);
      this.version = version;
      this.logger.log(`App Version loaded: ${this.version}`);
    } catch (error) {
      this.version = 'unknown';
      this.logger.error(
        `Failed to load app version: ${error.message}`,
        error.stack,
      );
    }
  }

  private async loadServiziDepositoVersion() {
    try {
      const urlDeposito = `${this.configService.get(
        'app.depositiUrl',
      )}/utils/version`;

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          this.logger.debug(`Fetching version from: ${urlDeposito} (attempt ${attempt})`);
          
          const response = await axios.get(urlDeposito, {
            headers: {
              'Content-Type': 'text/plain',
            },
            timeout: 10000, // Aumentato a 10 secondi
            validateStatus: status => status === 200,
          });

          if (typeof response.data === 'string') {
            this.serviziDepositoVersion = response.data.trim();
            this.lastVersionCheck = Date.now();
            this.logger.log(
              `Servizi Deposito Version loaded: ${this.serviziDepositoVersion}`,
            );
            return;
          } else {
            throw new Error(`Unexpected response type: ${typeof response.data}`);
          }
        } catch (error) {
          if (attempt === this.MAX_RETRIES) throw error;
          await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));
        }
      }
    } catch (error) {
      this.serviziDepositoVersion = 'unknown';
      this.logger.error(
        `Failed to load servizi deposito version: ${error.message}`,
        error.stack,
        {
          url: this.configService.get('app.depositiUrl'),
          response: error.response?.data,
          status: error.response?.status,
        },
      );
    }
  }

  async getVersions(): Promise<{
    appVersion: string;
    serviziDepositoVersion: string;
  }> {
    // Verifica se è necessario ricaricare la versione (cache scaduta o non presente)
    const now = Date.now();
    if (!this.serviziDepositoVersion || now - this.lastVersionCheck > this.CACHE_TTL) {
      await this.loadServiziDepositoVersion();
    }

    return {
      appVersion: this.version,
      serviziDepositoVersion: this.serviziDepositoVersion,
    };
  }
}
