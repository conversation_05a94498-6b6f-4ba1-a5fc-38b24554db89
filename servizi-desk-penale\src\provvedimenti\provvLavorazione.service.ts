import { Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { DataSource, EntityManager, In, IsNull } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { CreateProvvedimentiInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti.input';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { ProvvedimentiOrigineEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-origine.enum';
import { PenaleProvvLavFileEntity } from '../consultazioni-graphql/entities/penale_provv_lav_file.entity';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { FirmaProvvLavorazioneInput } from './entities/dto/firma-provvLavorazione.input';
import * as FormData from 'form-data';
import { CreateProvvLavorazioneInput } from '../consultazioni-graphql/entities/dto/create-provvLavorazione.input';
import { AuthService } from '../auth/auth.service';
import { UfficiDBService } from '../uffici/ufficiDB.service';
import { PenaleProvvChangeStatusEntity } from '../consultazioni-graphql/entities/penale_provv_change_status.entity';
import { PenaleMPProvvEntity } from '../consultazioni-graphql/entities/penale_mp_provv.entity';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { ProvvedimentiService } from './provvedimenti.service';
import { GenerateProvvLavorazioneInput } from '../consultazioni-graphql/entities/dto/generate-provvLavorazione.input';
import { readFileSync, stat } from 'fs';
import { exportFile } from '@ns/docx-tools-js';
import { PlaceholderService } from './placeholder.service';
import { ProvvEditorLavorazioneService } from './provvEditorLavorazione.service';
import { PenaleProvvEditorEntity } from '../consultazioni-graphql/entities/penale_provv_editor.entity';
import { ArgsDepositoProvvedimentoInput } from './entities/dto/provvLavorazione.input';
import { PenaleProvvedimenti } from '../consultazioni-graphql/models/penale_provvedimenti.model';
import { CreateProvvedimentiChangeStatusInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-change-status.input';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import * as moment from 'moment';
import { CodeDepositoService } from 'src/code-deposito/code-deposito.service';
import { ServiziDepositoException } from '../exceptions/servizi-deposito.exception';
import { HttpException } from '@nestjs/common/exceptions/http.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { Utils } from '../utils/utils';
import { ServiziDepositoCredenzialiErrateException } from '../exceptions/servizi-deposito-credenziali-errate.exception';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { ProvvedimentoRelazioneService } from '../provvedimento-change-status/provvedimento-relazione.service';
import { FirmaEDepositaException } from 'src/exceptions/firma-e-deposita.exception';

@UfficioService()
export class ProvvLavorazioneService {
  private logger = new Logger(ProvvLavorazioneService.name);

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private configService: ConfigService,
    private readonly authService: AuthService,
    private placeholderService: PlaceholderService,
    private readonly provvedimentoService: ProvvedimentiService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly ufficiDBService: UfficiDBService,
    private readonly provvEditorLavorazioneService: ProvvEditorLavorazioneService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
    private readonly codeDepositoService: CodeDepositoService,
  ) {}

  provvedimenti(): Promise<PenaleProvvedimentiEntity[]> {
    this.logger.log(`Richiesta di tutti i provvedimenti`);
    const provvedimenti = this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .find();
    return provvedimenti;
  }

  provvedimentoById(
    idProvvedimenti: string,
  ): Promise<PenaleProvvedimentiEntity | null> {
    this.logger.log(`Richiesta del provvedimento. idprovv:${idProvvedimenti}`);
    const provvedimenti = this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .findOne({
        where: { idProvvedimento: idProvvedimenti },
      });
    return provvedimenti;
  }

  provvedimentoByIdUdien(
    idUdien: number,
  ): Promise<PenaleProvvedimentiEntity[] | null> {
    this.logger.log(
      `Richiesta del provvedimento per idUdienza. idUdienza:${idUdien}`,
    );
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: { idUdienza: idUdien },
    });
  }

  async createProvvedimento(
    provvedimento: CreateProvvedimentiInput,
  ): Promise<string | null> {
    this.logger.log(
      `Creazione del provvedimento per idUdienza. idUdienza:${provvedimento.idUdienza}, nrg: ${provvedimento.nrg}`,
    );
    const idAutore = await this.authService.getCurrentId();

    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(PenaleProvvedimentiEntity)
      .values({
        idUdienza: provvedimento.idUdienza,
        nrg: provvedimento.nrg,
        tipo: provvedimento.tipo,
        stato: provvedimento.stato ?? ProvvedimentiStatoEnum.IN_BOZZA,
        dataUltimaModifica: new Date(),
        idAutore: idAutore,
        origine: provvedimento.origine ?? ProvvedimentiOrigineEnum.LOCALE,
        nomeDocumento: 'ddddd',
        dataDeposito: provvedimento.dataDeposito,
        dataDecisione: new Date(),
      })
      .returning(['idProvvedimento'])
      .execute();
    const x = result.raw[0];
    return x[0];
  }
  async generaPdfDaDocxApi(idProvv: string) {
    const provvEditor = await this.connection
      .getRepository(PenaleProvvEditorEntity)
      .findOne({
        where: { idProvvedimento: idProvv },
      });
    if (provvEditor) {
      await this.generaPdfDaDocx(idProvv);
      return true;
    }
    throw new GenericErrorException(
      'Provvedimento non salvato o non oscurato',
      CodeErrorEnumException.OSCURAMENTO_MANDATORY,
    );
  }
  async generaPdfDaDocx(idProvv: string): Promise<void> {
    const fileProvvedimentoListNew = await this.getFileProvvedimentoList(
      idProvv,
      'docx',
    );
    for (const r of fileProvvedimentoListNew) {
      if (r?.content) {
        const promise = await this.generatePdf(r.content);
        await this.saveProvvedimentoInLavorazione(
          r.idProvvedimento,
          promise,
          'pdf',
          r.nomeFile.replace('.docx', '.pdf'),
          r.oscurato,
        );
      }
    }
  }
  async generatePdf(file: Buffer): Promise<Buffer> {
    const buffer = file;
    const form = new FormData();
    form.append('file', buffer, {
      filename: 'provvedimento.docx',
      contentType:
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });
    form.append('registro', Buffer.from('CASSPENALE'), {
      contentType: 'application/json',
    });

    const serviziDepositi =
      this.configService.get('app.depositiUrl') + '/provvedimenti/to-pdf';
    this.logger.debug(`call servizi depositi:${serviziDepositi}`);
    const pdf = await axios
      .post(serviziDepositi, form, {
        headers: form.getHeaders(),
        responseType: 'arraybuffer',
      })
      .then(response => {
        if (response.status === 200) {
          return response.data;
        } else {
          this.logger.error('Errore nella richiesta depositi del csp');
          throw new ServiziDepositoException('depError');
        }
      })
      .catch(err => {
        if (err.response != undefined)
          throw new ServiziDepositoException(err.response.data);
        else {
          throw new ServiziDepositoException('noServiceError');
        }
      });
    return pdf;
  }

  async deleteProvvLavorazione(idProvv: string) {
    const cf = await this.authService.getCurrentUser();

    this.logger.log(
      `Cancellazione del provvedimento in lavorazione con per idUdienza. idProvv:${idProvv}`,
    );
    try {
      return await this.connection.manager.transaction(
        async transactionalEntityManager => {
          this.logger.log(
            `Cancellazione del provvedimento in inizio transazione. idProvv:${idProvv}`,
          );

          // Prima trovo il provvedimento da eliminare
          const provv = await this.connection
            .getRepository(PenaleProvvedimentiEntity)
            .findOne({
              where: {
                idProvvedimento: idProvv,
                stato: In([
                  ProvvedimentiStatoEnum.IN_BOZZA,
                  ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
                  ProvvedimentiStatoEnum.BOZZA_PRESIDENTE,
                ]),
              },
            });

          if (provv?.idProvvedimento) {
            // Prendo l'id del provvedimento originale da MP_PROVV
            const mpProvv = await this.connection
              .getRepository(PenaleMPProvvEntity)
              .findOne({
                where: {
                  idProvvedimentoDestinazione: provv.idProvvedimento,
                },
              });
            let statoProvvedimento = null;
            if (mpProvv?.idProvvedimentoOrigine) {
              // Prendo lo stato del provvedimento originale
              const originalProvv = await this.connection
                .getRepository(PenaleProvvedimentiEntity)
                .findOne({
                  where: {
                    idProvvedimento: mpProvv.idProvvedimentoOrigine,
                  },
                });

              if (originalProvv?.stato) {
                statoProvvedimento = originalProvv.stato;
              }
            }
            // Aggiorno lo stato del ricorso udienza con lo stato del provvedimento originale
            await this.connection.query(
              `UPDATE penale_t_ricudien 
                             SET STATO_PROVVEDIMENTO = :statoProvvedimento
                             WHERE NRG = :nrg AND ID_UDIEN = :idUdienza`,
              [statoProvvedimento, provv.nrg, provv.idUdienza],
            );

            await this.codeDepositoService.deleteCodeDeposito({
              idProvv: provv.idProvvedimento,
              cf,
            });
            await this.connection
              .getRepository(PenaleProvvLavFileEntity)
              .delete({
                idProvvedimento: provv.idProvvedimento,
              });
            await this.connection
              .getRepository(PenaleProvvEditorEntity)
              .delete({ idProvvedimento: provv.idProvvedimento });
            await this.connection
              .getRepository(PenaleProvvChangeStatusEntity)
              .delete({
                idProvvedimento: provv.idProvvedimento,
              });
            await this.connection.getRepository(PenaleMPProvvEntity).delete({
              idProvvedimentoDestinazione: provv.idProvvedimento,
            });
            await this.connection
              .getRepository(PenaleProvvedimentiEntity)
              .delete({ idProvvedimento: provv.idProvvedimento });
            this.logger.log(
              `Fine cancellazione del provvedimento in inizio transazione. idProvv:${idProvv}`,
            );
            return provv;
          } else {
            this.logger.debug(
              `Nessuna cancellazione avvenuta provvedimento non trovato . idProvv:${idProvv}`,
            );
            return null;
          }
        },
      );
    } catch (e) {
      this.logger.error(
        `Non è possibile eliminare il provvedimento. idProvv:${idProvv}`,
        e,
      );
      throw new InternalServerErrorException(
        'Non è possibile eliminare il provvedimento: ' + e,
      );
    }
  }

  async deleteFileOscurato(idProvLav: string) {
    const deleteResult = await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .delete({
        idCategoria: idProvLav,
        oscurato: true,
      });
    this.logger.log(`File Oscurato cancellato. idProvLav:${idProvLav}`);
    return deleteResult;
  }

  async deleteFile(idProvLav: string) {
    const deleteResult = await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .delete({
        idCategoria: idProvLav,
      });
    this.logger.log(`File in lavorazione cancellato. idProvLav:${idProvLav}`);
    return deleteResult;
  }

  async getProvvLavorazione(nrg: number, cfGiudice?: number) {
    this.logger.log(`Richiesta del provvedimento. nrg:${nrg}`);
    return await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .findOne({
        where: {
          nrg: nrg,
          idAutore: cfGiudice,
          fkIdCat: IsNull(),
        },
      });
  }

  async getProvvLavorazioneByIdProvv(idProvv: string, cfGiudice?: number) {
    this.logger.log(`Richiesta del provvedimento. idProvv:${idProvv}`);
    return await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
          idAutore: cfGiudice,
        },
      });
  }

  async getProvvLavorazioneByIdCategoria(idCat: string) {
    this.logger.log(
      `Richiesta del provvedimento in lavorazione. idCategoria:${idCat}`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idCategoria: idCat,
        },
      });
  }

  async getProvvLavorazioneByIdProvvedimento(idProvv: string) {
    this.logger.log(`Richiesta del provvedimento. idProvv:${idProvv}`);
    return await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
        },
      });
  }

  async getProvvLavorazioneByIdProvvAndXml(idProvv: string) {
    this.logger.log(
      `Richiesta del file XML in lavorazione del provvedimento. idProvv:${idProvv}`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
          tipoFile: 'xml',
        },
      });
  }

  async getProvvLavorazioneByIdCat(idCategoria: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idCategoria:${idCategoria}`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idCategoria: idCategoria,
        },
      });
  }
  async getProvvLavorazioneByIdCatUnlessDocx(idCategoria: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idCategoria:${idCategoria}`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idCategoria: idCategoria,
          mimeType: In(['application/pdf', 'application/xml']),
        },
      });
  }

  async getFileProvvedimento(idProvv: string, tipoFile: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idProvv:${idProvv}; tipoFile: ${tipoFile}`,
    );
    return await this.connection.getRepository(PenaleProvvLavFileEntity).find({
      where: {
        idProvvedimento: idProvv,
        tipoFile: tipoFile,
      },
    });
  }

  async getFileProvvedimentoAllByIdProvv(idProvv: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idProvv:${idProvv}`,
    );
    return await this.connection.getRepository(PenaleProvvLavFileEntity).find({
      where: {
        idProvvedimento: idProvv,
      },
    });
  }

  async getFileProvvedimentoByOne(idProvv: string, tipoFile: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idProvv:${idProvv}, tipoFile:${tipoFile}, oscurato: false`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
          tipoFile: tipoFile,
          oscurato: false,
        },
      });
  }

  async getFileProvvedimentoPDFNonOscurato(idProvv: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idProvv:${idProvv}, tipoFile: pdf, oscurato: true`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
          tipoFile: 'pdf',
          oscurato: false,
        },
      });
  }

  async getFileProvvedimentoPDFOscurato(idProvv: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idProvv:${idProvv}, tipoFile: pdf, oscurato: true`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
          tipoFile: 'pdf',
          oscurato: true,
        },
      });
  }

  async getFileProvvedimentoList(idProvv: string, tipoFile: string) {
    this.logger.log(
      `Richiesta dei file in lavorazione del provvedimento. idProvv:${idProvv}, tipoFile:${tipoFile}`,
    );
    return await this.connection.getRepository(PenaleProvvLavFileEntity).find({
      where: {
        idProvvedimento: idProvv,
        tipoFile: tipoFile,
      },
    });
  }

  async getAllFileProvvedimento(
    idProvv: string,
  ): Promise<PenaleProvvLavFileEntity[]> {
    this.logger.log(
      `Richiesta di tutti file in lavorazione del provvedimento. idProvv:${idProvv}`,
    );
    return await this.connection.getRepository(PenaleProvvLavFileEntity).find({
      where: {
        idProvvedimento: idProvv,
      },
    });
  }

  async getFilesProvvedimentoDaFirmare(
    idProvv: string,
  ): Promise<PenaleProvvLavFileEntity[]> {
    this.logger.log(
      `Richiesta di tutti file in lavorazione del provvedimento. idProvv:${idProvv}`,
    );
    return await this.connection.getRepository(PenaleProvvLavFileEntity).find({
      where: {
        idProvvedimento: idProvv,
        mimeType: In(['application/pdf', 'application/xml']),
      },
    });
  }

  async setIdCat(idCat: string, provv: PenaleProvvedimentiEntity) {
    if (idCat) {
      provv.fkIdCat = Number(idCat);
    }
    this.logger.log(
      `Salvataggio dei file in lavorazione del provvedimento. idProvv:${provv?.idProvvedimento}, nrg:${provv?.nrg}, idCategoria: ${idCat}`,
    );
    await this.connection.getRepository(PenaleProvvedimentiEntity).save(provv);
    return provv;
  }

  async createService(
    provvLavorazione: CreateProvvLavorazioneInput,
    idAutore: number,
  ) {
    this.logger.log(
      `Salvataggio del  provvedimento. provv:${JSON.stringify(
        provvLavorazione,
      )}`,
    );
    let entityProvv = this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .create();
    entityProvv.nrg = provvLavorazione.nrg;
    entityProvv.tipo =
      provvLavorazione.argsProvvedimento['tipologiaProvvedimento'];

    entityProvv.idAutore = idAutore;
    entityProvv.stato = ProvvedimentiStatoEnum.IN_BOZZA;
    entityProvv.nomeDocumento = provvLavorazione.nrg + '.pdf';
    entityProvv.idUdienza = provvLavorazione.idUdienza;
    entityProvv.dataUltimaModifica = new Date();
    entityProvv.dataDecisione = new Date();
    entityProvv.origine = provvLavorazione.origine;
    this.logger.log(
      `Salvataggio del  provvedimento. provvedimento :${JSON.stringify(
        entityProvv,
      )}, `,
    );
    entityProvv = await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .save(entityProvv);

    return entityProvv;
  }

  async saveFileProvvPdf(
    filesUpload: Array<Express.Multer.File>,
    provv: PenaleProvvedimenti,
  ) {
    const entityFiles = [];
    this.logger.log(
      `Salvataggio dei file in lavorazione del provvedimento. provvedimento :${JSON.stringify(
        provv,
      )}, `,
    );
    for (const file of filesUpload) {
      try {
        let entityFile = this.connection
          .getRepository(PenaleProvvLavFileEntity)
          .create();
        entityFile.idProvvedimento = provv.idProvvedimento;
        entityFile.tipoFile = 'pdf';
        entityFile.mimeType = 'application/pdf';
        entityFile.nomeFile = file.originalname;
        entityFile.content = file.buffer;
        entityFile.signed = false;
        entityFile = await this.connection
          .getRepository(PenaleProvvLavFileEntity)
          .save(entityFile);
        entityFiles.push(entityFile);

        await this.provvedimentoService.updateProvvedimento(
          provv.idProvvedimento,
          file.originalname,
        );
      } catch (e) {
        this.logger.error(
          `Errore nel salvataggio dei file di lavorazione del provvedimento. idProvv: ${provv?.idProvvedimento}, nrg: ${provv?.nrg} `,
          e,
        );
        throw new InternalServerErrorException(
          'Errore nel salvataggio dei file di lavorazione del provvedimento.',
        );
      }
    }
    return entityFiles;
  }

  async saveFileDatiAtto(
    datiAttoBuffer: Buffer,
    idProvvLav: string,
    entityManager?: EntityManager,
  ) {
    this.logger.log(
      `Salvataggio del datoAtto.xml per il provvedimento. idProvvLav:${idProvvLav}`,
    );
    const entityFile = this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .create();
    entityFile.idProvvedimento = idProvvLav;
    entityFile.tipoFile = 'xml';
    entityFile.mimeType = 'application/xml';
    entityFile.nomeFile = 'DatiAtto.xml';
    entityFile.content = datiAttoBuffer;
    entityFile.signed = false;
    entityFile.createAt = new Date();
    if (entityManager) {
      return entityManager
        .getRepository(PenaleProvvLavFileEntity)
        .save(entityFile);
    }
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .save(entityFile);
  }

  async firmaEDeposita(
    depProvvLavorazioneInput: FirmaProvvLavorazioneInput,
    filesPdf: PenaleProvvLavFileEntity[],
    fileXml: PenaleProvvLavFileEntity,
  ) {
    this.logger.log(
      `Firma e deposito del presidente creazione del formaData del datoAtto.xml per il provvedimento. idProvvLav:${depProvvLavorazioneInput?.idProvvedimento}, nrg:${depProvvLavorazioneInput?.nrg}, 
      tipologiaProvvedimento:${depProvvLavorazioneInput?.tipologiaProvvedimento}, firmatoInput: ${depProvvLavorazioneInput.firmato}`,
    );
    try {
      const form = new FormData();
      for (const filesPdf1 of filesPdf) {
        this.logger.log(
          `Sto inserendo il pdf nel formDat. idCategoria:${filesPdf1?.idCategoria}, nomeFile:${filesPdf1?.nomeFile}, 
      idProvvedimento:${filesPdf1?.idProvvedimento},  oscurato:${filesPdf1?.oscurato}, signed:${filesPdf1?.signed}`,
        );
        form.append(
          filesPdf1.oscurato ? 'fileOscurato' : 'file',
          filesPdf1.content,
          {
            filename: filesPdf1.nomeFile,
            contentType: filesPdf1.mimeType,
          },
        );
      }

      form.append('fileXml', fileXml.content, {
        filename: fileXml.nomeFile,
        contentType: fileXml.mimeType,
      });
      const ufficioDB = await this.ufficiDBService.getFirstUfficio();
      if (!ufficioDB?.codiceUfficio) {
        this.logger.warn('Ufficio non trovato');
        throw new InternalServerErrorException('ufficio non trovato');
      }
      depProvvLavorazioneInput.bustaMakerData.codiceUfficioDestinatario =
        ufficioDB?.codiceUfficio;
      depProvvLavorazioneInput.generazioneDatiAtto.allegatoOscurato =
        filesPdf.length > 1;
      return await this.depositaProvvedimentoCurrent(
        depProvvLavorazioneInput,
        form,
      );
    } catch (e) {
      this.logger.error(
        `Errore nella Firma e deposito massivo. idProvvLav:${depProvvLavorazioneInput?.idProvvedimento}, nrg:${depProvvLavorazioneInput?.nrg}`,
        e,
      );
      throw e;
    }
  }

  private getSignatureExceptionCode = (error: any) => {
    const message = error?.message;
    if (!message || !message.includes('SignatureException')) return null;
    const ccodice = message.match(/Codice\:\s+[0-9]+/);
    if (!ccodice) return null;
    return ccodice[0].split(':')[1].trim();
  };

  private getSignatureExceptionName = (error: any) => {
    switch (this.getSignatureExceptionCode(error)) {
      case '191':
      case '192':
      case '193':
      case '194':
      case '195':
        return CodeErrorEnumException.INVALID_JWT_TOKEN_SIGN_ERROR;
      case '030':
        return CodeErrorEnumException.USER_NOT_PRESENT_SIGN_ERROR;
      case '010':
        return CodeErrorEnumException.INCORRECT_PASSWORD_SIGN_ERROR;
      case '019':
        return CodeErrorEnumException.INCORRECT_PIN_SIGN_ERROR;
      case '027':
        return CodeErrorEnumException.USER_BLOCKED_SIGN_ERROR;
      case '134':
        return CodeErrorEnumException.PDF_CORRUPTED_SIGN_ERROR;
      default:
        return null;
    }
  };

  async depositaProvvedimentoCurrent(
    datiDeposito: FirmaProvvLavorazioneInput,
    form: FormData,
  ): Promise<string> {
    this.logger.log(
      `Inizio del deposito. nrg:${datiDeposito.nrg},
      idProvvedimento:${datiDeposito.idProvvedimento},
      allegatoOscurato:${
        datiDeposito.generazioneDatiAtto.allegatoOscurato
      }, getLengthSync:${form.getLengthSync()}`,
    );
    form.append(
      'depositoProvvedimento',
      Buffer.from(JSON.stringify(datiDeposito)),
      {
        contentType: 'application/json',
      },
    );
    form.append('registro', Buffer.from('CASSPENALE'), {
      contentType: 'application/json',
    });

    const depositiUrl =
      this.configService.get('app.depositiUrl') +
      '/provvedimenti/firma-deposita-provvedimento';
    this.logger.debug(`call servizi depositi:${depositiUrl}`);
    // bustaMakerData contains CF and ufficio values from frontend
    const { codiceFiscaleMittente, codiceUfficioDestinatario } = datiDeposito.bustaMakerData;

    if (!codiceFiscaleMittente || !codiceUfficioDestinatario) {
      throw new ServiziDepositoException('CF o codice ufficio mancanti');
    }

    return axios
      .post<string>(depositiUrl, form, {
        headers: {
          ...form.getHeaders(),
          'x-wasp-user': codiceFiscaleMittente as string,
          'x-codice-ufficio': codiceUfficioDestinatario as string
        },
        responseType: 'text',
      })
      .then(response => {
        if (response.status === 200) {
          this.logger.log(
            `Deposito del provvedimento success: idProvvedimento:${datiDeposito?.idProvvedimento}, nrg:${datiDeposito?.nrg}`,
          );
          // @ts-ignore
          const errors = response?.data?.errors || new Array<any>();
          if (errors?.length > 0) {
            this.logger.error(
              `Errore nel deposito del provvedimento su servizi depositi. idProvvedimento:${datiDeposito?.idProvvedimento}, nrg:${datiDeposito?.nrg}, ${response.status}: ${response.statusText}, errors: ${errors}`,
            );
            throw new ServiziDepositoException(errors[0]);
          }
          if (response.data && response.data != '') {
            return response.data;
          }
          this.logger.error(
            `Errore nel deposito del provvedimento su servizi depositi con idCat null. idProvvedimento:${datiDeposito?.idProvvedimento}, nrg:${datiDeposito?.nrg}, ${response.status}: ${response.statusText}, errors: ${errors}`,
          );
          throw new ServiziDepositoException(
            'ID_DEPOSITO_NULL',
            undefined,
            CodeErrorEnumException.ID_DEPOSITO_NULL,
          );
        } else {
          this.logger.error(
            `Errore nel deposito del provvedimento. idProvvedimento:${datiDeposito?.idProvvedimento}, nrg:${datiDeposito?.nrg}, ${response.status}: ${response.statusText}`,
          );
          throw new ServiziDepositoException('depError');
        }
      })
      .catch(err => {
        // se arriva 134 come codice di errore ital_agile significa che il pdf è corrotto
        const signErrorName = this.getSignatureExceptionName(err);
        this.logger.error(
          `Errore nel deposita provvedimenti. idProvvedimento:${datiDeposito?.idProvvedimento}, nrg:${datiDeposito?.nrg}`,
          err,
        );
        if (signErrorName)
          throw new ServiziDepositoException(
            err?.message,
            undefined,
            signErrorName,
          );
        if (err?.message?.includes('500')) {
          throw new ServiziDepositoCredenzialiErrateException(
            'DEPOSITO_INTERNAL_ERROR',
          );
        }
        if (err instanceof HttpException) {
          throw err;
        }
        if (err.response != undefined)
          throw new ServiziDepositoException(err.response.data);
        else {
          throw new ServiziDepositoException('SERVIZI_DEPOSITO_ERROR');
        }
      });
  }

  async depositaProvvedimento(
    form: FormData,
    codiceFiscale: string,
    firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput,
  ) {
    this.logger.log(
      `Deposita provvedimento firmato. idProvvedimento: ${firmaProvvLavorazioneInput.idProvvedimento}, nrg:${firmaProvvLavorazioneInput.nrg}, allegatoOscurato:${firmaProvvLavorazioneInput?.generazioneDatiAtto?.allegatoOscurato}`,
    );
    const ufficioDB = await this.ufficiDBService.getFirstUfficio();
    if (!ufficioDB?.codiceUfficio) {
      this.logger.warn('Ufficio non trovato');
      throw new InternalServerErrorException('ufficio non trovato');
    }
    form.append(
      'infoBusta',
      Buffer.from(
        JSON.stringify({
          codiceFiscaleMittente: codiceFiscale,
          codiceUfficioDestinatario: ufficioDB?.codiceUfficio,
          ruoloMittente: 'MAG',
          idMsg: firmaProvvLavorazioneInput.nrg,
        }),
      ),
      { contentType: 'application/json' },
    );
    let endpoint;

    if (firmaProvvLavorazioneInput.firmato) {
      endpoint = '/provvedimenti/deposita-provvedimento';
    } else {
      form.append(
        'infoProvvedimento',
        Buffer.from(
          JSON.stringify({
            ...firmaProvvLavorazioneInput.generazioneDatiAtto,
          }),
        ),

        { contentType: 'application/json' },
      );
      form.append(
        'user',
        firmaProvvLavorazioneInput.credenzialiFirmaRemota.usernameFirma,
      );
      form.append(
        'password',
        firmaProvvLavorazioneInput.credenzialiFirmaRemota.passwordFirma,
      );
      form.append(
        'pin',
        firmaProvvLavorazioneInput.credenzialiFirmaRemota.pinFirma,
      );
      form.append('registro', Buffer.from('CASSPENALE'), {
        contentType: 'application/json',
      });
      endpoint = '/provvedimenti/firma-deposita-provvedimento';
    }
    this.logger.log(`Deposito provvedimento chiamo l'url: ${endpoint}`);
    const urlservizidepositi =
      this.configService.get('app.depositiUrl') + endpoint;
    this.logger.debug(`call servizi depositi:${urlservizidepositi}`);
    return axios
      .post(urlservizidepositi, form, {
        responseType: 'text',
      })
      .then(response => {
        if (response.status === 200) {
          this.logger.log(
            `Provvedimento firmato depositato. idProvvedimento: ${firmaProvvLavorazioneInput.idProvvedimento},
             nrg:${firmaProvvLavorazioneInput.nrg}`,
          );
          this.logger.log(
            `Deposito del provvedimento success: idProvvedimento:${firmaProvvLavorazioneInput?.idProvvedimento}, nrg:${firmaProvvLavorazioneInput?.nrg}`,
          );
          //@ts-ignore
          const errors = response?.errors || new Array<any>();
          if (errors?.length > 0) {
            this.logger.error(
              `Errore nel deposito del provvedimento su servizi depositi. idProvvedimento:${firmaProvvLavorazioneInput?.idProvvedimento}, nrg:${firmaProvvLavorazioneInput?.nrg}, ${response.status}: ${response.statusText}, errors: ${errors}`,
            );
            throw new ServiziDepositoException('DEPOSITO_ERROR');
          }
          if (response.data && response.data != '') {
            return response.data;
          }
          this.logger.error(
            `Errore nel deposito del provvedimento su servizi depositi con idCat null. idProvvedimento:${firmaProvvLavorazioneInput?.idProvvedimento}, nrg:${firmaProvvLavorazioneInput?.nrg}, ${response.status}: ${response.statusText}, errors: ${errors}`,
          );
          throw new ServiziDepositoException(
            'ID_DEPOSITO_NULL',
            undefined,
            CodeErrorEnumException.ID_DEPOSITO_NULL,
          );
        } else {
          this.logger
            .error(`Errore nel depositare il provvedimento firmato. idProvvedimento: ${firmaProvvLavorazioneInput.idProvvedimento},
             nrg:${firmaProvvLavorazioneInput.nrg}, ${response.status}: ${response.statusText}`);
          throw new ServiziDepositoException('depError');
        }
      })
      .catch(err => {
        this.logger.error(
          `Errore nel depositare il provvedimento firmato. idProvvedimento: ${firmaProvvLavorazioneInput.idProvvedimento},
             nrg:${firmaProvvLavorazioneInput.nrg}`,
          err,
        );
        if (err.response != undefined)
          throw new ServiziDepositoException(err.response.data);
        else {
          throw new ServiziDepositoException('noServiceError');
        }
      });
  }

  async saveContetAndFileProvvDocx(
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
    idProvvLav: string,
    fileProvvedimentoListPDF: PenaleProvvLavFileEntity[],
  ) {
    this.logger.log(
      `Inizio salvataggio dei file docx e del contenuto, idProvvLav: ${idProvvLav}, fileLength: ${fileProvvedimentoListPDF?.length}`,
    );
    try {
      const propsToClean = [
        'text',
        'introduzione',
        'motivoRicorso',
        'finaleDeposito',
        'pqm',
        'textOscurato',
      ];
      this.cleanNewlines(
        generateProvvLavorazioneInput.argsProvvedimento as {},
        propsToClean,
      );
      if (
        //this.checkFieldMandatory(generateProvvLavorazioneInput) &&
        this.checkTestoOscurato(generateProvvLavorazioneInput)
      ) {
        let date = generateProvvLavorazioneInput?.argsProvvedimento
          ?.dataDecisione
          ? moment(
              generateProvvLavorazioneInput.argsProvvedimento.dataDecisione,
            ).toDate()
          : new Date();
        date = moment(date).add(2, 'h').toDate();
        await this.provvedimentoService.updateDataDecisioneProvvedimento(
          idProvvLav,
          date,
        );
        const provvedimentoEditorByIdProvv =
          await this.provvEditorLavorazioneService.getProvvedimentoEditorByIdProvv(
            idProvvLav,
          );
        const { bufferNonOscurato, bufferOscurato } =
          await this.createOrUpdateProvvedimentoFile(
            provvedimentoEditorByIdProvv,
            generateProvvLavorazioneInput,
            idProvvLav,
          );

        let entityFile = null;
        if (fileProvvedimentoListPDF && fileProvvedimentoListPDF.length > 0) {
          await this.aggiornamentoDeiFileGiaEsistenti(
            generateProvvLavorazioneInput,
            fileProvvedimentoListPDF,
            bufferOscurato,
            bufferNonOscurato,
            idProvvLav,
          );
          await this.provvedimentoService.updateDataUltimaModificaProvvedimento(
            idProvvLav,
          );
        } else {
          entityFile = await this.saveProvvedimentoInLavorazione(
            idProvvLav,
            bufferNonOscurato,
            'docx',
            `Provvedimento_${generateProvvLavorazioneInput.argsProvvedimento.numRuolo}_${generateProvvLavorazioneInput.argsProvvedimento.anRuolo}.docx`,
            false,
          );
          if (
            generateProvvLavorazioneInput.allegatoOscurato ||
            generateProvvLavorazioneInput.argsProvvedimento.generaOscurato
          ) {
            await this.saveFileOscurato(
              bufferOscurato,
              idProvvLav,
              generateProvvLavorazioneInput,
            );
          }
          await this.provvedimentoService.updateProvvedimento(
            idProvvLav,
            entityFile.nomeFile,
          );
        }
        await this.updateDatiAtto(idProvvLav, generateProvvLavorazioneInput);
        this.logger.log(
          `Fine salvataggio dei file docx e del contenuto, idProvvLav: ${idProvvLav}, fileLength: ${fileProvvedimentoListPDF?.length}`,
        );
        return entityFile;
      }
    } catch (e) {
      this.logger.error(
        `Errore nel salvataggio dei file docx e del contenuto, idProvvLav: ${idProvvLav}, fileLength: ${fileProvvedimentoListPDF?.length}`,
        e,
      );
      throw new InternalServerErrorException(e);
    }
    this.logger.error(
      `Errore nel salvataggio dei file docx e del contenuto dati non valorizzati, idProvvLav: ${idProvvLav}, fileLength: ${fileProvvedimentoListPDF?.length}`,
    );
    throw new InternalServerErrorException('Valorizza tutti i campi');
  }

  private async createOrUpdateProvvedimentoFile(
    provvedimentoEditorByIdProvv: PenaleProvvEditorEntity | null,
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
    idProvvLav: string,
  ) {
    if (!provvedimentoEditorByIdProvv) {
      await this.provvEditorLavorazioneService.createProvvedimentoEditor(
        generateProvvLavorazioneInput.argsProvvedimento,
        generateProvvLavorazioneInput?.strutturato || false,
        idProvvLav,
      );
    } else {
      await this.provvEditorLavorazioneService.updateProvvedimentoEditor(
        generateProvvLavorazioneInput.argsProvvedimento,
        generateProvvLavorazioneInput.strutturato || false,
        provvedimentoEditorByIdProvv.idProvvedimentoEditor,
      );
    }
    const [bufferNonOscurato, bufferOscurato] = await this.generaDocxUseLibrary(
      generateProvvLavorazioneInput,
      idProvvLav,
    );
    return { bufferNonOscurato, bufferOscurato };
  }

  async cleanNewlines(object: { [key: string]: string }, properties: string[]) {
    if (properties) {
      properties.forEach(property => {
        if (object.hasOwnProperty(property)) {
          if (property === 'textOscurato') {
            // Preserva meglio la struttura per il testo oscurato
            object[property] = object[property]
              ?.replace(/\n\s*\n/g, '\n\n') // Preserva i doppi a capo
              ?.split('<div style="text-align: center;">')
              .join('\n<div style="text-align: center;">')
              ?.split('</strong></div>')
              .join('</strong></div>\n')
              ?.split('<p>')
              .join('\n<p>')
              ?.split('</p>')
              .join('</p>\n')
              ?.replace(/\n{3,}/g, '\n\n'); // Limita i multipli a capo
          } else {
            object[property] = object[property]?.split('\n').join('');
          }
        }
      });
      return object;
    }
    return null;
  }

  async updateProvvedimentoLav(idCat: string, update: any) {
    this.logger.log(
      `Aggiornamento dei file in lavorazione. idCategoria:${idCat}`,
    );
    return await this.connection
      .createQueryBuilder()
      .update(PenaleProvvLavFileEntity)
      .set({ ...update })
      .where('idCategoria= :idCategoria', {
        idCategoria: idCat,
      })
      .execute();
  }

  async saveProvvedimentoInLavorazione(
    idProvvLav: string,
    buffer: Buffer,
    tipoFile: string,
    nomeFile: string,
    oscurato: boolean,
  ) {
    /* The code attempts to find a single entry in the database where
     * the idProvvedimento, tipoFile, oscurato match the given parameters respectively.
     *
     **/
    const fileEsiste = await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .findOne({
        where: {
          idProvvedimento: idProvvLav,
          tipoFile: tipoFile,
          oscurato: oscurato,
        },
      });
    if (fileEsiste) {
      this.logger.log(
        `Aggiornament dei file in lavorazione. idProvvLav:${idProvvLav}, tipoFile:${tipoFile}, nomeFile:${nomeFile}, oscurato:${oscurato}`,
      );
      fileEsiste.idProvvedimento = idProvvLav;
      fileEsiste.tipoFile = tipoFile;
      fileEsiste.mimeType =
        tipoFile === 'docx'
          ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          : 'application/pdf';
      fileEsiste.nomeFile = nomeFile;
      fileEsiste.content = Buffer.from(buffer);
      fileEsiste.signed = false;
      fileEsiste.oscurato = oscurato;
      return await this.connection
        .getRepository(PenaleProvvLavFileEntity)
        .save(fileEsiste);
    }
    this.logger.log(
      `Salvataggio dei file in lavorazione. idProvvLav:${idProvvLav}, tipoFile:${tipoFile}, nomeFile:${nomeFile}, oscurato:${oscurato}`,
    );
    const entityFile = new PenaleProvvLavFileEntity();
    entityFile.idProvvedimento = idProvvLav;
    entityFile.tipoFile = tipoFile;
    entityFile.mimeType =
      tipoFile === 'docx'
        ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        : 'application/pdf';
    entityFile.nomeFile = nomeFile;
    entityFile.content = Buffer.from(buffer);
    entityFile.signed = false;
    entityFile.oscurato = oscurato;
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .save(entityFile);
  }

  async countPdfFile(idProvvedimento: string) {
    const countPdf = await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .count({
        where: {
          idProvvedimento: idProvvedimento,
          tipoFile: 'pdf',
        },
      });
    this.logger.log(
      `Conteggio  dei file pdf in lavorazione. idProvvedimento:${idProvvedimento}, countPdf:${countPdf}`,
    );
    return countPdf;
  }

  async getListaPDF(idProvvedimento: string) {
    this.logger.log(
      `Lista dei file pdf in lavorazione. idProvvedimento:${idProvvedimento}`,
    );
    return await this.connection.getRepository(PenaleProvvLavFileEntity).find({
      where: {
        idProvvedimento: idProvvedimento,
        tipoFile: 'pdf',
      },
    });
  }

  async saveCopyProvvedimentiLavorazione(
    provvLav: PenaleProvvLavFileEntity,
    entityManager?: EntityManager,
  ): Promise<PenaleProvvLavFileEntity | null> {
    this.logger.log(
      `Salvataggio copia  dei file pdf in lavorazione. idProvvedimento:${provvLav?.idProvvedimento}, 
      nomeFile:${provvLav?.nomeFile},signed:${provvLav?.signed},oscurato:${provvLav?.oscurato}`,
    );
    if (entityManager) {
      return await entityManager
        .getRepository(PenaleProvvLavFileEntity)
        .save(provvLav);
    }
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .save(provvLav);
  }

  async duplicateEditor(idProvv: string, idProvvToCopy: string) {
    let provvedimentoEditorByIdProvv: PenaleProvvEditorEntity | null = null;
    let idProvvForCloneEditor = idProvv;
    this.logger.log(
      `Duplicazione del contenuto dell'editor. idProvvedimento:${idProvv}, 
      idProvvedimentoDaCopiare:${idProvvToCopy}`,
    );
    try {
      provvedimentoEditorByIdProvv =
        await this.provvEditorLavorazioneService.getProvvedimentoEditorByIdProvv(
          idProvv,
        );
      do {
        if (idProvvForCloneEditor && provvedimentoEditorByIdProvv == null) {
          const provvCollegato =
            await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
              idProvvForCloneEditor,
            );
          if (provvCollegato?.idProvvedimentoOrigine) {
            idProvvForCloneEditor = provvCollegato?.idProvvedimentoOrigine;
            provvedimentoEditorByIdProvv =
              await this.provvEditorLavorazioneService.getProvvedimentoEditorByIdProvv(
                idProvvForCloneEditor,
              );
          } else {
            this.logger.error('Editor non duplicato perchè non trovato');
            throw new GenericErrorException(
              "Errore nella duplicazione del contenuto dell'editor.",
              CodeErrorEnumException.PROVV_NOT_DUPLICATED,
            );
          }
        }
      } while (!provvedimentoEditorByIdProvv);
    } catch (e) {
      this.logger.error(
        `Errore nella duplicazione del contenuto dell'editor. idProvvedimento:${idProvvForCloneEditor}, 
      idProvvedimentoDaCopiare:${idProvvToCopy}`,
        e,
      );
      throw new GenericErrorException(
        "Errore nella duplicazione del contenuto dell'editor.",
        CodeErrorEnumException.PROVV_NOT_DUPLICATED,
      );
    }
    if (provvedimentoEditorByIdProvv) {
      const cloneprovvedimentoEditorByIdProvv = (({
        idProvvedimentoEditor,
        ...o
      }) => o)(provvedimentoEditorByIdProvv as PenaleProvvEditorEntity);
      cloneprovvedimentoEditorByIdProvv.idProvvedimento = idProvvToCopy;
      return await this.provvEditorLavorazioneService.saveProvvEditor(
        cloneprovvedimentoEditorByIdProvv as PenaleProvvEditorEntity,
      );
    }
    this.logger.error(
      `Editor non duplicato perchè non trovato. idProvv:${idProvvForCloneEditor}`,
    );
    throw new GenericErrorException(
      "Errore nella duplicazione del contenuto dell'editor.",
      CodeErrorEnumException.PROVV_NOT_DUPLICATED,
    );
  }

  /**
   * Cancella i dati pdf di un provvedimento che è stato eliminato
   * @param idProvv
   */
  async deleteOldFilePDF(idProvv: string) {
    this.logger.log(
      `Cancellazione file pdf in lavorazione. idProvvedimento:${idProvv}`,
    );
    const allFileProvvedimento = await this.getAllFileProvvedimento(idProvv);
    for (const penaleProvvLavFileEntity of allFileProvvedimento) {
      if (penaleProvvLavFileEntity.tipoFile == 'pdf') {
        await this.deleteFileByIdCat(penaleProvvLavFileEntity.idCategoria);
      }
    }
  }

  async deleteFileByIdCat(idCategoria: string) {
    this.logger.log(
      `Cancellazione provvedimenti in lavorazione. idCategoria:${idCategoria}`,
    );
    return await this.connection
      .getRepository(PenaleProvvLavFileEntity)
      .delete({ idCategoria: idCategoria });
  }

  private async updateDatiAtto(
    idProvv: string,
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ) {
    this.logger.log(`Aggiornamento del dati atto. idProvv:${idProvv}`);
    const createProvvLavorazioneInput = new CreateProvvLavorazioneInput();
    createProvvLavorazioneInput.nrg = generateProvvLavorazioneInput.nrg;
    createProvvLavorazioneInput.idUdienza =
      generateProvvLavorazioneInput.idUdienza;
    createProvvLavorazioneInput.allegatoOscurato =
      generateProvvLavorazioneInput.allegatoOscurato ||
      generateProvvLavorazioneInput.argsProvvedimento.generaOscurato;
    createProvvLavorazioneInput.argsProvvedimento =
      new ArgsDepositoProvvedimentoInput();
    createProvvLavorazioneInput.argsProvvedimento.anRuolo =
      generateProvvLavorazioneInput.argsProvvedimento.anRuolo;
    createProvvLavorazioneInput.argsProvvedimento.numRuolo =
      generateProvvLavorazioneInput.argsProvvedimento.numRuolo;
    createProvvLavorazioneInput.argsProvvedimento.tipologiaProvvedimento =
      generateProvvLavorazioneInput.argsProvvedimento.tipologiaProvvedimento;
    const bufferDatiAtto = await this.provvedimentoService.generaDatiAtto(
      createProvvLavorazioneInput,
    );
    const provvLavorazioneByIdProvvAndXml =
      await this.getProvvLavorazioneByIdProvvAndXml(idProvv);
    if (bufferDatiAtto && provvLavorazioneByIdProvvAndXml?.idCategoria) {
      await this.updateProvvedimentoLav(
        provvLavorazioneByIdProvvAndXml.idCategoria,
        {
          content: bufferDatiAtto,
        },
      );
    } else {
      this.logger.error(
        `Errore nell'aggiornamento del dati atto. idProvv:${idProvv}`,
      );
      throw new InternalServerErrorException(
        'Errore nella creazione del dato atto.',
      );
    }
  }

  /**
   * Restituisce i buffer docx generati dalla libreria.
   * @param generateProvvLavorazioneInput
   * @param idProvvLav
   * @private
   */
  private async generaDocxUseLibrary(
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
    idProvvLav: string,
  ): Promise<[Buffer, Buffer | null]> {
    this.logger.log(
      `Creazione del docx usando la libreria ns. idProvv:${idProvvLav}`,
    );
    const ricUdien = await this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: {
          nrg: generateProvvLavorazioneInput.nrg,
          idUdienza: generateProvvLavorazioneInput.idUdienza,
        },
      });
    if (!ricUdien?.idRicudien) {
      this.logger.error('Errore nella creazione del provvedimento');
      throw new GenericErrorException(
        'Errore nella creazione del provvedimento, idRicUdien not trovato',
        CodeErrorEnumException.RICORSO_UDIENZA_NOT_FOUND,
      );
    }
    try {
      let bufferOscurato = null;
      const placeholderOscurato =
        generateProvvLavorazioneInput.argsProvvedimento.generaOscurato;
      generateProvvLavorazioneInput.argsProvvedimento.idProvv = idProvvLav;
      if (
        generateProvvLavorazioneInput?.argsProvvedimento?.generaOscurato ||
        generateProvvLavorazioneInput?.allegatoOscurato
      ) {
        const fileOscurato = readFileSync(
          'dist/templates/templateProvvedimentoOscurato.json',
          'utf-8',
        );

        const variablesOscurato =
          await this.placeholderService.resolveVariables(
            generateProvvLavorazioneInput.nrg,
            ricUdien?.idRicudien,
            generateProvvLavorazioneInput.idUdienza,
            fileOscurato,
            generateProvvLavorazioneInput.argsProvvedimento,
          );
        const jsonOscurato = JSON.parse(fileOscurato);
        variablesOscurato['@@@args.placeholderOscurato@@@'] =
          generateProvvLavorazioneInput.argsProvvedimento.generaOscurato;
        variablesOscurato['@@@args.visibNomePopolo@@@'] =
          generateProvvLavorazioneInput.argsProvvedimento
            .tipologiaProvvedimento === 'MINUTA_SENTENZA';
        bufferOscurato = await exportFile(
          jsonOscurato,
          variablesOscurato
        );
      }
      const fileNonOscurato = readFileSync(
        'dist/templates/' +
          (generateProvvLavorazioneInput.strutturato
            ? 'templateProvvedimentoForStrutturato.json'
            : 'templateProvvedimento.json'),
        'utf-8',
      );

      generateProvvLavorazioneInput.argsProvvedimento.generaOscurato = false;
      const variablesNonOscuato =
        await this.placeholderService.resolveVariables(
          generateProvvLavorazioneInput.nrg,
          ricUdien?.idRicudien,
          generateProvvLavorazioneInput.idUdienza,
          fileNonOscurato,
          generateProvvLavorazioneInput.argsProvvedimento,
        );

      variablesNonOscuato['@@@args.placeholderOscurato@@@'] =
        placeholderOscurato;
      variablesNonOscuato['@@@args.visibNomePopolo@@@'] =
        generateProvvLavorazioneInput.argsProvvedimento
          .tipologiaProvvedimento === 'MINUTA_SENTENZA';

      const jsonNonOscurato = JSON.parse(fileNonOscurato);
      const bufferNonOscurato = await exportFile(
        jsonNonOscurato,
        variablesNonOscuato,
      );

      generateProvvLavorazioneInput.argsProvvedimento.generaOscurato =
        generateProvvLavorazioneInput.allegatoOscurato ||
        generateProvvLavorazioneInput.argsProvvedimento.generaOscurato ||
        placeholderOscurato;
      this.logger.log(
        `Fine creazione del docx usando la libreria ns. idProvv:${idProvvLav}`,
      );
      return [bufferNonOscurato, bufferOscurato];
    } catch (e) {
      this.logger.error(
        `Errore nella creazione del docx usando la libreria ns. idProvv:${idProvvLav}`,
        e,
      );
      throw new InternalServerErrorException('Errore nella libreria docx', e);
    }
  }
  /**
   * Aggiorna i file sul db con quelli generarti nuovi, se l'oscurati non esiste viene creato se generato oscurato e true, altrimenti elimina il file oscurato se esiste.
   * @param generateProvvLavorazioneInput
   * @param fileProvvedimentoListPDF
   * @param bufferOscurato
   * @param bufferNonOscurato
   * @param idProvvLav
   * @private
   */
  private async aggiornamentoDeiFileGiaEsistenti(
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
    fileProvvedimentoListPDF: PenaleProvvLavFileEntity[],
    bufferOscurato: any,
    bufferNonOscurato: Buffer,
    idProvvLav: string,
  ) {
    this.logger.log(
      `Aggiornamento dei file del docx e pdf quando si salva una bozza. idProvv:${idProvvLav}`,
    );
    if (
      generateProvvLavorazioneInput.allegatoOscurato ||
      generateProvvLavorazioneInput?.argsProvvedimento?.generaOscurato
    ) {
      //Caso in cui devo generare l'oscurato e quando devo aggiornarlo se prima esta stato gia generato
      await this.casoGeneraOscurato(
        fileProvvedimentoListPDF,
        bufferOscurato,
        bufferNonOscurato,
        idProvvLav,
        generateProvvLavorazioneInput,
      );
    } else {
      await this.casoDoveNonDeveEssereGeneratoIlFileOscurato(
        fileProvvedimentoListPDF,
        bufferNonOscurato,
      );
    }
    this.logger.log(
      `Fine aggiornamento dei file del docx e pdf quando si salva una bozza. idProvv:${idProvvLav}`,
    );
  }

  private async casoDoveNonDeveEssereGeneratoIlFileOscurato(
    fileProvvedimentoListPDF: PenaleProvvLavFileEntity[],
    bufferNonOscurato: Buffer,
  ) {
    this.logger.log(
      `Aggiornamento dei file del docx e pdf quando si salva una bozza e non si deve generare il file oscurato. fileProvvedimentoListPDF:${fileProvvedimentoListPDF?.length}`,
    );
    //caso in cui non devo generare l'oscurato
    if (fileProvvedimentoListPDF.length == 1) {
      //aggiorno solo il file non oscurato
      await this.updateFileGiaEsistenti(
        fileProvvedimentoListPDF,
        bufferNonOscurato,
      );
    } else {
      // caso in cui devo eliminare quello oscurato
      const penaleProvvLavFileEntity = fileProvvedimentoListPDF.find(
        file => file.oscurato,
      );
      if (penaleProvvLavFileEntity?.idCategoria) {
        await this.deleteFileOscurato(penaleProvvLavFileEntity.idCategoria);
      }
    }
  }

  private async casoGeneraOscurato(
    fileProvvedimentoListPDF: PenaleProvvLavFileEntity[],
    bufferOscurato: any,
    bufferNonOscurato: Buffer,
    idProvvLav: string,
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ) {
    this.logger.log(
      `Aggiornamento dei file del docx e pdf quando si salva una bozza e si deve generare il file oscurato. idProvvLav :${idProvvLav},
      fileProvvedimentoListPDF:${fileProvvedimentoListPDF?.length}`,
    );
    if (fileProvvedimentoListPDF.length >= 2) {
      await this.updateFileGiaEsistenti(
        fileProvvedimentoListPDF,
        bufferNonOscurato,
        bufferOscurato,
      );
    } else {
      await this.saveFileOscurato(
        bufferOscurato,
        idProvvLav,
        generateProvvLavorazioneInput,
      );
    }
  }

  private async updateFileGiaEsistenti(
    fileProvvedimentoListPDF: PenaleProvvLavFileEntity[],
    bufferNonOscurato: Buffer,
    bufferOscurato?: Buffer,
  ) {
    this.logger.log(
      `Aggiornamento dei file del docx e pdf già esistenti.,
      fileProvvedimentoListPDF:${fileProvvedimentoListPDF?.length}`,
    );
    for (const provLav of fileProvvedimentoListPDF) {
      if (provLav.idCategoria) {
        await this.updateProvvedimentoLav(provLav.idCategoria, {
          content: provLav.oscurato ? bufferOscurato : bufferNonOscurato,
        });
      }
    }
  }

  /**
   * Controlla che il bufferOscurato sia Valorizzato e salva il file oscurato sul DB
   * @param bufferOscurato
   * @param idProvvLav
   * @param generateProvvLavorazioneInput
   * @private
   */
  private async saveFileOscurato(
    bufferOscurato: any,
    idProvvLav: string,
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ) {
    if (bufferOscurato) {
      this.logger.log(
        `Salvataggio del file Oscurato.,
      idProvvLav:${idProvvLav}`,
      );
      await this.saveProvvedimentoInLavorazione(
        idProvvLav,
        bufferOscurato,
        'docx',
        `Provvedimento_${generateProvvLavorazioneInput.argsProvvedimento.numRuolo}_${generateProvvLavorazioneInput.argsProvvedimento.anRuolo}_oscurato.docx`,
        true,
      );
    }
  }

  private checkTestoOscurato(
    generateProvvLavorazioneInput: GenerateProvvLavorazioneInput,
  ) {
    const checkTestOscuratoVar =
      !generateProvvLavorazioneInput.argsProvvedimento.generaOscurato ||
      (generateProvvLavorazioneInput.argsProvvedimento.generaOscurato &&
        generateProvvLavorazioneInput.argsProvvedimento.textOscurato);
    this.logger.log(
      `Check del file testo Oscurato.,
      checkTestoOscurato:${checkTestOscuratoVar}`,
    );
    return Boolean(checkTestOscuratoVar || checkTestOscuratoVar == '');
  }

  /**
   * Questo viene chiamato dello dal relatore/estensore se si firma un singolo fascicolo(senza passare dalla coda deposito)
   * @param firmaProvvLavorazioneInput dati
   */
  async firmaEDepositaMutation(
    firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput,
  ) {
    return await this.connection.manager.transaction(async () => {
      const idAutore = await this.authService.getCurrentId();
      const provv = await this.getProvvLavorazioneByIdProvv(
        firmaProvvLavorazioneInput.idProvvedimento,
        idAutore,
      );

      Utils.checkAlreadyWorked(
        provv,
        ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE,
        this.logger,
      );

      Utils.checkAlreadyWorked(
        provv,
        ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE,
        this.logger,
      );

      if (!provv?.idProvvedimento) {
        this.logger.warn(
          `Provvedimento non trovato. idProvv:${firmaProvvLavorazioneInput?.idProvvedimento}`,
        );
        throw new InternalServerErrorException(
          'errore nella ricerca del provvedimento in lavorazione',
        );
      }
      const file = await this.getFileProvvedimento(
        provv?.idProvvedimento,
        'pdf',
      );

      const fileXml = await this.getFileProvvedimentoByOne(
        provv.idProvvedimento,
        'xml',
      );
      if (!file || !fileXml) {
        this.logger.warn(
          `File o filexml vuoti per il provvedimento. idProvv:${provv?.idProvvedimento}`,
        );
        throw new InternalServerErrorException(
          'errore nella ricerca del provvedimento in lavorazione',
        );
      }
      firmaProvvLavorazioneInput.tipologiaProvvedimento = provv.tipo;
      //console.log("provv",provv)
      const idCat = await this.firmaEDeposita(
        firmaProvvLavorazioneInput,
        file,
        fileXml,
      );
      if (!Number(idCat)) {
        throw new FirmaEDepositaException(
          'Errore nella firma e deposito. idCat null',
        );
      }
      const changeStatusValue = new CreateProvvedimentiChangeStatusInput();
      changeStatusValue.idProvvedimento = provv.idProvvedimento;
      changeStatusValue.stato =
        ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE;
      changeStatusValue.idAutore = idAutore;
      changeStatusValue.prevStato = provv.stato;
      provv.stato = ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE;
      provv.dataUltimaModifica = new Date();

      this.logger.log(
        `metodo firma e deposita relatore e inserimento nel change status. idProvv:${firmaProvvLavorazioneInput?.idProvvedimento}`,
      );
      await this.changeStatusService.createProvvedimentoChangeStatus(
        changeStatusValue,
      );
      provv.dataDeposito = new Date();
      const provvWithIDCat = await this.setIdCat(
        idCat,
        new PenaleProvvedimentiEntity({ ...provv }),
      );
      return provvWithIDCat;
    });
  }
}
