import { Inject, InternalServerErrorException } from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { CodeDepositoEntity } from '../consultazioni-graphql/entities/code_deposito.entity';
import { CodeDepositoInput } from '../consultazioni-graphql/entities/dto/code-deposito.input';
import { CodeDepositoOutput } from '../consultazioni-graphql/entities/dto/code-deposito.output';

@UfficioService()
export class CodeDepositoService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  codeDeposito(): Promise<CodeDepositoEntity[] | null> {
    return this.connection.getRepository(CodeDepositoEntity).find({});
  }

  codeDepositoByIdProvvCf(
    idProvv: string,
    cf: string,
  ): Promise<CodeDepositoEntity | null> {
    return this.connection.getRepository(CodeDepositoEntity).findOne({
      where: { idProvv: idProvv, cf: cf },
    });
  }

  codeDepositoByIdProvv(idProvv: string): Promise<CodeDepositoEntity | null> {
    return this.connection.getRepository(CodeDepositoEntity).findOne({
      where: { idProvv: idProvv },
    });
  }

  codeDepositoByCf(cf: string): Promise<CodeDepositoEntity[] | null> {
    return this.connection.getRepository(CodeDepositoEntity).find({
      where: { cf: cf },
    });
  }

  async createCodeDeposito(
    input: CodeDepositoInput,
    entityManager?: EntityManager,
  ): Promise<CodeDepositoOutput | null> {
    if (entityManager) {
      return entityManager.getRepository(CodeDepositoEntity).save({
        idProvv: input.idProvv,
        cf: input.cf,
      });
    }
    return await this.connection.getRepository(CodeDepositoEntity).save({
      idProvv: input.idProvv,
      cf: input.cf,
    });
  }

  async deleteCodeDeposito(input: CodeDepositoInput) {
    const result = await this.connection
      .getRepository(CodeDepositoEntity)
      .delete({
        idProvv: input.idProvv,
        cf: input.cf,
      });
    if (result?.affected != null && result.affected > 0) {
      return true;
    } else {
      return false;
    }
  }
}
