import { Inject, InternalServerErrorException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleUdienzaEntity } from '../consultazioni-graphql/entities/penale_udienza.entity';

@UfficioService()
export class PenaleUdienzaService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  penaleUdienza(): Promise<PenaleUdienzaEntity[] | null> {
    return this.connection.getRepository(PenaleUdienzaEntity).find({});
  }

  penaleUdienzaByIdUdienza(
    idUdienza: number,
  ): Promise<PenaleUdienzaEntity | null> {
    return this.connection.getRepository(PenaleUdienzaEntity).findOne({
      where: { idUdienza: idUdienza },
    });
  }
}
