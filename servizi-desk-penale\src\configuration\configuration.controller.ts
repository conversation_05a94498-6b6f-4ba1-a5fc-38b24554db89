import { Controller, Get } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import process from 'process';

@ApiTags('configuration')
@Controller('configuration')
@ApiBearerAuth('access-token')
export class ConfigurationController {
  constructor(private configService: ConfigService) {}

  @Get()
  readConfiguration(): string | undefined {
    return this.configService.get('app');
  }
}
