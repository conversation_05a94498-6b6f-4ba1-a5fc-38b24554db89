import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTProvvedEntity } from '../consultazioni-graphql/entities/penale_t_provved.entity';

@UfficioService()
export class ProvvimpugnatiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  provvedimentiImpugnati(): Promise<PenaleTProvvedEntity[]> {
    return this.connection.getRepository(PenaleTProvvedEntity).find();
  }

  provvedimentoImpugnato(
    idProvv: number,
  ): Promise<PenaleTProvvedEntity | null> {
    return this.connection
      .getRepository(PenaleTProvvedEntity)
      .findOneBy({ idProvvedimento: idProvv });
  }

  async provvedimentoImpugnatoByNrg(nrg: number) {
    return this.connection.getRepository(PenaleTProvvedEntity).findOne({
      where: { nrg: nrg, impugnato: true },
      relations: {
        tipoProvv: true,
        gradoProvv: true,
      },
    });
  }
  async provvedimentoDetailsByNrg(nrg: number) {
    return this.connection.getRepository(PenaleTProvvedEntity).find({
      where: { nrg: nrg },
      relations: {
        tipoProvv: true,
        gradoProvv: true,
      },
    });
  }
}
