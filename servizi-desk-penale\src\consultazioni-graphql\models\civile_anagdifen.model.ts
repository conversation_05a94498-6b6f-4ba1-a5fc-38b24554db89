import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
@ObjectType()
export class CivileAnagdifen {
  @Field(type => ID)
  idParam: number;

  @Field(type => String)
  cognome?: string;
  @Field(type => String)
  nome?: string;
  @Field(type => String)
  codiceAvvocato?: string;
  @Field(type => String)
  codiceFiscale?: string;
  @Field(type => Date)
  dataNascita?: Date;
  @Field(type => String)
  luogoNascita?: string;
  @Field(type => String)
  provicniaNascita?: string;
  @Field(type => Date)
  dataCassazionista?: Date;
  @Field(type => Int)
  foroProvincia?: number;
  @Field(type => Int)
  codiceForo?: number;
  @Field(type => Date)
  dtAvv?: Date;
  @Field(type => Int)
  dUff?: number;
  @Field(type => Date)
  dtProc?: Date;
  @Field(type => String)
  email?: string;
  @Field(type => Int)
  idAuloRif?: number;
  @Field(type => Int)
  deceduto?: number;
  @Field(type => String)
  fax?: string;
  @Field(type => String)
  nazionalita?: string;
  @Field(type => String)
  codiceFiscalePrecedente?: string;
}
