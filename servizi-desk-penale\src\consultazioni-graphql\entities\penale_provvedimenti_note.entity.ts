import {
  BeforeInsert,
  Column,
  Entity,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { v4 as uuid4 } from 'uuid';
import { StatoInserimentoNoteEnum } from './enumaration/stato-inserimento-note';
@Entity('PENALE_PROVV_NOTE') //nome tabella su schema oracle
export class PenaleProvvedimentiNoteEntity {
  @PrimaryColumn({ name: 'ID_PROVV_NOTE', default: 'uuid_generate_v4()' })
  idProvvNote: string;
  public constructor(init?: Partial<PenaleProvvedimentiNoteEntity>) {
    Object.assign(this, init);
  }
  @BeforeInsert()
  generateUuid() {
    this.idProvvNote = uuid4().replace(/-/g, '');
  }

  @Column({ name: 'ID_PROVV' })
  idProvvedimento: string;

  @Column({ name: 'NOTE' })
  note: string;
  @Column({ name: 'DATA_INSERIMENTO' })
  dataInserimento: Date;
  @Column({ name: 'ID_AUTORE' })
  idAutore: number;
  @Column({ name: 'STATO_INSERIMENTO' })
  statoInserimento: StatoInserimentoNoteEnum;
}
