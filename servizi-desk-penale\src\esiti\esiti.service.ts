import { Inject } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';

@UfficioService()
export class EsitiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  esiti(): Promise<PenaleTEsitoEntity[]> {
    return this.connection.getRepository(PenaleTEsitoEntity).find();
  }

  esito(idRicUdienza: number): Promise<PenaleTEsitoEntity | null> {
    return this.connection.getRepository(PenaleTEsitoEntity).findOne({
      where: { idRicUdienza: idRicUdienza },
      relations: { privacyParam: true },
    });
  }
  esitoByidsRicUdien(
    idsRicUdienza: Array<number>,
  ): Promise<Array<PenaleTEsitoEntity>> {
    return this.connection.getRepository(PenaleTEsitoEntity).find({
      where: { idRicUdienza: In(idsRicUdienza) },
      relations: { privacyParam: true },
    });
  }
}
