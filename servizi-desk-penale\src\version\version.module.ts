import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { VersionService } from './version.service';
import { VersionResolver } from './version.resolver';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { UfficiModule } from 'src/uffici/uffici.module';

@Module({
  imports: [
    ConfigModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
    UfficiModule,
  ],
  providers: [VersionService, VersionResolver],
  exports: [VersionService],
})
export class VersionModule {}
