import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';

@Entity('PENALE_T_PARTI') //nome tabella su schema oracle
export class PenalePartiEntity {
  @PrimaryColumn({ name: 'ID_PARTE' })
  idParte: number;

  @Column({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'ALTRI' })
  altri: string;

  @Column({ name: 'ID_TIPOFIG' })
  idTipoFig: number;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_TIPOFIG' })
  tipoFig?: PenaleParamEntity;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'OPERATORE' })
  operatore: number;

  @Column({
    name: 'RICORRENTE',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  ricorrente: boolean;
  @Column({ name: 'DTINIZ_STATO' })
  dtinizStato: Date;
  @Column({ name: 'PENASUP5' })
  penaSup5: string;
  @Column({ name: 'STATOPARTE' })
  statoParte: string;
  @Column({ name: 'DATARRESTO' })
  datarresto: Date;
  @Column({ name: 'DATASCARCER' })
  dataScarcerazione: Date;
  @Column({ name: 'DATADECOR' })
  dataDecor: Date;
  @Column({ name: 'TIPOLEGAME' })
  tipoLegame: number;
  @Column({ name: 'NUMORD' })
  numOrdine: number;
  @Column({ name: 'ID_ANAGPARTE' })
  idAnagraficaParte: number;
  @Column({ name: 'STRALCIO' })
  stralcio: string;
  @Column({ name: 'SECRETATA' })
  secretata: string;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'UFFINS' })
  uffins?: PenaleParamEntity;
  @Column({ name: 'ID_PARAMFIG' })
  idParamfig: number;
  @Column({ name: 'PRIVACY' })
  privacy: number;
  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;
  @Column({ name: 'ART161' })
  art161: string;
  @Column({ name: 'ART159' })
  art159: string;
  @Column({ name: 'ART165' })
  art165: string;
  public constructor(init?: Partial<PenalePartiEntity>) {
    Object.assign(this, init);
  }
}
