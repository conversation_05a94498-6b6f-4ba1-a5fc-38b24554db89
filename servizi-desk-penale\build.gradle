String packageJsonContent = new File(project.projectDir, 'package.json' ).getText( 'UTF-8' )
packageJsonContent = packageJsonContent.replaceAll('("version": ")((.)+)(",)', '"version": "' + project.version + (rcVersion ? ("-RC" + rcVersion) : "") + '",' )
new File(project.projectDir, 'package.json').write( packageJsonContent, 'UTF-8' )

task build {
    doLast {
		if (System.getProperty('os.name').toLowerCase(Locale.ROOT).contains('windows')) {
			exec {
				workingDir "$projectDir"
				commandLine 'cmd', '/c', 'yarn', 'install'
			}
			exec {
				workingDir "$projectDir"
				commandLine 'cmd', '/c', 'yarn', 'build'
			}		
		} else {
			exec {
				workingDir "$projectDir"
				commandLine 'yarn', 'install'
			}
			exec {
				workingDir "$projectDir"
				commandLine 'yarn', 'build'
			}
		}
		copy {
			from "package.json"
			into buildDir
		}
		copy {
			from "dist"
			into new File(buildDir, "dist")
		}
		copy {
			from "node_modules"
			into new File(buildDir, "node_modules")
		}
    }
}

task removeNsLibraries {
    doLast {
		File yarnLockFile = new File(project.projectDir, 'yarn.lock')
		String contents = yarnLockFile.getText() 
		int i = contents.indexOf("\n\"@ns/docx-tools-js")
		if (i > 0) {
			String part1 = contents.substring(0, i)
		 	i = contents.indexOf("\n\"@nuxtjs/opencollective")
		 	String part2 = contents.substring(i, contents.length())
			yarnLockFile.setText(part1 + part2)
		}
    }
}

task clean {
	delete "build", "dist", "node_modules"
}

build.finalizedBy removeNsLibraries
