import { Inject, Logger } from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleMPProvvEntity } from '../consultazioni-graphql/entities/penale_mp_provv.entity';
import { PenaleProvvedimenti } from '../consultazioni-graphql/models/penale_provvedimenti.model';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';

@UfficioService()
export class ProvvedimentoRelazioneService {
  private logger = new Logger(ProvvedimentoRelazioneService.name);

  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  /**
   * Restituisce la reazione tra ordinanza o sentenza con la sua minuta
   * @param idProvv id dell'ordinzanza o della sentenza
   */
  provvRelazioneByProvvDest(idProvv: string): Promise<PenaleMPProvvEntity[]> {
    this.logger.log(
      `Tabella relazione dei provvedimenti ricerca destinazione:${idProvv}`,
    );
    return this.connection.getRepository(PenaleMPProvvEntity).find({
      where: { idProvvedimentoDestinazione: idProvv },
      order: {
        createAt: 'DESC',
      },
    });
  }

  /**
   * Restituisce tutti gli stati del provvedimento, controllando anche gli stati delle sentenze o Ordinzante
   * che sono state rifiutate dalla cancelleria dopo la firma del presidente
   * @param provveidmentoRelatore relazione del provvedimento con quello precedente
   * @param idProvvDaEscludere il provvedimento iniziale e da ecludere perche vengono prelevati nel metodo superiore.
   * @private
   */
  async getAllStatusLIFOAndSentenzeOOrdinazeRifiutate(
    idProvvDaEscludere: string,
  ) {
    this.logger.log(
      `tracking degli stati con metologia Lifo. idProvvDaEscludere:${idProvvDaEscludere}`,
    );
    const idsAllProvv: Array<string> = new Array<string>();
    //  prendo il provvedimento da dove è stato dupplicato
    let newIdToClone: string | null = idProvvDaEscludere || null;
    while (newIdToClone !== null) {
      // controllo che questo provvedimento non sia stato clonato piu di una volta
      const allDuplicateProvvedimenti: PenaleMPProvvEntity[] =
        await this.provvRelazioneByProvvOrigine(newIdToClone);
      // prendo l'ultimo provvedimento in ordine di date ci dovrebbe essere sempre e solo un provv
      const provCheck: PenaleMPProvvEntity | null =
        await this.provvRelazioneByProvvDestLastDate(newIdToClone);

      if (provCheck?.idProvvedimentoDestinazione) {
        // se la relazione e verificata filtro i provvedimenti duplicati eliminado quello corrente
        const indexOf = this.myIndexOf(allDuplicateProvvedimenti, provCheck);
        const allDuplicateProvvedimentiFiltrato =
          indexOf >= 0
            ? allDuplicateProvvedimenti.splice(indexOf, 1)
            : allDuplicateProvvedimenti;
        // setto il nuovo id del provv ordine andndo in ritroso ed è il nuovo provv da controllare e lo aggiungo alla lista
        newIdToClone = provCheck?.idProvvedimentoOrigine;
        idsAllProvv.push(newIdToClone);
        // mi prendo tutti gli di dei duplicati
        for (const duplicatoSentenzaOOrdinanza of allDuplicateProvvedimentiFiltrato) {
          // inserisco le ordinaze o sentenze  duplicate quindi gli id di destinazione
          idsAllProvv.push(
            duplicatoSentenzaOOrdinanza.idProvvedimentoDestinazione,
          );
        }
      } else {
        if (allDuplicateProvvedimenti) {
          for (const duplicatoSentenzaOOrdinanza of allDuplicateProvvedimenti) {
            // inserisco le ordinaze o sentenze  duplicate quindi gli id di destinazione
            idsAllProvv.push(
              duplicatoSentenzaOOrdinanza.idProvvedimentoDestinazione,
            );
          }
        }
        // setto a null l'id duplicato perche non c'è nessuna relazione cosi posso uscire dal ciclo
        newIdToClone = null;
      }
    }
    if (idsAllProvv.length > 0) {
      this.logger.log(
        `Tracking degli stati con metologia Lifo. ids risultati idsProvvedimenti:${idsAllProvv}`,
      );
      return idsAllProvv;
    }
    this.logger.log(
      `Nessun tracking degli stati con metologia Lifo trovato. idProvvDaEscludere:${idProvvDaEscludere}`,
    );
    return null;
  }

  myIndexOf(
    listRelazioni: PenaleMPProvvEntity[],
    relazione: PenaleMPProvvEntity,
  ) {
    this.logger.log(
      `Restituisce l'ultimo indice per la relazione. listRelazioni:${listRelazioni}, relazione:${relazione}`,
    );
    for (let i = 0; i < listRelazioni.length; i++) {
      if (this.compareTo(listRelazioni[i], relazione)) {
        this.logger.log(
          `Restituisce l'ultimo indice per la relazione. listRelazioni:${listRelazioni}, relazione:${relazione}, indexTrovato:${i}`,
        );
        return i;
      }
    }
    this.logger.log(
      `Restituisce l'ultimo indice per la relazione. listRelazioni:${listRelazioni}, relazione:${relazione} elemento non trovato`,
    );
    return -1;
  }

  /**
   * Controlla se il provvedimento può essere creato
   * @param penaleProvvedimenti
   */
  async disabledCreaNuovoProv(
    penaleProvvedimenti: PenaleProvvedimenti | PenaleProvvedimentiEntity | null,
    isEstesore: boolean,
  ) {
    this.logger.log(
      `Inizio controllo se può essere disabilitato il provvedimento. idProvv:${penaleProvvedimenti?.idProvvedimento}`,
    );
    if (!isEstesore) {
      this.logger.log(
        `Fine controllo se può essere disabilitato il provvedimento. l'utente non è un estensore`,
      );
      return true;
    }
    if (penaleProvvedimenti) {
      if (
        penaleProvvedimenti.stato ===
          ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE ||
        penaleProvvedimenti.stato === ProvvedimentiStatoEnum.BUSTA_RIFIUTATA ||
        penaleProvvedimenti.stato === ProvvedimentiStatoEnum.PUBBLICATA
      ) {
        const provvedimentoClonato = await this.provvRelazioneByProvvOrigine(
          penaleProvvedimenti.idProvvedimento,
        );
        this.logger.log(
          `Fine controllo se può essere disabilitato il provvedimento. il provvedimento è stato gia clonato? clone: ${!!provvedimentoClonato}`,
        );
        return !!provvedimentoClonato;
      }
      this.logger.log(
        `Fine controllo se può essere disabilitato il provvedimento. il provvedimento non può essere duplicato`,
      );
      return true;
    }
    this.logger.log(
      `Fine controllo se può essere disabilitato il provvedimento. il provvedimento può essere duplicato`,
    );
    return false;
  }

  provvRelazioneByProvvOrigine(
    idProvvMin: string,
  ): Promise<PenaleMPProvvEntity[]> {
    this.logger.log(
      `lista dei provvedimenti originale ordinata per creazione decrescente . idProvvMin:${idProvvMin},`,
    );
    return this.connection.getRepository(PenaleMPProvvEntity).find({
      where: { idProvvedimentoOrigine: idProvvMin },
      order: {
        createAt: 'DESC',
      },
    });
  }

  async insertRelazioneTraProvv(
    idProvv: string,
    idProvvClonato: string,
    entityManager?: EntityManager,
  ) {
    this.logger.log(
      `Inserimento nella tabella relazionale dei provvedimenti. idProvvedimentoOrigine:${idProvvClonato}, idProvvedimentoDestinazione:${idProvv}`,
    );
    const provvRelazione = new PenaleMPProvvEntity();
    provvRelazione.idProvvedimentoOrigine = idProvvClonato;
    provvRelazione.idProvvedimentoDestinazione = idProvv;
    provvRelazione.createAt = new Date();
    if (entityManager) {
      return entityManager
        .getRepository(PenaleMPProvvEntity)
        .save(provvRelazione);
    }
    return await this.connection
      .getRepository(PenaleMPProvvEntity)
      .save(provvRelazione);
  }

  async deleteRelazioneSoruceDestinazione(idProvv: string) {
    this.logger.log(
      `Cancellazione nella tabella relazionale dei provvedimenti. idProvvedimentoDestinazione:${idProvv}`,
    );
    const result = await this.connection
      .getRepository(PenaleMPProvvEntity)
      .delete({
        idProvvedimentoDestinazione: idProvv,
      });
    if (result?.affected != null && result.affected > 0) {
      return true;
    } else {
      return false;
    }
  }

  async provvRelazioneByProvvDestLastDate(idProvvedimento: string) {
    this.logger.log(
      `L'ultimo elemento inserito nella tabella relazionale dei provvedimenti ordinato per data di creazione decrescente . idProvvedimentoDestinazione:${idProvvedimento}`,
    );
    return this.connection.getRepository(PenaleMPProvvEntity).findOne({
      where: { idProvvedimentoDestinazione: idProvvedimento },
      order: {
        createAt: 'DESC',
      },
    });
  }

  provvRelazioneByProvvOrigineLastDate(
    idProvvMin: string,
  ): Promise<PenaleMPProvvEntity | null> {
    this.logger.log(
      `L'ultimo elemento inserito nella tabella relazionale dei provvedimenti originali ordinato per data di creazione decrescente . idProvvedimentoOrigine:${idProvvMin}`,
    );
    return this.connection.getRepository(PenaleMPProvvEntity).findOne({
      where: { idProvvedimentoOrigine: idProvvMin },
      order: {
        createAt: 'DESC',
      },
    });
  }

  private compareTo(a: PenaleMPProvvEntity, b: PenaleMPProvvEntity): boolean {
    return (
      a.idProvvedimentoDestinazione == b.idProvvedimentoDestinazione &&
      a.idProvvedimentoOrigine == b.idProvvedimentoOrigine &&
      a.createAt.toISOString() == b.createAt.toISOString()
    );
  }
}
