import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleProfiloEntity } from '../consultazioni-graphql/entities/penale_profilo.entity';

@UfficioService()
export class ProfiloService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  profili(): Promise<PenaleProfiloEntity[]> {
    return this.connection.getRepository(PenaleProfiloEntity).find();
  }

  profilo(idProfilo: number): Promise<PenaleProfiloEntity | null> {
    return this.connection
      .getRepository(PenaleProfiloEntity)
      .findOneBy({ idProfilo: idProfilo });
  }
}
