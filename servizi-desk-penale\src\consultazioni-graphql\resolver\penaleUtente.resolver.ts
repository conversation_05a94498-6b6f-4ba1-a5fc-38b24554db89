import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, <PERSON>rent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UtenteService } from '../../utente/utente.service';
import { PenaleTUtente } from '../models/penale_t_utente.model';
import { ProfiloService } from '../../profilo/profilo.service';
import { PenaleProfilo } from '../models/penale_profilo.model';

@Resolver(() => PenaleTUtente)
export class PenaleUtenteResolver {
  constructor(
    private readonly utenteService: UtenteService,
    private readonly profiloService: ProfiloService,
  ) {}

  @Query(() => PenaleTUtente, { name: 'utenteById' })
  async utenteById(@Args('id') id: number): Promise<PenaleTUtente> {
    const utente = await this.utenteService.utente(id);
    if (!utente) {
      throw new NotFoundException(id);
    }
    return utente;
  }

  @Query(returns => [PenaleTUtente], { name: 'utenti' })
  utente(): Promise<PenaleTUtente[]> {
    return this.utenteService.utenti();
  }

  @Query(returns => PenaleTUtente, { name: 'utentiByCf' })
  async utenteByCf(@Args('cf') cf: string): Promise<PenaleTUtente | null> {
    const utente = await this.utenteService.utenteByCf(cf);
    if (!utente) {
      throw new NotFoundException(cf);
    }
    return utente;
  }

  @ResolveField('profilo', () => PenaleProfilo, { nullable: true })
  async getAutore(
    @Parent() penaleTUtente: PenaleTUtente,
  ): Promise<PenaleProfilo | null> {
    if (penaleTUtente.idProfilo && penaleTUtente.idProfilo > 0) {
      return this.profiloService.profilo(penaleTUtente.idProfilo);
    }
    return null;
  }
}
