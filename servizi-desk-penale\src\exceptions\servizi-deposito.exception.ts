import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class ServiziDepositoException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
    error?: CodeErrorEnumException,
  ) {
    super(
      response,
      error ?? CodeErrorEnumException.SERVIZI_DEPOSITO_ERROR,
      NSTypeErrorEnum.ERROR,
      options,
    );
  }
}
