import { Modu<PERSON> } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { TemplateTextAreaService } from './template-text-area.service';
import { TemplateTextAreaController } from './template-text-area.controller';
import { AuthModule } from '../auth/auth.module';
import { UfficiModule } from '../uffici/uffici.module';

@Module({
  imports: [
    AuthModule,
    UfficiModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql', 'template'],
    }),
  ],
  providers: [TemplateTextAreaService],
  exports: [TemplateTextAreaService],
  controllers: [TemplateTextAreaController],
})
export class TemplateTextAreaModule {}
