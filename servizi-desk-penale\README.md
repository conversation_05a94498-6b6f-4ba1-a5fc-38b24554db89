BE Starter pack
===============

Ricetta:
Dal progetto NEST pulito:
* Aggiunte le dipendenze graphql  `yarn add @nestjs/graphql @nestjs/apollo @apollo/server graphql`
* Aggiunto `@nestjs/config`, la cartella configuration e i file .env*
* Aggiunto graphql-relay e la cartella common con le utility per relay
* Aggiunto supporto a typeorm `yarn add @nestjs/typeorm typeorm` -- Da aggiungere il driver del DB utilizzato
* Aggiunto supporto alla validazione con `yarn add class-validator class-transformer` -- vedi [validation](https://docs.nestjs.com/techniques/validation)
* Aggiunto supporto logging con `nest-winston winston` e configurazione in main.ts 


## TODO 

* Fixare il logging degli errori graphql
* Mettere a posto la paginazione
* Spiegare il GUARD di NEST
* SPIEGARE come loggare con logger di nest
* Spiegare l'authService