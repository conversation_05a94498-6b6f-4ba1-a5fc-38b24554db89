import { Logger, NotFoundException } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { ProvvedimentiNoteService } from '../../provvedimenti-note/provvedimenti-note.service';
import { PenaleProvvedimentiNote } from '../models/penale_provvedimenti_note.model';
import { CreateProvvedimentiNoteInput } from '../entities/dto/create-provvedimenti-note.input';
import { PenaleTUtente } from '../models/penale_t_utente.model';
import { UtenteService } from '../../utente/utente.service';

@Resolver(() => PenaleProvvedimentiNote)
export class PenaleProvvedimentoNoteResolver {
  private logger = new Logger(PenaleProvvedimentoNoteResolver.name);
  constructor(
    private readonly provvedimentiNoteService: ProvvedimentiNoteService,
    private readonly utenteService: UtenteService,
  ) {}

  @Query(() => [PenaleProvvedimentiNote], { name: 'provvedimentoNote' })
  async provvedimentoNote(
    @Args('id') id: string,
  ): Promise<PenaleProvvedimentiNote[]> {
    this.logger.log("Ricerca della note per l'utente idProvv:", id);
    const provvediemento =
      await this.provvedimentiNoteService.provvedimentoNotaByIdProvvedimento(
        id,
      );
    if (!provvediemento) {
      throw new NotFoundException(id);
    }

    this.logger.log("Fine ricerca della note per l'utente idProvv:", id);
    return provvediemento;
  }

  @Mutation(() => PenaleProvvedimentiNote, { name: 'createProvvedimentoNote' })
  async createProvvedimento(
    @Args('provvedimento') provvedimento: CreateProvvedimentiNoteInput,
  ): Promise<PenaleProvvedimentiNote | null> {
    const provvedimentoNoteId =
      await this.provvedimentiNoteService.createProvvedimentoNote(
        provvedimento,
      );
    console.log(provvedimentoNoteId);
    if (provvedimentoNoteId) {
      return await this.provvedimentiNoteService.provvedimentoNotaById(
        provvedimentoNoteId,
      );
    }
    throw new NotFoundException('Nota sul provvedimento non inserito');
  }

  @ResolveField('autore', () => PenaleTUtente, { nullable: true })
  async getAutore(
    @Parent() penaleProvvedimentiNote: PenaleProvvedimentiNote,
  ): Promise<PenaleTUtente | null> {
    this.logger.log(
      "Ricerca dell'utente per la nota :",
      penaleProvvedimentiNote.idProvvNote,
    );
    return this.utenteService.utente(penaleProvvedimentiNote.idAutore);
  }

  @Query(returns => [PenaleProvvedimentiNote], { name: 'provvedimentiNote' })
  provvedimentiNote(): Promise<PenaleProvvedimentiNote[]> {
    return this.provvedimentiNoteService.provvedimentiNote();
  }
}
