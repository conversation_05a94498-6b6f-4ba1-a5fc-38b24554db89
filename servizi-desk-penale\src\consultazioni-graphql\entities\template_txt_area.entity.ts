import { BeforeInsert, <PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';
import { v4 as uuid4 } from 'uuid';
import { TipologiaTemplateTxtArea } from '../../template-text-area/entities/tipologia-template-txt-area';

@Entity({ name: 'PENALE_TEMPLATE_TXT_AREA' })
export class TemplateTextAreaEntity {
  @PrimaryColumn({ name: 'ID_TEMPLATE' })
  idTemplate: string;

  @BeforeInsert()
  generateUuid() {
    this.idTemplate = uuid4().replace(/-/g, '');
  }

  @Column({
    name: 'TIPOLOGIA',
  })
  tipologia: TipologiaTemplateTxtArea;

  @Column({ name: 'TITOLO' })
  title: string;
  @Column({ name: 'DESCRIZIONE', nullable: true })
  description: string;

  @Column({
    name: 'TEXT',
    type: 'blob',
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  content?: string | null;
  @Column({
    name: 'ORD',
  })
  ordine: number;
  public constructor(init?: Partial<TemplateTextAreaEntity>) {
    Object.assign(this, init);
  }
}
