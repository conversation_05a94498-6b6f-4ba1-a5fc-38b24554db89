import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class FirmaRemotaError2Exception extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    codeError: CodeErrorEnumException,
    options?: HttpExceptionOptions,
  ) {
    super(response, codeError, NSTypeErrorEnum.ERROR, options);
  }
}
