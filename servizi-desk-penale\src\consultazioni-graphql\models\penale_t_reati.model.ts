import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
@ObjectType()
export class PenaleTReati {
  @Field(type => ID)
  idReato?: number;

  @Field(type => String)
  fonteNorm: string;
  @Field(type => Int)
  anno: number;
  @Field(type => Int)
  art?: number;
  @Field(type => Int)
  capo: number;
  @Field(type => String)
  descrizione?: string;
  @Field(type => String)
  lettera: string;
  @Field(type => Int)
  libro: number;
  @Field(type => Int)
  comma: number;
  @Field(type => Int)
  numeroLegale: number;
  @Field(type => Int)
  idVoce: number;
  @Field(type => Int)
  titolo: number;
  @Field(type => Int)
  paragrafo: number;
  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  idFunzione: number;
  @Field(type => Int)
  operatore: number;
  @Field(type => String)
  aggrava: string;
  @Field(type => String)
  valido?: string;
  @Field(type => Int)
  privacy?: number;
  @Field(type => Int)
  glbDtime?: number;
  @Field(type => Int)
  gruppo?: number;
  @Field(type => String)
  provenienza?: string;
  @Field(type => Date)
  dataFineUtilizzo?: Date;
  @Field(type => String)
  eppo?: string;
  @Field(type => Int)
  altroIdentificativo?: number;
  @Field(type => String)
  displayReati?: string;
}
