import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_DIFENPARTI') //nome tabella su schema oracle
export class PenaleDifenpartiEntity {
  @PrimaryColumn({ name: 'ID_DIFENPARTI' })
  idDifensoriParti: number;

  @Column({ name: 'ID_ANAGDIFEN' })
  idAnagraficaDifensore: number;

  @Column({ name: 'ID_PARTI' })
  idParti: number;
  @Column({ name: 'NRG' })
  nrg: number;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'COMUNE' })
  comune: string;
  @Column({ name: 'INDIRIZZ<PERSON>' })
  indirizzo: string;
  @Column({ name: 'CAP' })
  cap: string;
  @Column({ name: 'PROVINCIA' })
  provincia: string;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPODIFEN' })
  tipoDifensore: PenaleParamEntity;
  @Column({ name: 'DATANOMINA' })
  dataAnomina: Date;
  @Column({ name: 'DATAREVOCA' })
  dataRevoca: Date;
  @Column({ name: 'DATARINUNZIA' })
  dataRinunzia: Date;

  @Column({ name: 'AVVISO' })
  avviso: string;
  @Column({ name: 'ESITO' })
  esito: string;
  @Column({ name: 'MERITO' })
  merito: string;
  @Column({ name: 'GRUPPO' })
  gruppo: number;
  @Column({ name: 'DECEDUTO' })
  deceduto: number;
}
