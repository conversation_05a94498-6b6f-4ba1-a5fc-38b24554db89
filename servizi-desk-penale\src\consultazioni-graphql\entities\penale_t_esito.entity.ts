import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';
import ColumnBooleanStringTransformer from './utility/column-boolean-string-transformer';
import { PenaleTEsitoSentEntity } from './penale_t_esito_sent.entity';

@Entity('PENALE_T_ESITO') //nome tabella su schema oracle
export class PenaleTEsitoEntity {
  @PrimaryColumn({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'RINVIODESCR' })
  rinvioDescrizione: string;

  @Column({ name: 'ART28' })
  art28: string;

  @Column({ name: 'ART94' })
  art94: string;
  @Column({ name: 'NOTE' })
  note: string;
  @Column({ name: 'ID_RICUDIEN' })
  idRicUdienza: number;
  @Column({ name: 'ID_ESITO' })
  idEsito: number;
  @Column({ name: 'MOTIVOSOSP' })
  motivoSosp: string;
  @Column({ name: 'ID_REATOPARTE' })
  idReatoParte: string;
  // se valorizzato è 1 significa che il ricorso è stato riunito dopo udienza
  @Column({
    name: 'RIUNITO',
    type: 'varchar',
    width: 1,
    transformer: new ColumnBooleanStringTransformer(),
  })
  riunito: boolean;
  @Column({ name: 'PRIVACY' })
  privacy: string;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'PRIVACY' })
  privacyParam: PenaleParamEntity;
  @Column({ name: 'SEMPLIFICATA' })
  semplificata: string;
  @OneToOne(() => PenaleTEsitoSentEntity)
  @JoinColumn({ name: 'ID_ESITO', referencedColumnName: 'idEsito' })
  esitoSent: PenaleTEsitoSentEntity;

  public constructor(init?: Partial<PenaleTEsitoEntity>) {
    Object.assign(this, init);
  }
}
