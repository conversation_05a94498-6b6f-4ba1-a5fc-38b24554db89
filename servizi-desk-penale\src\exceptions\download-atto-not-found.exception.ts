import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class DownloadAttoNotFoundException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
  ) {
    super(
      response,
      CodeErrorEnumException.FILE_PUBBLICATO_NOT_FOUND,
      NSTypeErrorEnum.WARN,
      options,
    );
  }
}
