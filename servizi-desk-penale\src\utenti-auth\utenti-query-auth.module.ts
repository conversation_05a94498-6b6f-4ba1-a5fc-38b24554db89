import { Module } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UtentiQueryAuthService } from './utenti-query-auth.service';
import { PenaleCollegioEntity } from '../consultazioni-graphql/entities/penale_collegio.entity';
import { PenaleTMagisEntity } from '../consultazioni-graphql/entities/penale_t_magis.entity';
import { PenaleAnagmagisEntity } from '../consultazioni-graphql/entities/penale_anagmagis.entity';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { PenaleTUtenteEntity } from '../consultazioni-graphql/entities/penale_t_utente.entity';
import { NsCustomLoggerTypeorm } from '../utils/ns-custom-logger-typeorm';
import { PenaleVRicercaPenaleSentenzaEntity } from 'src/consultazioni-graphql/entities/penale_v_ricerca_penale_sentenza.entity';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      name: 'auth',
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        const datasource = config.get('app.uffici.datasource');
        const dataSourceLogin = {
          name: 'auth',
          type: datasource.dbType,
          host: datasource.dbHost,
          port: parseInt(datasource.dbPort, 10),
          username: datasource.dbUser,
          password: datasource.dbPassword,
          serviceName: datasource.dbServiceName,
          logger: new NsCustomLoggerTypeorm(),
          entities: ['dist/**/*.entity{.ts,.js}'],
          synchronize: false,
          //TODO nn cancellare servono se va impostato il timezone sul db
          /*  dateStrings: ['timestamp without time zone'],
          timezone: 'UTC',*/
        }; // WARNING: non mettere a true, altrimenti sovrascrive il db
        return dataSourceLogin as TypeOrmModuleOptions;
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature(
      [
        PenaleCollegioEntity,
        PenaleTMagisEntity,
        PenaleAnagmagisEntity,
        PenaleTRicorsoUdienzaEntity,
        PenaleTUtenteEntity,
        PenaleVRicercaPenaleSentenzaEntity,
      ],
      'auth',
    ),
  ],
  providers: [UtentiQueryAuthService],
  exports: [UtentiQueryAuthService],
})
export class UtentiQueryAuthModule {}
