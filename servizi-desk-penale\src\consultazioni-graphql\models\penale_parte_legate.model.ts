import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleAnagraficaParti } from './penale_anagparti.model';

@ObjectType()
export class PenaleParteLegate {
  @Field(type => ID)
  idAnagraficaParte: number;

  @Field(type => ID)
  idParte: number;
  @Field(type => Int)
  idFunzione: number;
  @Field(type => Int)
  operatore: number;

  tipoLegame?: PenaleParam;
  anagraficaParte?: PenaleAnagraficaParti;
}
