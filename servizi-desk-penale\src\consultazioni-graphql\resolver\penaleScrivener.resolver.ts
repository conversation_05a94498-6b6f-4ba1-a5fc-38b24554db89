import { Logger } from '@nestjs/common';
import {
  Args,
  Int,
  Parent,
  Query,
  ResolveField,
  Resol<PERSON>,
  Root,
} from '@nestjs/graphql';
import { AuthService } from '../../auth/auth.service';
import { ProvvedimentiService } from 'src/provvedimenti/provvedimenti.service';
import { ScrivaniaProvvedimentiModel } from '../models/scrivania_provvedimenti.model';
import {
  PaginationCustomQueryFilter,
  ProvvedimentiDtoConnection,
} from '../models/dto/generic_paginator';
import {
  computePageInfo,
  endCursor,
  startCursor,
} from '../../relay-pagination/pagination-utils';
import { ElencoProvvedimentiService } from '../../scrivania/elenco-provvedimenti.service';
import { ScrivaniaService } from '../../scrivania/scrivania.service';
import { RicorsoUdienzaCommonService } from '../../ricorso-udienza/ricorso-udienza-common.service';
import { PenaleElencoProvvEntity } from '../entities/penale_v_elenco_provv.entity';
import { InfoProvvedimento } from '../models/dto/info_provvedimento.model';
import { Utils } from '../../utils/utils';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';

@Resolver(() => ScrivaniaProvvedimentiModel)
export class PenaleScrivenerResolver {
  private logger = new Logger(PenaleScrivenerResolver.name);
  constructor(
    private readonly elencoProvvedimentiService: ElencoProvvedimentiService,
    private readonly scrivaniaService: ScrivaniaService,
    private readonly authService: AuthService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly ricorsoUdienzaCommonService: RicorsoUdienzaCommonService,
  ) {}

  @ResolveField('relatore', () => String)
  getRelatore(@Root() provvEntity: PenaleElencoProvvEntity) {
    return `${provvEntity?.nome} ${provvEntity?.cognome}`;
  }

  @ResolveField('nrgFormat', () => String)
  getNrgFormattato(@Root() provvEntity: PenaleElencoProvvEntity) {
    const [anno, numero] = Utils.calcoloNumeroFascicolo(
      String(provvEntity.nrgReale),
    );
    return numero + '/' + anno;
  }

  @ResolveField('oscuratoSIC', () => String)
  async getOscuratoSIC(
    @Root() provvEntity: PenaleElencoProvvEntity,
  ): Promise<string> {
    const oscuramentoSic =
      await this.provvedimentiService.getProvvedimentoOscuratoSicComplessivo(
        provvEntity.idUdienza,
        provvEntity.nrg,
      );
    return oscuramentoSic ? 'SI' : 'NO';
  }

  @ResolveField('oscuratoDESKCSP', () => String)
  async checkOscuramentoDeskCp(@Root() provvEntity: PenaleElencoProvvEntity) {
    const oscuratoDESKCSP =
      await this.elencoProvvedimentiService.getOscuramentoDeskCsp(
        provvEntity.idUdienza,
        provvEntity.nrg,
      );
    return oscuratoDESKCSP ? 'Si' : 'No';
  }

  @ResolveField('riunito', () => InfoProvvedimento, { nullable: true })
  async getRiunitiProvvedimento(@Root() provvEntity: PenaleElencoProvvEntity) {
    return await this.ricorsoUdienzaCommonService.checkStatoOnSicCommon(
      provvEntity.idUdienza,
      provvEntity.nrg,
    );
  }

  @ResolveField('stato', () => ProvvedimentiStatoEnum)
  async getStatoCorretto(@Root() provvEntity: PenaleElencoProvvEntity) {
    // Applica la logica di correzione dello stato per il presidente
    const statoCorretto = await this.elencoProvvedimentiService.checkStatoCorrettoPresidente(
      provvEntity.idProvv,
      provvEntity.stato,
    );
    return statoCorretto || provvEntity.stato;
  }
  @Query(() => ScrivaniaProvvedimentiModel, { name: 'test', nullable: true })
  async getAllProvv(): Promise<ScrivaniaProvvedimentiModel | null> {
    return null;
  }
  @Query(() => ProvvedimentiDtoConnection, { name: 'provvedimentiScrivania' })
  async getProvvedimenti(
    @Args('idUdienza') idUdienza: number,
    @Args() paginationArgs: PaginationCustomQueryFilter,
  ): Promise<ProvvedimentiDtoConnection> {
    this.logger.log(
      `Richiesta dettaglio provvedimento per il presidente. idUdienza:${idUdienza}`,
    );
    const cf = await this.authService.getCurrentUser();

    const from = startCursor(paginationArgs);
    const to = endCursor(paginationArgs);
    const offset = Number.parseInt(from) || 0;
    const records = Number.parseInt(to) || 5;
    const [provvedimenti, count] =
      await this.elencoProvvedimentiService.getElencoProvvedimenti(
        idUdienza,
        cf,
        paginationArgs,
      );
    const connection = new ProvvedimentiDtoConnection();
    connection.pageInfo = computePageInfo(from, `${offset + records}`, count);
    connection.aggregate = {
      count: provvedimenti.length,
      total: count,
      totalElement: count,
    };
    const start =
      Number.parseInt(
        connection?.pageInfo?.startCursor
          ? connection?.pageInfo?.startCursor
          : '0',
      ) + 1;
    const edges = provvedimenti.map((fe, index) => ({
      cursor: `${start + index}`,
      node: fe,
    }));
    connection.edges = edges;
    return connection;
  }
}
