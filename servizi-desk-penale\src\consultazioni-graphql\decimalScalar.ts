import { GraphQLScalarType } from 'graphql/type';
import { Kind } from 'graphql/language';
import BigNumber from 'bignumber.js';
BigNumber.config({ DECIMAL_PLACES: 2 });

export const DecimalScalar = new GraphQLScalarType({
  name: 'Decimal',
  description: 'A simple UUID parser',
  serialize(value: number) {
    // value sent to the client
    return new BigNumber(value).decimalPlaces(2);
  },
  parseValue(value: string) {
    // value from the client
    if (new BigNumber(value).isNaN()) {
      throw new TypeError(`${String(value)} is not a valid decimal value.`);
    }

    return BigNumber(value);
  },
  parseLiteral(ast: any) {
    if (ast.kind !== Kind.STRING) {
      throw new TypeError(`${ast.value} is not a valid decimal value.`);
    }

    return BigNumber(ast.value);
  },
});
