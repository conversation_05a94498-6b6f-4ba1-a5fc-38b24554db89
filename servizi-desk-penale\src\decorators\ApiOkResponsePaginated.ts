import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { PaginationQueryArgs } from '../relay-pagination/pagination-query.args';

export const ApiOkResponsePaginated = <DataDto extends Type<unknown>>(
  dataDto: DataDto,
  descrizione: string,
) =>
  applyDecorators(
    ApiExtraModels(PaginationQueryArgs, dataDto),
    ApiOkResponse({
      status: 200,
      schema: {
        allOf: [
          { $ref: getSchemaPath(PaginationQueryArgs) },
          {
            properties: {
              listResult: {
                type: 'array',
                items: { $ref: getSchemaPath(dataDto) },
              },
            },
          },
        ],
      },
      description: descrizione,
    }),
  );
