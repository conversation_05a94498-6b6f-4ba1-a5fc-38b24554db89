import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';
import { PenaleRicercaRiunitiEntity } from './penale_v_ricerca_riuniti.entity';
import ColumnDateIsoTransformer from './utility/column-date-iso-transformer';

@Entity('V_RICERCA_PENALE_SENTENZA') //nome vista su schema oracle
export class PenaleVRicercaPenaleSentenzaEntity {
  @Column({ name: 'SENTENZA' })
  sentenza: number;

  @Column({ name: 'ID_ESTENSORE' })
  idEstensore: number;

  @Column({ name: 'ID_SENT' })
  idSent: number;

  @PrimaryColumn({ name: 'ID_RICUDIEN' })
  idRicUdien: number;

  @Column({ 
    name: 'DATAMINUTA',
    transformer: new ColumnDateIsoTransformer()
  })
  dataMinuta: Date;

  @Column({ name: 'DATAPUBBL' })
  dataPubblicazione: Date;

  @Column({ name: 'TIPOSENT' })
  tipoSent: number;
  @Column({ name: 'NRACCG' })
  numeroRaccoltaGenerale: number;
  @OneToOne(() => PenaleRicercaRiunitiEntity)
  @JoinColumn({ name: 'ID_RICUDIEN' })
  riunitiView: PenaleRicercaRiunitiEntity;

  @Column({
    name: 'PUBBLICATO_TELEMATICO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  pubblicatoTelematico: boolean;

  @Column({
    name: 'DEPOSITO_TELEMATICO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  depositoTelematico: boolean;

  @Column({
    name: 'RIUNITO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  riunito: boolean;
}
