import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';
import ColumnDateIsoTransformer from './utility/column-date-iso-transformer';

@Entity('PENALE_T_RICORSO') //nome tabella su schema oracle
export class PenaleTRicorsoEntity {
  @PrimaryColumn({ name: 'NRG' })
  nrg: number;

  @Column({
    name: 'DATAISCR',
    type: 'timestamp with time zone',
    transformer: new ColumnDateIsoTransformer(),
  })
  dataIscrizione: Date;

  @Column({ name: 'STATORIC' })
  statoRicorso: string;

  @Column({ name: 'DETPARTI' })
  detParti: string;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_DOVESTA' })
  doveSta: PenaleParamEntity;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_DOVEFTO' })
  doveFto: PenaleParamEntity;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPORIC' })
  tipoRicorso: PenaleParamEntity;
  @Column({ name: 'CODINAM1' })
  codinam1: number;
  @Column({ name: 'CODINAM2' })
  codinam2: number;
  @Column({ name: 'CODINAM3' })
  codinam3: number;
  @Column({ name: 'NRGPRINC' })
  nrgPrinc: number;
  @Column({ name: 'ID_FUNZ' })
  idFunzionario: number;
  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'URGENTE' })
  urgente: string;
  @Column({ name: 'ID_RINVIO' })
  idRinvio: number;
  @Column({ name: 'NRGREALE' })
  nrgReale: number;
  @Column({ name: 'ID_SEZERR' })
  idSezerr: number;
  @Column({ name: 'CONFISCA' })
  confisca: string;
  @Column({ name: 'MAFIA' })
  mafia: string;
  @Column({ name: 'NOTE' })
  note: string;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_SEZIONE' })
  sezione: PenaleParamEntity;
  @Column({ name: 'VECCHIO_RITO' })
  vecchioRito: string;
  @Column({ name: 'ID_MAGISINAM' })
  idMagisinam: number;
  @Column({ name: 'ID_DOVEINC' })
  idDoveinc: number;
  @Column({ name: 'DATAPASSA' })
  dataPassa: Date;
  @Column({ name: 'DATAPASSA_FTO' })
  dataPassaFto: Date;
  @Column({ name: 'DATAPASSA_INC' })
  dataPassaInc: Date;
  @Column({ name: 'TIPORICOLD' })
  tipoRicorsoOld: string;
  @Column({ name: 'ID_AULO_INC' })
  idAuloInc: number;
  @Column({ name: 'PRIVACY' })
  privacy: number;
  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;
  @Column({ name: 'NUMPARTI' })
  numParti: number;
  @Column({ name: 'FLAG_SITMP' })
  flagSitmp: string;
  @Column({ name: 'DATA_DECORSO_IMP' })
  dataDecorsoImp: Date;
}
