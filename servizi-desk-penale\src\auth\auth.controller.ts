import { Controller, Get, HttpCode, Post, Query } from '@nestjs/common';
import { AuthService } from './auth.service';
import { Role } from './role.enum';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import process from 'process';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { of } from 'rxjs';
import { FirmaRemotaErrorException } from '../exceptions/firma-remota-error.exception';
import { Log } from '../decorators/log.decorator';

@ApiTags('auth')
@ApiBearerAuth('access-token')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('/roles')
  @HttpCode(200)
  @Log()
  async getRole(): Promise<Role[] | null> {
    return await this.authService.getRole();
  }

  @Get('/nomecognomeuser')
  @HttpCode(200)
  @Log()
  async getNomeCognomeUser(): Promise<string | null> {
    return await this.authService.getNomeCognomeUser();
  }

  @Get('/emailuser')
  @HttpCode(200)
  async getUserEmail(): Promise<string | null> {
    return await this.authService.getUserEmail();
  }

  @Get('/cf')
  @HttpCode(200)
  async getCf(): Promise<string | null> {
    return await this.authService.getCf();
  }

  @Get('/checkUser')
  @HttpCode(200)
  async getCheckUser(
    @Query('disabledError') disabledError: string,
  ): Promise<boolean> {
    const disabledErrorBool = disabledError === 'true';
    try {
      return await this.authService.getCheckUser();
    } catch (error) {
      if (error instanceof FirmaRemotaErrorException) {
        if (disabledErrorBool) {
          return false;
        }
      }
      throw error;
    }
  }
}
