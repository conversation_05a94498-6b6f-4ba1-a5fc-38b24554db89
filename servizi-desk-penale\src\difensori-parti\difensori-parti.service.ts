import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleDifenpartiEntity } from '../consultazioni-graphql/entities/penale_difenparti.entity';
import { CivileAnagdifenEntity } from '../consultazioni-graphql/entities/civile_anagdifen.entity';
import { PlainObjectToNewEntityTransformer } from 'typeorm/query-builder/transformer/PlainObjectToNewEntityTransformer';

@UfficioService()
export class DifensoriPartiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  difensori(): Promise<PenaleDifenpartiEntity[]> {
    return this.connection.getRepository(PenaleDifenpartiEntity).find({
      relations: {
        tipoDifensore: true,
      },
    });
  }

  difensore(idDifensore: number): Promise<PenaleDifenpartiEntity | null> {
    return this.connection.getRepository(PenaleDifenpartiEntity).findOne({
      where: { idDifensoriParti: idDifensore },
      relations: {
        tipoDifensore: true,
      },
    });
  }

  async getAnagraficaDifensore(idAnagDifen: number) {
    const [queryString, parameters] =
      this.connection.driver.escapeQueryWithParameters(
        ' select * from CIVILE_ANAGDIFEN@CIVILE_DBLINK.CASSAZIONE.SIC cad WHERE  cad.ID_ANAGDIFEN = :idAnagDifen ',
        { idAnagDifen: idAnagDifen },
        {},
      );
    const query = await this.connection
      .getRepository(CivileAnagdifenEntity)
      .query(queryString, parameters);

    const metadata = this.connection.getMetadata(CivileAnagdifenEntity);
    const transformer = new PlainObjectToNewEntityTransformer();

    const updatedUser: CivileAnagdifenEntity = metadata.create(
      this.connection.createQueryRunner(),
    );
    const anan = query[0];
    const anagraficaModel: any = {};
    metadata.columns.forEach(value => {
      if (value.givenDatabaseName) {
        // console.log(anan[value.givenDatabaseName])
        const pror = value.propertyName;
        anagraficaModel[pror] = anan[value.givenDatabaseName];
      }
    });
    transformer.transform(updatedUser, (await query)[0], metadata);
    return anagraficaModel;
  }

  async difensoreByIdParte(
    idParte: number,
  ): Promise<PenaleDifenpartiEntity[] | null> {
    return this.connection.getRepository(PenaleDifenpartiEntity).find({
      where: { idParti: idParte },
      relations: {
        tipoDifensore: true,
      },
    });
  }
}
