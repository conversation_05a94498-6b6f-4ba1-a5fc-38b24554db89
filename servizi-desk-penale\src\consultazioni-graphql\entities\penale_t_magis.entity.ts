import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_T_MAGIS') //nome tabella su schema oracle
export class PenaleTMagisEntity {
  @PrimaryColumn({ name: 'ID_MAGIS' })
  idMagis: number;

  @Column({ name: 'UFFICIO' })
  ufficio: number;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPOMAG' })
  tipoMag: PenaleParamEntity;
  @Column({ name: 'DATAINIZIO' })
  dataInizio: Date;
  @Column({ name: 'DATAFINE' })
  dataFine: Date;
  @Column({ name: 'GRADO' })
  grado: number;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @Column({ name: 'OPERATORE' })
  operatore: number;

  @Column({ name: 'OG<PERSON>' })
  oggi: Date;
  @Column({ name: 'CODMAG' })
  codMag: number;
  @Column({ name: 'ID_ANAGMAGIS' })
  idAnagmagis: number;

  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;
}
