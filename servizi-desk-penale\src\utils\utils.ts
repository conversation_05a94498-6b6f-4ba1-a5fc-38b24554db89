import * as moment from 'moment';
import { ProvvedimentiTipoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { PenaleVRicercaPenaleSentenzaEntity } from '../consultazioni-graphql/entities/penale_v_ricerca_penale_sentenza.entity';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { Logger } from '@nestjs/common';
import { PenaleUfficioEntity } from '../consultazioni-graphql/entities/penale_ufficio.entity';

export class Utils {
  private static logger = new Logger(Utils.name);
  static readonly listItalianDaysSummer: Array<string> = ['29-06', '24-12'];

  static isOrdinanzaOSentenza(tipo: ProvvedimentiTipoEnum) {
    switch (tipo) {
      case ProvvedimentiTipoEnum.ORDINANZA:
      case ProvvedimentiTipoEnum.SENTENZA:
        return true;
      default:
        return false;
    }
  }
  static isMinuta(tipo: ProvvedimentiTipoEnum) {
    switch (tipo) {
      case ProvvedimentiTipoEnum.MINUTA_SENTENZA:
      case ProvvedimentiTipoEnum.MINUTA_ORDINANZA:
        return true;
      default:
        return false;
    }
  }

  static isStatoDelPresidente(stato: ProvvedimentiStatoEnum) {
    if (stato) {
      switch (stato) {
        case ProvvedimentiStatoEnum.BUSTA_RIFIUTATA:
        case ProvvedimentiStatoEnum.CODA_DI_FIRMA:
        case ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE:
        case ProvvedimentiStatoEnum.ERRORE_DI_PUBBLICAZIONE:
        case ProvvedimentiStatoEnum.PUBBLICATA:
        case ProvvedimentiStatoEnum.MINUTA_MODIFICATA:
        case ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE:
          return true;
        default:
          return false;
      }
    }
    return false;
  }

  static checkStatoCorretto(
    sentenza: PenaleVRicercaPenaleSentenzaEntity | null,
    statoProvvedimento: ProvvedimentiStatoEnum | null,
  ): ProvvedimentiStatoEnum | null {
    if (
      sentenza?.dataPubblicazione != null &&
      !sentenza?.pubblicatoTelematico &&
      sentenza.numeroRaccoltaGenerale != null
    ) {
      return ProvvedimentiStatoEnum.PUBBLICATO_SIC;
    } else if (
      sentenza?.dataPubblicazione != null &&
      !sentenza?.pubblicatoTelematico
    ) {
      return ProvvedimentiStatoEnum.PROVV_DEPOSITATO_SIC;
    } else if (sentenza?.dataMinuta != null && !sentenza?.depositoTelematico) {
      return ProvvedimentiStatoEnum.MINUTA_DEPOSITATA_SIC;
    }
    return statoProvvedimento;
  }
  static isDuplicabile(stato: ProvvedimentiStatoEnum) {
    if (stato) {
      switch (stato) {
        case ProvvedimentiStatoEnum.BUSTA_RIFIUTATA:
        case ProvvedimentiStatoEnum.MINUTA_ACCETTATA:
          return true;
        default:
          return false;
      }
    }
    return false;
  }
  static isVerificabile(stato: ProvvedimentiStatoEnum) {
    if (stato) {
      switch (stato) {
        case ProvvedimentiStatoEnum.BUSTA_RIFIUTATA:
        case ProvvedimentiStatoEnum.MINUTA_ACCETTATA:
          return true;
        default:
          return false;
      }
    }
    return false;
  }
  static isGiornoFerialeAndReturnDateNoFeriale(
    dateMoment: moment.Moment,
  ): Date {
    const dayOfWeek = dateMoment.toDate().getDay();
    let newDAte = dateMoment.toDate();
    if (dayOfWeek === 6) {
      const newDateMoment = dateMoment.add(2, 'd');
      newDAte = newDateMoment.toDate();
    }
    if (dayOfWeek === 6) {
      const newDateMoment = dateMoment.add(1, 'd');
      newDAte = newDateMoment.toDate();
    }
    return this.isHollidayItalianAndReturnNewDate(newDAte);
  }

  static isHollidayItalianAndReturnNewDate(date: Date): Date {
    const dateStringDayMonth = date.getDate() + '-' + date.getMonth();
    if (Utils.listItalianDaysSummer.indexOf(dateStringDayMonth) > -1) {
      const newDateMoment = moment(date).add(1, 'd');
      return this.isGiornoFerialeAndReturnDateNoFeriale(newDateMoment);
    }
    return date;
  }

  /**
   *  Restituisce il numero del fascicolo nel formato anno, numero
   * @param nrgReale
   */
  static calcoloNumeroFascicolo(
    nrgReale: string,
  ): [number, number] | [null, null] {
    if (nrgReale && nrgReale != '') {
      return [
        Number(nrgReale.substring(0, 4)),
        Number(nrgReale.substring(4, 10)),
      ];
    }
    return [null, null];
  }
  static getNumeroFascicolo(nrgReale: string): number | null {
    const numeroFascicolo = this.calcoloNumeroFascicolo(nrgReale);
    return numeroFascicolo[1];
  }
  static getAnnoFascicolo(nrgReale: string): number | null {
    const numeroFascicolo = this.calcoloNumeroFascicolo(nrgReale);
    return numeroFascicolo[0];
  }
  static calcoloNumeroRaccoltaGenerale(
    numRaccG: string,
  ): [number, number] | [null, null] {
    if (numRaccG && numRaccG != '') {
      return [
        Number(numRaccG.substring(0, 4)),
        Number(numRaccG.substring(4, 10)),
      ];
    }
    return [null, null];
  }

  static calcolaIdSezionale(nrgReale: string): [number, number] | [null, null] {
    if (nrgReale) {
      return [
        Number(nrgReale.substring(0, 4)),
        Number(nrgReale.substring(4, 9)),
      ];
    }
    return [null, null];
  }

  static reatoSostituisciData(
    stringaParam: string | null,
    to: Date | null,
    from: Date | null,
  ) {
    let newStringFormatta = '';
    if (stringaParam) {
      const countDataCompleta = (stringaParam.match(/GG\/MM\/AAAA/g) || [])
        .length;
      if (countDataCompleta > 0) {
        if (countDataCompleta === 2) {
          newStringFormatta = stringaParam.replace(
            'GG/MM/AAAA',
            Utils.getData('DD/MM/YYYY', to),
          );
          newStringFormatta = newStringFormatta.replace(
            'GG/MM/AAAA',
            Utils.getData('DD/MM/YYYY', from),
          );
        } else {
          const countFino = (stringaParam.match(/FINO/g) || []).length;
          newStringFormatta = stringaParam.replace(
            'GG/MM/AAAA',
            Utils.getData('DD/MM/YYYY', countFino > 0 ? from : to),
          );
        }
        return newStringFormatta;
      }

      const countDataAnno = (stringaParam.match(/AAAA/g) || []).length;
      if (countDataAnno > 0) {
        if (countDataAnno === 2) {
          newStringFormatta = stringaParam.replace(
            'AAAA',
            Utils.getData('YYYY', to),
          );
          newStringFormatta = newStringFormatta.replace(
            'AAAA',
            Utils.getData('YYYY', from),
          );
        } else {
          newStringFormatta = stringaParam.replace(
            'AAAA',
            Utils.getData('YYYY', to ?? from),
          );
        }
        return newStringFormatta;
      }
    }

    return null;
  }

  static getData(formatData: string, data: Date | null) {
    if (data) {
      return moment(data).format(formatData);
    }
    return '';
  }

  static getStringDate(data: Date | null) {
    if (data) {
      const temp = new Date();
      const pad = (i: number) => (i < 10 ? '0' + i : '' + i);

      return (
        pad(temp.getDate()) +
        '/' +
        pad(1 + temp.getMonth()) +
        '/' +
        temp.getFullYear()
      );
    }
    return '';
  }
  static isBoolean(value: string) {
    if (value && value.trim() !== '') {
      switch (value.toLowerCase()) {
        case '1':
        case 'true':
          return true;
        default:
          return false;
      }
    }
    return false;
  }

  /* riguarda SOLO gli stati del relatore/estensore */
  static isOnlyStatiRelatoreEstensore(
    stato: ProvvedimentiStatoEnum,
    preStato: ProvvedimentiStatoEnum,
  ) {
    switch (stato) {
      case ProvvedimentiStatoEnum.IN_BOZZA:
      case ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL:
      case ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE:
      case ProvvedimentiStatoEnum.ERRORE_DI_PUBBLICAZIONE:
        return true;
      default:
        console.log('potrebbe essere uno stato del relatore');
        break;
    }
    return (
      stato == ProvvedimentiStatoEnum.BUSTA_RIFIUTATA &&
      preStato == ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE
    );
  }

  /* riguarda SOLO gli stati del presidente */
  static isOnlyStatiPresidente(
    stato: ProvvedimentiStatoEnum,
    preStato: ProvvedimentiStatoEnum,
  ) {
    switch (stato) {
      case ProvvedimentiStatoEnum.CODA_DI_FIRMA:
      case ProvvedimentiStatoEnum.ERRORE_DI_PUBBLICAZIONE:
      // TODO inseri bozza presidente
      case ProvvedimentiStatoEnum.BOZZA_PRESIDENTE:
        return true;
      default:
        console.log('potrebbe essere uno stato del presidente');
        break;
    }
    return false;
  }
  /**
   * Controlla se il fasciolo è stato già lavorato. restitusice una eccezione se il provedimento e già stato lavorato
   * @param provv provvedimento da controllare
   * @param checkStato lo stato che deve controllare
   * @param logger il logger per la loggata
   */
  static checkAlreadyWorked(
    provv: PenaleProvvedimentiEntity | null,
    checkStato: ProvvedimentiStatoEnum,
    logger: Logger,
  ) {
    if (!provv || provv?.stato === checkStato) {
      logger.warn(
        `Provvedimento è già stato lavorato lo stato corrente è statoProvv:${provv?.stato}, statoChecked:${checkStato}. idProvv:${provv?.idProvvedimento}`,
      );
      throw new GenericErrorException(
        'Provvedimento gia inviato in cancelleria relatore',
        CodeErrorEnumException.PROVV_GIA_FIRMATO_DEPOSITATO,
      );
    }
  }
  static checkUfficoDB(ufficioDB: PenaleUfficioEntity | null) {
    if (!ufficioDB?.codiceUfficio) {
      this.logger.warn('Ufficio non trovato');
      throw new GenericErrorException(
        'ufficio non trovato',
        CodeErrorEnumException.UFFICIO_NOT_FOUND,
      );
    }
  }

  static getTipoProvvPerDuplicatePerEstensore(tipo: ProvvedimentiTipoEnum) {
    switch (tipo) {
      case ProvvedimentiTipoEnum.ORDINANZA:
        return ProvvedimentiTipoEnum.MINUTA_ORDINANZA;
      case ProvvedimentiTipoEnum.SENTENZA:
        return ProvvedimentiTipoEnum.MINUTA_SENTENZA;
      default:
        return tipo;
    }
  }
  static getTipoProvvPerDuplicatePerPresidente(tipo: ProvvedimentiTipoEnum) {
    switch (tipo) {
      case ProvvedimentiTipoEnum.MINUTA_ORDINANZA:
        return ProvvedimentiTipoEnum.ORDINANZA;
      case ProvvedimentiTipoEnum.MINUTA_SENTENZA:
        return ProvvedimentiTipoEnum.SENTENZA;
      default:
        return tipo;
    }
  }

  static convertMeseToString(meseNumber: number) {
    switch (meseNumber) {
      case 1:
        return 'Gennaio';
      case 2:
        return 'Febbraio';
      case 3:
        return 'Marzo';
      case 4:
        return 'Aprile';
      case 5:
        return 'Maggio';
      case 6:
        return 'Giugno';
      case 7:
        return 'Luglio';
      case 8:
        return 'Agosto';
      case 9:
        return 'Settembre';
      case 10:
        return 'Ottobre';
      case 11:
        return 'Novembre';
      case 12:
        return 'Dicembre';
      default:
        return null;
    }
  }
}
