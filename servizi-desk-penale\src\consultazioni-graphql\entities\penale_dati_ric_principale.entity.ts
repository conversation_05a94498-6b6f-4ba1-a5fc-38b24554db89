import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_DATIRICPRINC') //nome tabella su schema oracle
export class PenaleDatiRicPrincipaleEntity {
  @PrimaryColumn({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'ID_MAGIS' })
  idMagis: Date;
  @Column({ name: 'DATAPROVV' })
  dataProvvedimentoSic: Date;
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPOPROVV' })
  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPORIUNIONE' })
  tipoRiunione: PenaleParamEntity;
  @Column({ name: 'NOTE' })
  note: string;
  @Column({ name: 'OGGI' })
  oggi: Date;
}
