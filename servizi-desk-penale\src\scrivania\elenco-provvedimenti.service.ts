import { Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { RicercaSentenzaService } from '../ricerca-sentenza/ricerca-sentenza.service';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { ProvvedimentiOrigineEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-origine.enum';
import { OscuramentoService } from './oscuramento.service';
import { PenaleElencoProvvEntity } from 'src/consultazioni-graphql/entities/penale_v_elenco_provv.entity';
import { ProvvedimentiService } from '../provvedimenti/provvedimenti.service';
import { RicorsoUdienzaCommonService } from '../ricorso-udienza/ricorso-udienza-common.service';
import { PaginationCustomQueryFilter } from '../consultazioni-graphql/models/dto/generic_paginator';
import { endCursor, startCursor } from '../relay-pagination/pagination-utils';
import { ProvvedimentoRelazioneService } from '../provvedimento-change-status/provvedimento-relazione.service';
import * as moment from 'moment/moment';

@UfficioService()
export class ElencoProvvedimentiService {
  private logger = new Logger(ElencoProvvedimentiService.name);

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
    private readonly oscuramentoService: OscuramentoService,
    private readonly ricorsoUdienzaCommonService: RicorsoUdienzaCommonService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly provvedimentoChangeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
  ) {}

  /**
   * Restituisce i fascicoli relativo ad una udienza per il presidente corrente
   * @param idUdienza - id dell'Udienza
   * @param codiceFiscale - cf dell'utente loggato
   * @param pagination - informazioni su paginazione e filtri da applicare nella ricerca
   */
  async getElencoProvvedimenti(
    idUdienza: number,
    codiceFiscale: string,
    pagination: PaginationCustomQueryFilter,
  ): Promise<[PenaleElencoProvvEntity[], number]> {
    this.logger.log(
      `Richiesta dell'elenco dei provvedimento per il presidente per id udienza. idUdienza:${idUdienza}`,
    );
    const from = startCursor(pagination);
    const to = endCursor(pagination);
    const offset = Number.parseInt(from) || 0;
    const limit = Number.parseInt(to) || 5;
    const whereCondition: any = {
      idUdienza: idUdienza,
      codiceFiscale: codiceFiscale,
    };
    try {
      // Se si filtra per MINUTA_DA_MODIFICARE o per MINUTA_MODIFICATA_PRESIDENTE,
      // vanno recuperati anche i provvedimenti con stato BUSTA_RIFIUTATA e IN_CODE_FIRMA_REL,
      // poichè se sono stati rifiutati all'estensore o sono in coda di firma estensore può significare
      // che lo stato precedente può essere di MINUTA_DA_MODIFICARE o di MINUTA_MODIFICATA_PRESIDENTE,
      // che è lo stato che il presidente deve vedere
      if (
        pagination?.status &&
        (pagination.status === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE ||
          pagination.status ===
            ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE)
      ) {
        whereCondition.statoRicorso = In([
          pagination.status,
          ProvvedimentiStatoEnum.BUSTA_RIFIUTATA,
          ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
          ProvvedimentiStatoEnum.IN_BOZZA,
        ]);

        const provvToCheck = await this.connection
          .getRepository(PenaleElencoProvvEntity)
          .find({
            select: ['idProvv', 'statoRicorso'],
            where: whereCondition,
            order: {
              dataDeposito: 'DESC',
            },
          });

        // Vanno verificati i provvedimenti con stato BUSTA_RIFIUTATA, IN_BOZZA e IN_CODE_FIRMA_REL, se la busta è rifiutata
        // lato estensore o in coda di firma del relatore , va recuperato lo stato precedente (ovvero il primo in ordine
        // cronologico descrescente che deve essere visualizzato dal Presidente)
        const idProvvs = [];
        for (const provv of provvToCheck) {
          if (
            [
              ProvvedimentiStatoEnum.BUSTA_RIFIUTATA,
              ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
              ProvvedimentiStatoEnum.IN_BOZZA,
            ].includes(provv.statoRicorso)
          ) {
            const statoCorretto = await this.checkStato(
              provv.idProvv,
              provv.statoRicorso,
            );
            //Sono da includere solo quelli con lo stato che si sta cercando
            if (statoCorretto === pagination.status) {
              idProvvs.push(provv.idProvv);
            }
          } else {
            idProvvs.push(provv.idProvv);
          }
        }
        const totalCount = idProvvs.length;
        const idProvvToFind = idProvvs.slice(offset, offset + limit);
        if (idProvvToFind.length === 0) {
          return [[], totalCount];
        }

        const provvPage: PenaleElencoProvvEntity[] = await this.connection
          .getRepository(PenaleElencoProvvEntity)
          .find({
            where: { idProvv: In(idProvvToFind) },
            order: { dataDeposito: 'DESC' },
          });
        //Provvedimenti già filtrati in precedenza, lo stato sarà quello che si sta cercando
        //quindi se diverso va cambiato
        provvPage.forEach(provv => {
          if (provv.stato !== pagination.status)
            provv.stato = pagination.status!;
        });

        return [provvPage, totalCount];
      } else {
        if (pagination?.status) {
          whereCondition.statoRicorso = pagination?.status;
        }

        const [provvedimenti, count] = await this.connection
          .getRepository(PenaleElencoProvvEntity)
          .findAndCount({
            where: whereCondition,
            order: {
              dataDeposito: 'DESC',
            },
            skip: offset, // questo è l'offset
            take: limit, // questo è il limit
          });

          
        for (const provv of provvedimenti) {
          //Nei casi seguenti va recuperato lo stato corretto da mostrare
          if (
            [
              ProvvedimentiStatoEnum.BUSTA_RIFIUTATA,
              ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
              ProvvedimentiStatoEnum.IN_BOZZA,
            ].includes(provv.statoRicorso)
          ) {
            const statoCorretto = await this.checkStato(
              provv.idProvv,
              provv.statoRicorso,
            );
            provv.stato = statoCorretto || provv.stato;
          }
        }

        return [provvedimenti, count];
      }
    } catch (e) {
      this.logger.log(
        `Errore nella richiesta dell'elenco dei provvedimento per il presidente per id udienza. idUdienza:${idUdienza}`,
        e,
      );
      throw new InternalServerErrorException('Errore nella query');
    }
  }

  async getOscuramentoDeskCsp(idUdienza: number, nrg: number) {
    const provvedimentoByIdUdienAndNrg =
      await this.oscuramentoService.provvedimentoByIdUdienAndNrg(
        idUdienza,
        nrg,
      );
    if (
      provvedimentoByIdUdienAndNrg &&
      provvedimentoByIdUdienAndNrg?.length > 0
    ) {
      if (
        provvedimentoByIdUdienAndNrg[0].origine ==
        ProvvedimentiOrigineEnum.SYSTEM
      ) {
        const idsProvv = provvedimentoByIdUdienAndNrg.map(
          prov => prov.idProvvedimento,
        );
        return await this.oscuramentoService.isOscuratoEditor(idsProvv);
      }
      // sono nel caso dei file. Devo prendere il valore dal timbripub
      return await this.oscuramentoService.checkOscuramentoType(idUdienza, nrg);
    }
    return false;
  }
  async checkStatoCorrettoPresidente(
    idProvvedimento: string | null | undefined,
    currentStatoProv: ProvvedimentiStatoEnum | null | undefined,
  ) {
    this.logger.debug(
      `checkStatoCorrettoPresidente - idProvv: ${idProvvedimento}, currentStato: ${currentStatoProv}`,
    );

    if (
      idProvvedimento &&
      (currentStatoProv ===
        ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE ||
        currentStatoProv === ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL ||
        currentStatoProv === ProvvedimentiStatoEnum.IN_BOZZA)
    ) {
      const listStati =
        await this.provvedimentoChangeStatusService.changeStatusListByIdProvvAndLast(
          idProvvedimento,
        );

      this.logger.debug(
        `checkStatoCorrettoPresidente - listStati: ${JSON.stringify(
          listStati?.map(s => ({
            stato: s.stato,
            prevStato: s.prevStato,
            dateChange: s.dateChange,
          })),
        )}`,
      );

      if (listStati?.length > 0) {
        const statoCorrent = listStati[0];
        this.logger.debug(
          `checkStatoCorrettoPresidente - statoCorrente: ${statoCorrent.stato}, prevStato: ${statoCorrent.prevStato}`,
        );

        // Controlliamo lo stato corrente dal database, non dalla cronologia
        switch (currentStatoProv) {
          case ProvvedimentiStatoEnum.IN_BOZZA:
            this.logger.debug(
              `checkStatoCorrettoPresidente - caso IN_BOZZA, restituisco prevStato: ${statoCorrent.prevStato}`,
            );
            return statoCorrent.prevStato;
          case ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL:
            return listStati.length > 1
              ? listStati[1].prevStato
              : currentStatoProv;
          case ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE: {
            let stato =
              listStati.length > 1 ? listStati[1].prevStato : currentStatoProv;
            if (stato === ProvvedimentiStatoEnum.IN_BOZZA) {
              stato =
                listStati.length > 2
                  ? listStati[2].prevStato
                  : currentStatoProv;
            }
            return stato;
          }
          default:
            this.logger.debug(
              `checkStatoCorrettoPresidente - caso default, restituisco currentStatoProv: ${currentStatoProv}`,
            );
            return currentStatoProv;
        }
      }
    }
    this.logger.debug(
      `checkStatoCorrettoPresidente - nessuna condizione soddisfatta, restituisco currentStatoProv: ${currentStatoProv}`,
    );
    return currentStatoProv;
  }

  async checkStato(idProvv: string, currentStatoProv: ProvvedimentiStatoEnum) {
    this.logger.debug(`checkStato - idProvv: ${idProvv}`);
    const listStati =
      await this.provvedimentoChangeStatusService.changeStatusListByIdProvvAndLast(
        idProvv,
      );
    if (listStati?.length > 0) {
      const lastStato = listStati[0];
      // Se lo stato precedente è INVIATO_IN_CANCEL_PRESIDENTE significa che lo stato corrente (BUSTA_RIFIUTATA) è corretto
      if (
        lastStato.prevStato ===
        ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE
      ) {
        return currentStatoProv;
      }

      // Se arrivato qui significa che:
      // - esisterà un provvedimento che è stato modificato dal Presidente
      // - o che il Presidente ha fatto richiesta di modifica
      // Occorre cercare per lo storico del fascicolo e non del singolo provvedimento perchè potrebbero esserci
      // rifiuti multipli da parte della cancelleria
      this.logger.debug(
        `Controllo storico degli stati per il fascicolo con ultimo idProvv:${idProvv}`,
      );
      const idsAllProvv =
        await this.provvedimentoRelazioneService.getAllStatusLIFOAndSentenzeOOrdinazeRifiutate(
          idProvv,
        );
      if (idsAllProvv?.length) {
        // elimino tutti gli stati duplicati e quello di partenza e faccio restiture la lista degli stati
        const changeStatusList =
          await this.provvedimentiService.getAllStatusEliminaDuplicatiAndEcludeIdIniziale(
            idsAllProvv,
            idProvv,
          );
        const changeStatusListOrdered = changeStatusList.sort((a, b) =>
          moment(a.dateChange).isAfter(b.dateChange) ? -1 : 1,
        );
        for (const stato of changeStatusListOrdered) {
          if (
            stato.stato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE ||
            stato.stato === ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE
          ) {
            return stato.stato;
          }
        }
      }
    }

    return currentStatoProv;
  }
}
