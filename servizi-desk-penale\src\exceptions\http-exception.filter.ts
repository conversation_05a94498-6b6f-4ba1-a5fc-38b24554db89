import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { NsBaseException } from './ns-base.exception';
import { CodeErrorEnumException } from './code-error-enum.exception';
import { isNumber } from '@nestjs/common/utils/shared.utils';

@Catch(HttpException, NsBaseException)
export class HttpExceptionFilter implements ExceptionFilter {
  private LOG: Logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    this.LOG.error(`HTTP error -> ${exception}`);
    // @ts-ignore
    if (host.getType() === 'graphql') {
      return exception;
    } else {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();
      const status = exception.getStatus() || 500;

      const errorObject: any = {
        statusCode: status,
        timestamp: new Date().toISOString(),
        message: exception.message,
        path: request.url,
      };
      if (exception instanceof NsBaseException) {
        this.LOG.log(CodeErrorEnumException[exception?.errorCode?.valueOf()]);
        errorObject.errorCode =
          CodeErrorEnumException[exception?.errorCode?.valueOf()] ||
          exception.errorCode;
        errorObject.typeError = exception.typeError;
      }
      response.status(status).json(errorObject);
    }
  }
}
@Catch(Error)
export class ErrorFilter implements ExceptionFilter {
  private LOG: Logger = new Logger(ErrorFilter.name);

  catch(exception: Error, host: ArgumentsHost) {
    this.LOG.error(`HTTP error -> ${exception}`);
    // @ts-ignore
    if (host.getType() === 'graphql') {
      return exception;
    } else {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();
      const status = 500;

      response.status(status).json({
        statusCode: status,
        timestamp: new Date().toISOString(),
        message: exception.message,
        path: request.url,
      });
    }
  }
}
