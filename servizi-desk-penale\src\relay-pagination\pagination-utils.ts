import { PageInfo } from 'graphql-relay';
import { PageInfo as PageInfoClass } from './connection-paging';
import { PaginationQueryArgs } from './pagination-query.args';

export const DEFAULT_PAGE_SIZE = '5';

export enum PaginationDirection {
  FORWARD = 'FORWARD',
  BACKWARDS = 'BACKWARDS',
}

export function getPaginationDirection(args: PaginationQueryArgs) {
  return args.first !== undefined && args.first != null
    ? PaginationDirection.FORWARD
    : PaginationDirection.BACKWARDS;
}

export function startCursor(paginationArgs: PaginationQueryArgs) {
  const dir = getPaginationDirection(paginationArgs);
  const cursor =
    dir === PaginationDirection.FORWARD
      ? paginationArgs.after
      : Number.parseInt(paginationArgs.before || DEFAULT_PAGE_SIZE) -
        (paginationArgs.last || 5);
  return cursor ? cursor.toString() : '0';
}

export function endCursor(paginationArgs: PaginationQueryArgs) {
  const dir = getPaginationDirection(paginationArgs);
  const count =
    dir === PaginationDirection.FORWARD
      ? paginationArgs.first
      : paginationArgs.last;
  return count ? count.toString() : DEFAULT_PAGE_SIZE;
}

export function hasNextPage(pageInfo: PageInfo, totalCount: number) {
  const start = Number.parseInt(pageInfo.startCursor || '0');
  return (
    Number.parseInt(pageInfo.endCursor || start + DEFAULT_PAGE_SIZE) <=
    totalCount
  );
}

export function hasPreviousPage(pageInfo: PageInfo) {
  return Number.parseInt(pageInfo.startCursor || '0') > 0;
}

export function computePageInfo(
  start: string,
  end: string,
  count: number,
): PageInfo {
  const pageInfo = new PageInfoClass() as any as PageInfo;

  pageInfo.startCursor = start;
  pageInfo.endCursor = end;

  pageInfo.hasPreviousPage = hasPreviousPage(pageInfo);
  pageInfo.hasNextPage = hasNextPage(pageInfo, count);
  return pageInfo;
}
