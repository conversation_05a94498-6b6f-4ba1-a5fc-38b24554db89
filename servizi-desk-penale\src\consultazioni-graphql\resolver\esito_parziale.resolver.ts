import { Args, Query, Resolver } from '@nestjs/graphql';
import { EsitoParzialeGraph } from '../models/esito_parziale_graph.model';
import { PenaleParamService } from '../../penale-param/penale-param.service';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { EsitiService } from '../../esiti/esiti.service';

@Resolver(() => EsitoParzialeGraph)
export class EsitoParzialeResolver {
  constructor(
    private readonly penaleParamService: PenaleParamService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly esitiService: EsitiService,
  ) {}

  @Query(() => EsitoParzialeGraph, { name: 'esitoParziale' })
  async esitoParziale(
    @Args('ricorsoUdienza') ricorsoUdienza: number,
  ): Promise<EsitoParzialeGraph | null> {
    const esitoParziale = new EsitoParzialeGraph();
    if (ricorsoUdienza) {
      const ricorsoUdienzaByid =
        await this.ricorsoUdienzaService.ricorsoUdienzaByid(ricorsoUdienza);

      const esitoRicUdienza = await this.esitiService.esito(ricorsoUdienza);
      if (ricorsoUdienzaByid && ricorsoUdienzaByid.idMotivorinv) {
        const param = await this.penaleParamService.getParam(
          ricorsoUdienzaByid.idMotivorinv,
        );
        esitoParziale.motivo =
          param && param.descrizione ? param.descrizione : '';
        if (esitoRicUdienza?.rinvioDescrizione) {
          esitoParziale.descrizione = esitoRicUdienza?.rinvioDescrizione;
        }
        return esitoParziale;
      }
      if (esitoRicUdienza?.motivoSosp) {
        esitoParziale.motivo = 'SOSPENSIONE';

        const param =
          await this.penaleParamService.getParamBySiglaPerSospensione(
            esitoRicUdienza.motivoSosp,
          );
        esitoParziale.descrizione =
          param && param.descrizione ? param.descrizione : '';
        return esitoParziale;
      }
    }
    return null;
  }
}
