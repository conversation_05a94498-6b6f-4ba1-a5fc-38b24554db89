import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleTRicorso } from './penale_t_ricorso.model';
import { PenaleProvvedimenti } from './penale_provvedimenti.model';
import { PenaleTMagis } from './penale_t_magis.model';
import { ProvvedimentiTipoEnum } from '../entities/enumaration/provvedimenti-tipo.enum';
import { PenaleTEsito } from './penale_t_esito.model';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { DecimalScalar } from '../decimalScalar';
import {PenaleParam} from "./penale_param.model";

@ObjectType()
export class PenaleTRicudien {
  @Field(type => ID)
  idRicudien: number;
  @Field(type => PenaleTEsito)
  esito?: PenaleTEsito;
  @Field(type => PenaleTRicorso)
  ricorso?: PenaleTRicorso;
  @Field(type => Int)
  idUdienza: number;
  @Field(type => Int)
  anno?: number;
  @Field(type => Int)
  numero?: number;
  @Field(type => Int)
  nrg: number;
  @Field(type => Int)
  idConcl1: number;
  @Field(type => Int)
  idConcl2: number;
  @Field(type => Int)
  idConcl3: number;
  @Field(type => Int)
  idEsito: number;
  @Field(type => Int)
  numOrdine: number;
  @Field(type => Int)
  idSosp: number;
  @Field(type => Int)
  idMagiscolle: number;
  @Field(type => Int)
  idFunzione: number;
  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  operatore: number;
  @Field(type => Int)
  principale?: number;
  @Field(type => String)
  importante: string;
  @Field(type => String)
  urgente: string;
  @Field(type => String)
  art169: string;
  @Field(type => String)
  art161: string;
  @Field(type => String)
  notiPres: string;
  @Field(type => String)
  faxEsito: string;
  // @Field(type => String)
  // statoProvvedimento?: string | null ;
  @Field(type => Int)
  idRelatore: number;
  @Field(type => Int)
  glbDTime: number;
  @Field(type => Int)
  idMotivorinv: number;
  @Field(type => DecimalScalar)
  valPondComplessivo?: number;
  @Field(type => PenaleParam)
  esitoFascicolo: PenaleParam;

  statoProvvedimento?: ProvvedimentiStatoEnum;
  tipologia?: ProvvedimentiTipoEnum;
  oscuratoSicComplessivo?: boolean;
  provvedimento?: PenaleProvvedimenti;
  relatore?: PenaleTMagis;
  estensore?: PenaleTMagis;
  isEstensore?: boolean;
  isRelatore?: boolean;
  isPresidente?: boolean;
  public constructor(init?: Partial<PenaleTRicudien>) {
    Object.assign(this, init);
  }
}
