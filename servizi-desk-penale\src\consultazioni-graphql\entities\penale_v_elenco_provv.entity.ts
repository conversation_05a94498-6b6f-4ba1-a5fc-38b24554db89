import {Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON>, OneToOne, PrimaryColumn} from 'typeorm';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';
import { ProvvedimentiStatoEnum } from './enumaration/provvedimenti-stato.enum';
import {PenaleParamEntity} from "./penale_param.entity";
import {PenaleTRicorsoEntity} from "./penale_t_ricorso.entity";

@Entity('V_PROVVEDIMENTI_RICORSI')
export class PenaleElencoProvvEntity {
  @PrimaryColumn({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'ID_UDIEN' })
  idUdienza: number;

  @Column({ name: 'ID_RICUDIEN' })
  idRicUdien: number;

  @Column({ name: 'STATO_RICORSO', nullable: true })
  statoRicorso: ProvvedimentiStatoEnum;

  @Column({
    name: 'OSCURATO_SIC',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
    nullable: true,
  })
  oscuratoSic: boolean;

  @Column({ name: 'NUMORD' })
  numOrdine: number;

  @Column({ name: 'NRGREALE' })
  nrgReale: number;

  @Column({ name: 'ID_PROVV', nullable: true })
  idProvv: string;

  @Column({ name: 'DATA_DEPOSITO', nullable: true })
  dataDeposito: Date;

  @Column({ name: 'TIPO', nullable: true })
  tipo: string;

  @Column({ name: 'STATO', nullable: true })
  stato: ProvvedimentiStatoEnum;

  @Column({ name: 'MODIFICA_PRESIDENTE', nullable: true })
  modificaPresidente: boolean;

  @Column({ name: 'NOME', nullable: true })
  nome: string;

  @Column({ name: 'COGNOME', nullable: true })
  cognome: string;

  @Column({ name: 'CODICE_FISCALE', nullable: true })
  codiceFiscale: string;

  @Column({
    name: 'OSCURATO',
    type: 'int',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
    nullable: true,
  })
  oscurato: boolean;

}
