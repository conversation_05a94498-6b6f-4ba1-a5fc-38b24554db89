# compiled output
/servizi-desk-penale/dist
/servizi-desk-penale/node_modules
/servizi-desk-penale/.yarn
/servizi-desk-penale/build
/servizi-desk-penale/.gradle
/.gradle
gradle
/distribution/patch/build

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

gradlew
gradlew.bat
/build/
servizi-desk-penale/.yalc/
servizi-desk-penale/yalc.lock
/node_modules/.bin/rimraf
/node_modules/.bin/rimraf.cmd
/node_modules/.bin/rimraf.ps1
/node_modules/balanced-match/.github/FUNDING.yml
/node_modules/balanced-match/index.js
/node_modules/balanced-match/LICENSE.md
/node_modules/balanced-match/package.json
/node_modules/balanced-match/README.md
/node_modules/brace-expansion/index.js
/node_modules/brace-expansion/LICENSE
/node_modules/brace-expansion/package.json
/node_modules/brace-expansion/README.md
/node_modules/concat-map/example/map.js
/node_modules/concat-map/test/map.js
/node_modules/concat-map/.travis.yml
/node_modules/concat-map/index.js
/node_modules/concat-map/LICENSE
/node_modules/concat-map/package.json
/node_modules/concat-map/README.markdown
/node_modules/fs.realpath/index.js
/node_modules/fs.realpath/LICENSE
/node_modules/fs.realpath/old.js
/node_modules/fs.realpath/package.json
/node_modules/fs.realpath/README.md
/node_modules/glob/common.js
/node_modules/glob/glob.js
/node_modules/glob/LICENSE
/node_modules/glob/package.json
/node_modules/glob/README.md
/node_modules/glob/sync.js
/node_modules/inflight/inflight.js
/node_modules/inflight/LICENSE
/node_modules/inflight/package.json
/node_modules/inflight/README.md
/node_modules/inherits/inherits.js
/node_modules/inherits/inherits_browser.js
/node_modules/inherits/LICENSE
/node_modules/inherits/package.json
/node_modules/inherits/README.md
/node_modules/minimatch/LICENSE
/node_modules/minimatch/minimatch.js
/node_modules/minimatch/package.json
/node_modules/minimatch/README.md
/node_modules/once/LICENSE
/node_modules/once/once.js
/node_modules/once/package.json
/node_modules/once/README.md
/node_modules/path-is-absolute/index.js
/node_modules/path-is-absolute/license
/node_modules/path-is-absolute/package.json
/node_modules/path-is-absolute/readme.md
/node_modules/rimraf/bin.js
/node_modules/rimraf/CHANGELOG.md
/node_modules/rimraf/LICENSE
/node_modules/rimraf/package.json
/node_modules/rimraf/README.md
/node_modules/rimraf/rimraf.js
/node_modules/tmp/lib/tmp.js
/node_modules/tmp/CHANGELOG.md
/node_modules/tmp/LICENSE
/node_modules/tmp/package.json
/node_modules/tmp/README.md
/node_modules/wrappy/LICENSE
/node_modules/wrappy/package.json
/node_modules/wrappy/README.md
/node_modules/wrappy/wrappy.js
/node_modules/.package-lock.json
/servizi-desk-penale.iml
.history/


/servizi-desk-penale/indice.html
.vscode/launch.json