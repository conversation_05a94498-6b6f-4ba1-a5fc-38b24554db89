import { CodeDepositoInput } from '../consultazioni-graphql/entities/dto/code-deposito.input';
import { CreateProvvedimentiChangeStatusInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-change-status.input';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { Inject, Logger } from '@nestjs/common';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { DataSource, EntityManager } from 'typeorm';
import { AuthService } from '../auth/auth.service';
import { UfficiDBService } from '../uffici/ufficiDB.service';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { CodeDepositoService } from '../code-deposito/code-deposito.service';
import { ProvvedimentiService } from './provvedimenti.service';
import { ProvvedimentoNotFoundException } from '../exceptions/provvedimento-not-found.exception';

@UfficioService()
export class ProvvedimentiEstensoreService {
  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private authService: AuthService,
    /*  private readonly provvLavorazService: ProvvLavorazioneService,*/
    private readonly ufficiDBService: UfficiDBService,
    private readonly codaDeposito: CodeDepositoService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly provvedimentoChangeStatusService: ProvvedimentoChangeStatusService,
  ) {}

  async createCodeDepositoEstensore(
    addCodaFirma: boolean | undefined,
    idProvv: string,
    entityManager?: EntityManager,
  ) {
    // aggiungo il provvedimento alla coda di deposito per estensore/relatore
    if (addCodaFirma) {
      const provvedimento = await this.provvedimentiService.provvedimentoById(
        idProvv,
      );

      if (provvedimento && provvedimento.idProvvedimento) {
        const cf = await this.authService.getCurrentUser();
        const currentId = await this.authService.getCurrentId();
        const input = new CodeDepositoInput();
        input.idProvv = idProvv;
        input.cf = cf;
        await this.codaDeposito.createCodeDeposito(input);
        const lastChangeStatus =
          await this.provvedimentoChangeStatusService.changeStatusByIdProvvAndLast(
            idProvv,
          );
        const changeStatusValue = new CreateProvvedimentiChangeStatusInput();
        changeStatusValue.idProvvedimento = idProvv;
        changeStatusValue.stato = ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL;
        changeStatusValue.idAutore = currentId;
        // in caso di richiesta di modifica o  busta rifiutata metto il change stato precedente in modo che semplifico la vista sul CSP
        changeStatusValue.prevStato =
          lastChangeStatus?.prevStato || provvedimento.stato;
        const promise =
          await this.provvedimentoChangeStatusService.createProvvedimentoChangeStatus(
            changeStatusValue,
            entityManager,
          );
        this.provvedimentiService.updateStatoProvvedimento(
          idProvv,
          ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
          entityManager,
        );
        return promise;
      }
      Logger.log("Nessun provvedimento trovato per l'idProvv:", idProvv);
      throw new ProvvedimentoNotFoundException('Nessun provvedimento trovato');
    }
  }
}
