import * as dotenv from 'dotenv';
import * as yaml from 'js-yaml';
import { readFileSync } from 'fs';

// Setta le variabili d'ambiente dal file .env relativo al nostro ambiente
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'development'}` });

const loadUffici = (confFile: string) =>
  yaml.load(readFileSync(confFile, 'utf8')) as Record<string, any>;

/**
 * Mapping tra le variabili d'ambiente e la configurazione applicativa
 *
 * @constructor
 */
export const ConfigLoader = () => ({
  app: {
    port: parseInt(process.env.SERVER_PORT || '3000', 10),
    logLevel: process.env.LOG_LEVEL || 'info',
    graphql: {
      debug: process.env.GRAPHQL_DEBUG || false,
      enablePlayground: process.env.GRAPHQL_ENABLE_PLAYGROUND || false,
    },
    depositiUrl: process.env.URL_SERVIZI_DEPOSITI,
    isDebug: process.env.IS_DEBUG,
    enableModheader: process.env.ENABLE_MODHEADER,
    uffici: loadUffici(process.env.YAML_CONFIG_FILENAME || 'uffici.yaml'),
  },
});
