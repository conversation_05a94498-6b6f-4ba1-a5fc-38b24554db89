import { Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { PenaleDeskConteggioScrivaniaEntity } from '../consultazioni-graphql/entities/penale_v_desk_conteggio_scrivania.entity';
import { ScrivaniaDto } from './dto/ScrivaniaDto';
import { Utils } from '../utils/utils';
import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { DataSource } from 'typeorm';

@UfficioService()
export class ScrivaniaService {
  private readonly logger = new Logger(ScrivaniaService.name);

  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  private createUdienzeQuery(
    codiceFiscale: string,
    dataUdienza?: string,
    sezione?: string,
    tipoUdienza?: string,
    collegio?: string,
  ) {
    const queryBuilder = this.connection
      .getRepository(PenaleDeskConteggioScrivaniaEntity)
      .createQueryBuilder()
      .where({ cfPresidente: codiceFiscale })
      .orderBy('dataUd');

    if (sezione) {
      queryBuilder.andWhere({ sezione });
    }
    if (tipoUdienza) {
      queryBuilder.andWhere({ tipoUd: tipoUdienza });
    }
    if (collegio) {
      queryBuilder.andWhere({ aula: collegio });
    }
    if (dataUdienza) {
      queryBuilder.andWhere('TRUNC(dataUd) = TRUNC(:dataUdienza)', {
        dataUdienza: new Date(dataUdienza),
      });
    } else {
      let mesi = '-' + process.env.MESI_QUERY_SCRIVANIA;
      queryBuilder.andWhere(
        'TRUNC(dataUd) > trunc(add_months(sysdate, :mesi))',
        {mesi},
      );
    }

    return queryBuilder;
  }

  private mapToScrivaniaDto(
    entity: PenaleDeskConteggioScrivaniaEntity,
  ): ScrivaniaDto {
    const dto = new ScrivaniaDto();
    dto.dataud = entity.dataUd || null;
    dto.sezione = entity.sezione || null;
    dto.tipo = entity.tipoUd || null;
    dto.aula = entity.aula || null;
    dto.mese = Utils.convertMeseToString(entity.mese);
    dto.dataUdienza = entity.dataUdienza || null;
    dto.idUdienza = entity.idUdienza || null;
    dto.ricorsiTotali = entity.ricorsiTotali || 0;
    dto.minutaAccettata = entity.minutaAccettata || 0;
    dto.minutaAccettataModifica = entity.minutaAccettataModifica || 0;
    dto.richiestaModifica = entity.richiestaModifica || 0;
    dto.bustaRifiutata = entity.bustaRifiutata || 0;
    dto.pubblicati = entity.pubblicati || 0;
    dto.pubblicatiSic = entity.pubblicatiSic || 0;
    dto.provvDepositatiSic = entity.provvDepositatiSic || 0;
    dto.minutePervenuteSic = entity.minutePervenuteSic || 0;
    dto.lavorati = entity.lavorati || 0;
    dto.pubblicatiTotali = entity.totPubblicati || 0;
    dto.bozzaMinutaModificata = entity.minutaModificataPresidente || 0;
    dto.bozzaPresidente = entity.bozzaPresidente || 0;
    dto.firmati = entity.firmati || 0;
    dto.riuniti = entity.riuniti || 0;
    return dto;
  }

  /**
   * Restituisce tutte i fascicoli relativi a una specifica udienza del presidente corrente
   * @param codiceFiscale
   * @param dataUdienza
   * @param sezione
   * @param tipoUdienza
   * @param collegio
   */
  async getUdienze(
    codiceFiscale: string,
    dataUdienza?: string,
    sezione?: string,
    tipoUdienza?: string,
    collegio?: string,
  ): Promise<ScrivaniaDto[]> {
    this.logger.log(`Richieste delle udienze per utente collegato.`);
    try {
      const udienzeAccettate = await this.createUdienzeQuery(
        codiceFiscale,
        dataUdienza,
        sezione,
        tipoUdienza,
        collegio,
      ).getMany();
      return udienzeAccettate.map(this.mapToScrivaniaDto);
    } catch (e) {
      this.logger.error(
        `Errore richieste delle udienze per utente collegato.`,
        e,
      );
      throw new InternalServerErrorException('Errore nella query');
    } finally {
      this.logger.log(`Fine richieste delle udienze per utente collegato.`);
    }
  }
}
