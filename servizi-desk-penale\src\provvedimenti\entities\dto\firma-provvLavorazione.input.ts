import { Field, InputType, Int } from '@nestjs/graphql';
import { ProvvedimentiTipoEnum } from '../../../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class CredenzialiFirmaRemotaInput {
  @ApiProperty()
  @Field(() => String, { nullable: true })
  passwordFirma: string;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  usernameFirma: string;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  pinFirma: string;
}
@InputType()
export class FirmaPresidenteInput {
  @ApiProperty()
  @Field(() => [String])
  daDepositare: string[];
  @ApiProperty()
  @Field(() => CredenzialiFirmaRemotaInput)
  credenziali: CredenzialiFirmaRemotaInput;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  isDecoded: boolean;
}
@InputType()
export class DownloadCodaDepositoInput {
  @ApiProperty()
  @Field(() => [String])
  daScaricare: string[];
}
@InputType()
export class GenerazioneDatiAttoInput {
  @ApiProperty()
  @Field(() => Int, { nullable: true })
  numeroFascicolo?: number;
  @ApiProperty()
  @Field(() => Int, { nullable: true })
  annoFascicolo?: number;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  tipoProvvedimento: string;

  @ApiProperty()
  @Field(() => String, { nullable: true })
  allegatoOscurato = false;
}
@InputType()
export class DatiBustaInput {
  @ApiProperty()
  @Field(() => String, { nullable: true })
  codiceFiscaleMittente?: string;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  codiceUfficioDestinatario?: string;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  ruoloMittente?: string;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  idMsg?: string;
}

@InputType()
export class FirmaProvvLavorazioneInput {
  @ApiProperty()
  @Field(() => Int)
  nrg: number;
  @Field(() => String)
  @ApiProperty()
  idProvvedimento: string;
  @ApiProperty()
  @Field(() => String, { nullable: true })
  codiceUfficio?: string;
  @ApiProperty()
  @Field(() => Boolean)
  firmato: boolean;
  @ApiProperty()
  @Field(() => ProvvedimentiTipoEnum, { nullable: true })
  tipologiaProvvedimento?: ProvvedimentiTipoEnum;

  @ApiProperty()
  @Field(() => CredenzialiFirmaRemotaInput, { nullable: true })
  credenzialiFirmaRemota: CredenzialiFirmaRemotaInput;
  @ApiProperty()
  @Field(() => GenerazioneDatiAttoInput, { nullable: true })
  generazioneDatiAtto: GenerazioneDatiAttoInput;
  @ApiProperty()
  @Field(() => DatiBustaInput, { nullable: true })
  bustaMakerData: DatiBustaInput;
}
