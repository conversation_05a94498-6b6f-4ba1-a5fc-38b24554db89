import { Field, InputType, Int } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class SettingsInput {
  @ApiProperty()
  @Field(() => String, { description: 'Codice fiscale utente' })
  cfUtente: string;

  @ApiProperty()
  @Field(() => String, { description: 'Username firma remota' })
  usernameFirma: string;

  @ApiProperty()
  @Field(() => String, { description: 'Password firma remota' })
  passwordFirma: string;
}
