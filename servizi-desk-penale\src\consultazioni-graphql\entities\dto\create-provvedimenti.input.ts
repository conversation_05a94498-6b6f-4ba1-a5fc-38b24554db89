import { Field, InputType, Int } from '@nestjs/graphql';
import { ProvvedimentiStatoEnum } from '../enumaration/provvedimenti-stato.enum';
import { ProvvedimentiTipoEnum } from '../enumaration/provvedimenti-tipo.enum';
import { ProvvedimentiOrigineEnum } from '../enumaration/provvedimenti-origine.enum';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class CreateProvvedimentiInput {
  @ApiProperty()
  @Field(() => ProvvedimentiStatoEnum, {
    nullable: true,
  })
  stato?: ProvvedimentiStatoEnum;
  @ApiProperty()
  @Field(() => ProvvedimentiTipoEnum)
  tipo: ProvvedimentiTipoEnum;
  @ApiProperty()
  @Field(() => ProvvedimentiOrigineEnum)
  origine?: ProvvedimentiOrigineEnum;
  @ApiProperty()
  @Field(() => Date)
  dataDeposito: Date;
  @ApiProperty()
  @Field(() => String)
  nomeDocumento: string;
  @ApiProperty()
  @Field(() => Int)
  nrg: number;
  @ApiProperty()
  @Field(() => Int)
  idUdienza: number;
  @ApiProperty()
  @Field(() => Boolean)
  addCodeFirma?: boolean = false;
}
