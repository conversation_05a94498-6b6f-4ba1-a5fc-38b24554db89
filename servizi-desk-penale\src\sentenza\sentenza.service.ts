import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleSentenzeRuoloEntity } from '../consultazioni-graphql/entities/penale_sentenze_ruolo.entity';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';

@UfficioService()
export class SentenzaService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  sentenze(): Promise<PenaleTSentenzaEntity[]> {
    return this.connection.getRepository(PenaleTSentenzaEntity).find();
  }

  sentenza(idsent: number): Promise<PenaleTSentenzaEntity | null> {
    return this.connection.getRepository(PenaleTSentenzaEntity).findOne({
      where: { idSent: idsent },
      relations: {
        idTipoSent: true,
      },
    });
  }
}
