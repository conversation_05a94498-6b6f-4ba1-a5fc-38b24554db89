import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleAnagpartiEntity } from '../consultazioni-graphql/entities/penale_anagparti.entity';

@UfficioService()
export class AnagraficaPartiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  anagraficaParti(): Promise<PenaleAnagpartiEntity[]> {
    return this.connection.getRepository(PenaleAnagpartiEntity).find();
  }

  anagraficaPartiByIdAnagraficaParte(
    idParte: number,
  ): Promise<PenaleAnagpartiEntity | null> {
    return this.connection
      .getRepository(PenaleAnagpartiEntity)
      .findOneBy({ idAnagParte: idParte });
  }
}
