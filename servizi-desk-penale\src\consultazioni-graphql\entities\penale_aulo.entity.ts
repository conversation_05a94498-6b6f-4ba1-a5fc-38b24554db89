import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_T_AULO') //nome tabella su schema oracle
export class penaleTAulo {
  @PrimaryColumn({ name: 'ID_AULO' })
  idAulo: number;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'ID_AUTORITA' })
  autorita: PenaleParamEntity;

  @Column({ name: 'LOCALITA' })
  localita: string;
}
