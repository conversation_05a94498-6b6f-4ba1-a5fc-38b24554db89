import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleVerbaleColleggioEntity } from '../consultazioni-graphql/entities/penale_verbale_colleggio.entity';
import { penaleTAulo } from '../consultazioni-graphql/entities/penale_aulo.entity';

@UfficioService()
export class AuloService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  aule(): Promise<penaleTAulo[]> {
    return this.connection.getRepository(penaleTAulo).find();
  }

  aulaByIdAulo(idAulo: number): Promise<penaleTAulo | null> {
    return this.connection.getRepository(penaleTAulo).findOne({
      where: { idAulo: idAulo },
      relations: {
        autorita: true,
      },
    });
  }
}
