import { NotFoundException } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { PenaleTRicorso } from '../models/penale_t_ricorso.model';
import { ProvvedimentoChangeStatusService } from '../../provvedimento-change-status/provvedimento-change-status.service';
import { PenaleProvvChangeStatus } from '../models/penale_provv_change_status.model';
import { CreateProvvedimentiChangeStatusInput } from '../entities/dto/create-provvedimenti-change-status.input';
import { ProvvedimentoRelazioneService } from '../../provvedimento-change-status/provvedimento-relazione.service';
import * as moment from 'moment/moment';
import { PenaleProvvChangeStatusEntity } from '../entities/penale_provv_change_status.entity';
import { ProvvedimentiService } from '../../provvedimenti/provvedimenti.service';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { Role } from '../../auth/role.enum';
import { Utils } from '../../utils/utils';
import { GenericErrorException } from '../../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../../exceptions/code-error-enum.exception';

@Resolver(() => PenaleTRicorso)
export class PenaleProvvChangeStatusResolver {
  constructor(
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
  ) {}

  @Query(() => PenaleProvvChangeStatus, {
    name: 'provvedimentoChangeStatusById',
  })
  async provvedimentoChangeStatusById(
    @Args('id') id: string,
  ): Promise<PenaleProvvChangeStatus> {
    const changeStatusList = await this.changeStatusService.changeStatusById(
      id,
    );
    if (!changeStatusList) {
      throw new NotFoundException(id);
    }
    return changeStatusList;
  }

  @Query(() => [PenaleProvvChangeStatus], {
    name: 'provvedimentoChangeStatus',
  })
  provvedimentoChangeStatus(): Promise<PenaleProvvChangeStatus[]> {
    return this.changeStatusService.changeStatus();
  }

  @Query(() => [PenaleProvvChangeStatus], {
    name: 'provvedimentoTrackingByIdProvvedimento',
  })
  async provvedimentoTrackingByIdProvvedimento(
    @Args('id') id: string,
    @Args('roles') roles: Role,
  ): Promise<PenaleProvvChangeStatus[]> {
    const provv = await this.provvedimentiService.provvedimentoById(id);
    if (!provv?.nrg) {
      throw new NotFoundException('Provvedimento non trovato');
    }
    const changeStatusList =
      await this.changeStatusService.changeStatusByIdProvvedimento(
        id,
        provv?.nrg,
      );
    const otherStatus = await this.allStatusProvvedimentoLIFO(id);
    if (otherStatus) {
      changeStatusList?.push(...otherStatus);
    }
    if (!changeStatusList) {
      throw new NotFoundException(id);
    }
    const penaleProvvChangeStatusEntities = changeStatusList.sort((a, b) =>
      moment(a.dateChange).isBefore(b.dateChange) ? -1 : 1,
    );

    console.log(
      'tracking degli stati per il provv:, e lista degli stati ordinata:, end count:',
      id,
      penaleProvvChangeStatusEntities,
      penaleProvvChangeStatusEntities.length,
    );
    const provvedimentiDaVisualizzare = this.filterStatusByRoles(
      roles,
      penaleProvvChangeStatusEntities,
    );

    // Gestisce isRevisione controllando direttamente il prevStato per identificare le revisioni
    for (const changeX of provvedimentiDaVisualizzare) {
      if (changeX.stato === ProvvedimentiStatoEnum.IN_BOZZA) {
        changeX.isRevisione =
          changeX.prevStato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE ||
          changeX.prevStato === ProvvedimentiStatoEnum.BUSTA_RIFIUTATA ||
          changeX.prevStato ===
            ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE;
      }
    }

    return provvedimentiDaVisualizzare;
  }

  private filterStatusByRoles(
    roles: Role | Role.RELATORE | Role.ESTENSORE,
    penaleProvvChangeStatusEntities: PenaleProvvChangeStatusEntity[],
  ) {
    if (roles === Role.PRESIDENTE) {
      return penaleProvvChangeStatusEntities.filter(
        stat1 =>
          !Utils.isOnlyStatiRelatoreEstensore(stat1.stato, stat1.prevStato),
      );
    } else if (roles === Role.ESTENSORE || roles === Role.RELATORE) {
      return penaleProvvChangeStatusEntities.filter(
        stat2 => !Utils.isOnlyStatiPresidente(stat2.stato, stat2.prevStato),
      );
    } else {
      throw new GenericErrorException(
        'Ruolo non gestito',
        CodeErrorEnumException.ROLE_NOT_MANAGED,
      );
    }
  }

  @Query(() => [PenaleProvvChangeStatus], {
    name: 'provvedimentoChangeStatusByIdProvvedimento',
  })
  async provvedimentoChangeStatusByIdProvvedimento(
    @Args('id') id: string,
    @Args('roles') roles: Role,
  ): Promise<PenaleProvvChangeStatus[]> {
    const provv = await this.provvedimentiService.provvedimentoById(id);
    if (!provv?.nrg) {
      throw new NotFoundException('Provvedimento non trovato');
    }
    const changeStatusList =
      await this.changeStatusService.changeStatusByIdProvvedimento(
        id,
        provv.nrg,
      );
    if (!changeStatusList) {
      throw new NotFoundException(id);
    }
    if (
      changeStatusList.some(
        stat => stat.stato === ProvvedimentiStatoEnum.IN_BOZZA,
      )
    ) {
      const idsProvv = new Array<string>();
      let orgigine =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
          id,
        );
      idsProvv.push(id);
      while (orgigine) {
        idsProvv.push(orgigine.idProvvedimentoOrigine);
        orgigine =
          await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
            orgigine.idProvvedimentoOrigine,
          );
      }
      const allProvvPrecedenti =
        await this.changeStatusService.changeStatusByIdsProvvedimento(
          idsProvv,
          provv.nrg,
        );
      const indexModifica =
        allProvvPrecedenti?.findIndex(
          stat => stat.stato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE,
        ) ?? -1;
      if (allProvvPrecedenti && indexModifica >= 0) {
        for (const changeX of changeStatusList) {
          changeX.isRevisione =
            changeX.stato === ProvvedimentiStatoEnum.IN_BOZZA &&
            moment(changeX.dateChange).isAfter(
              allProvvPrecedenti[indexModifica].dateChange,
            );
        }
      }

      // Gestisce isRevisione controllando direttamente il prevStato per identificare le revisioni
      for (const changeX of changeStatusList) {
        if (changeX.stato === ProvvedimentiStatoEnum.IN_BOZZA) {
          changeX.isRevisione =
            changeX.prevStato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE ||
            changeX.prevStato === ProvvedimentiStatoEnum.BUSTA_RIFIUTATA ||
            changeX.prevStato ===
              ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE;
        }
      }
    }
    return this.filterStatusByRoles(roles, changeStatusList);
  }

  // ricerca degli stato in lifo dall'ultimo provvedimento fino al primo
  async allStatusProvvedimentoLIFO(idProvv: string) {
    // se entro in questo if che almeno una dupplicazione del provvedimento
    console.log('Sto prendeti gli stati precedenti per il provv:', idProvv);
    const idsAllProvv =
      await this.provvedimentoRelazioneService.getAllStatusLIFOAndSentenzeOOrdinazeRifiutate(
        idProvv,
      );
    if (idsAllProvv?.length)
      // elimino tutti gli stati duplicati e quello di partenza e faccio restiture la lista degli stati
      return this.provvedimentiService.getAllStatusEliminaDuplicatiAndEcludeIdIniziale(
        idsAllProvv,
        idProvv,
      );
    return new Array<PenaleProvvChangeStatusEntity>();
  }

  @Mutation(() => PenaleProvvChangeStatus, {
    name: 'createProvvedimentoChangeStatus',
  })
  async createProvvedimento(
    @Args('changeStatus') changeStatus: CreateProvvedimentiChangeStatusInput,
  ): Promise<PenaleProvvChangeStatus | null> {
    console.log(
      'Chiamata al creaProvveidmento change status e inserimento nel change status.',
    );
    const provvedimentoNoteId =
      await this.changeStatusService.createProvvedimentoChangeStatus(
        changeStatus,
      );
    if (provvedimentoNoteId) {
      return await this.changeStatusService.changeStatusById(
        provvedimentoNoteId,
      );
    }
    throw new NotFoundException(
      'Nota sul provvedimento change status non inserito',
    );
  }
}
