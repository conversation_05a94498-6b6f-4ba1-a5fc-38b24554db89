import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleProvvedimentiNote } from './penale_provvedimenti_note.model';
import { ProvvedimentiTipoEnum } from '../entities/enumaration/provvedimenti-tipo.enum';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { PenaleTUtente } from './penale_t_utente.model';
import { ProvvedimentiOrigineEnum } from '../entities/enumaration/provvedimenti-origine.enum';
import { PenaleProvvLavFile } from './penale_provv_lav_file.model';

@ObjectType()
export class PenaleProvvedimenti {
  @Field(type => ID)
  idProvvedimento: string;
  @Field(type => String)
  nomeDocumento?: string;
  @Field(type => Int)
  nrg: number;
  @Field(type => Int)
  idUdienza: number;

  @Field(type => String)
  stato?: ProvvedimentiStatoEnum;
  @Field(type => String)
  tipo?: ProvvedimentiTipoEnum;
  @Field(type => String)
  origine?: ProvvedimentiOrigineEnum;
  @Field(type => Date)
  dataDeposito?: Date;

  @Field(type => Date)
  dataUltimaModifica: Date;
  @Field(type => Date)
  dataDecisione: Date;
  @Field(type => Int)
  idAutore: number;
  note?: PenaleProvvedimentiNote[];
  autore?: PenaleTUtente;

  @Field(type => String)
  fkIdCat?: number;
  @Field(type => Boolean)
  lastModified: boolean;
  @Field(type => Boolean)
  provvedimentoLock: boolean;
  checkDownloadAndSign?: boolean;
  listaFile?: PenaleProvvLavFile;
  disabledCreaNuovoProv?: boolean;
  disabledModificaOrDuplica?: boolean;

  isDuplicato?: boolean;
  hasDuplicato?: boolean;
  hasNote?: boolean;
  @Field(type => Boolean)
  isRevisione?: boolean = false;

  @Field(type => Boolean)
  disabledButton?: boolean;

  @Field(type => Boolean)
  enabledRichiestaDiModificaEVerificato?: boolean;

  public constructor(init?: Partial<PenaleProvvedimenti>) {
    Object.assign(this, init);
  }
}
