import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinT<PERSON>,
  ManyToMany,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';

@Entity('PENALE_ANAGPARTI') //nome tabella su schema oracle
export class PenaleAnagpartiEntity {
  @PrimaryColumn({ name: 'ID_ANAGPARTE' })
  idAnagParte: number;

  @Column({ name: 'COGNOME' })
  cognome: string;

  @Column({ name: 'NOME' })
  nome: string;
  @Column({ name: 'CODFISC' })
  codiceFiscale: string;
  @Column({ name: 'DATANASC' })
  dataNascita: Date;
  @Column({ name: 'LUOGONASC' })
  luogoNascita: string;
  @Column({ name: 'PROVNASC' })
  provinciaNascita: string;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OGGI' })
  oggi: Date;

  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'NAZIONALITA' })
  nazionalita: number;
  @Column({ name: 'GLB_DTIME' })
  glbTime: number;
  @Column({ name: 'CODICEUNIVOCO' })
  codiceUnivoco: string;
}
