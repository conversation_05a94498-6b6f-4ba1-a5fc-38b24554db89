#!/bin/sh

# Ottiene il path assoluto dove si trova il nostro script
SCRIPT=$(readlink -f "$0")
SCRIPTPATH=$(dirname "$SCRIPT")

INSTALL_FOLDER=/opt/servizi-desk-penale
PATCH_INFO_FILE=/opt
TMP_FOLDER=/tmp

today=`date +%Y%m%d%H%M%S`
prettyToday=`date +"%d/%m/%Y %H:%M:%S"`
patchVersion=`cat include/.version`


# echo "Verifico lo stato dei servizi del Desk Cassazione Penale"
# systemctl is-active servizi-deskcp.service  \
#   && echo "Il servizio servizi-deskcp risulta attivo. Fermarlo prima di procedere all'applicazione della patch." \
#   && exit 1
echo "Arresto del servizio servizi-deskcp.service del Portale DESK Cassazione Penale"
systemctl stop  servizi-deskcp.service

BACKUP_FOLDER="$TMP_FOLDER/$(date +"%d-%m-%Y")_backup_conf"
mkdir -p "$BACKUP_FOLDER"

echo "Creazione backup configurazioni in $BACKUP_FOLDER"

cp -i "$INSTALL_FOLDER/uffici.yaml" "$BACKUP_FOLDER"
cp -i "$INSTALL_FOLDER"/.env* "$BACKUP_FOLDER"

echo "Rimozione della versione precedente del prodotto"
rm -rf $INSTALL_FOLDER

echo "Installazione della nuova versione"
cp -r "$SCRIPTPATH/servizi-desk-penale" $INSTALL_FOLDER
chown -R node: $INSTALL_FOLDER


# registrazione dell'applicazione della patch
touch $PATCH_INFO_FILE/patch.log
echo "$prettyToday - $patchVersion" >> $PATCH_INFO_FILE/patch.log

echo "Ripristino dei file di configurazione"
yes | cp -i "$BACKUP_FOLDER"/* $INSTALL_FOLDER/ 2> /dev/null
yes | cp -i "$BACKUP_FOLDER"/.env* $INSTALL_FOLDER/ 2> /dev/null

# echo "Applicazione della patch terminata."
# echo "E' ora possibile procedere al riavvio del servizio servizi-deskcp."


echo "=================================================="
echo "Applicazione della patch terminata."
echo "=================================================="
echo "Avviato il servizio servizi-deskcp.service e controllare nel file di log /var/log/servizi-deskcp.log il corretto avvio del servizio"
systemctl start  servizi-deskcp.service
