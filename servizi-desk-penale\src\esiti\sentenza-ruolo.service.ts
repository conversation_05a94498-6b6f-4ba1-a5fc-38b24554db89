import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleVerbaleColleggioEntity } from '../consultazioni-graphql/entities/penale_verbale_colleggio.entity';
import { PenaleSentenzeRuoloEntity } from '../consultazioni-graphql/entities/penale_sentenze_ruolo.entity';

@UfficioService()
export class SentenzaRuoloService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  sentenze(): Promise<PenaleSentenzeRuoloEntity[]> {
    return this.connection.getRepository(PenaleSentenzeRuoloEntity).find();
  }

  sentenza(idEsito: number): Promise<PenaleSentenzeRuoloEntity | null> {
    return this.connection
      .getRepository(PenaleSentenzeRuoloEntity)
      .findOneBy({ idEsito: idEsito });
  }
}
