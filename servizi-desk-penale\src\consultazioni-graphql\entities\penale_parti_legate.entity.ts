import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { PenaleParamEntity } from './penale_param.entity';

@Entity('PENALE_PARTI_LEGATE') //nome tabella su schema oracle
export class PenalePartiLegateEntity {
  @PrimaryColumn({ name: 'ID_ANAGPARTE' })
  idAnagraficaParte: number;

  @PrimaryColumn({ name: 'ID_PARTE' })
  idParte: number;
  @PrimaryColumn({ name: 'ID_FUNZIONE' })
  idFunzione: number;
  @PrimaryColumn({ name: 'OPERATORE' })
  operatore: number;

  @OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPO_LEGAME' })
  tipoLegame: PenaleParamEntity;
}
