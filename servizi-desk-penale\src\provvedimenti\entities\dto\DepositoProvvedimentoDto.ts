export interface DepositoProvvedimentoDto {
  codiceFiscale: string;
  codiceUfficio: string;
  credenzialiFirmaRemota: CredenzialiFirmaRemota;
  generazioneDatiAtto: GenerazioneDatiAttoDto;
  bustaMakerData: DatiBusta;
}

export interface CredenzialiFirmaRemota {
  passwordFirma: string;
  usernameFirma: string;
  pinFirma: string;
}

export interface GenerazioneDatiAttoDto {
  numeroFascicolo?: number;
  annoFascicolo?: number;
  tipoProvvedimento: string;
}

export interface DatiBusta {
  codiceFiscaleMittente: string;
  codiceUfficioDestinatario: string;
  ruoloMittente: string;
  idMsg: string;
}
