import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { AnagraficaMagistratiService } from './anagrafica-magistrati.service';

@Module({
  imports: [
    AnagraficaMagistratiModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [AnagraficaMagistratiService],
  exports: [AnagraficaMagistratiService],
})
export class AnagraficaMagistratiModule {}
