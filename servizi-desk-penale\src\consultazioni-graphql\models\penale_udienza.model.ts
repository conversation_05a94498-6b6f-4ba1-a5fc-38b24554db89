import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleCollegio } from './penale_t_collegio.model';
import { PenaleTRicudien } from './penale_t_ricudien.model';
import { PenaleTUdienzaEntity } from '../entities/penale_t_udienza.entity';

@ObjectType()
export class PenaleUdienza {
  @Field(type => ID)
  idUdienza: number;

  @Field(type => Date)
  dataUdienza: Date;

  @Field(type => String)
  tipoUdienza: string;

  @Field(type => String)
  sezione: string;

  @Field(type => String)
  aula?: string;

  @Field(type => Int)
  operatore: number;

  @Field(type => Int)
  idFunzione: number;

  @Field(type => Date)
  oggi: Date;

  @Field(type => String)
  notePg?: string;

  @Field(type => Date)
  inizioUdienza?: Date;

  @Field(type => Date)
  fineUdienza?: Date;
}
