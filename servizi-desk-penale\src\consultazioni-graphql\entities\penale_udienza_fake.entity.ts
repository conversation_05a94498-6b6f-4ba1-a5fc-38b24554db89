import { Column, Entity, PrimaryColumn } from 'typeorm';
import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import ColumnDateIsoTransformer from './utility/column-date-iso-transformer';

@Entity() //nome tabella su schema oracle
@ObjectType()
export class PenaleTUdienzaEntityFake {
  @Field(type => ID)
  @PrimaryColumn({ name: 'IDUDIEN' })
  idUdien: number;
  @Field(type => Int)
  @Column({ name: 'NRGREALE' })
  nrgReale: number;

  @Field(type => Int)
  @Column({ name: 'FK_IDCAT' })
  idTipoDiUdienza: number;

  @Field(type => Date)
  @Column({
    name: 'DATAUD',
    type: 'timestamp with time zone',
    transformer: new ColumnDateIsoTransformer(),
  })
  dataUdienza: Date;
  @Field(type => Date)
  @Column({ name: 'DATA_DEPOSITO' })
  dataDeposito: Date;
  @Field(type => Date)
  @Column({ name: 'DATASCADENZA' })
  dataScadenza: Date;
  @Field(type => String)
  @Column({ name: 'STATOPROV' })
  statoProvvedimento: string;

  @Field(type => String)
  @Column({ name: 'IDPROV' })
  idProvvvedimento: string;

  /*@OneToOne(() => PenaleParamEntity)
  @JoinColumn({ name: 'TIPOUD' })
  tipoUdienza?: PenaleParamEntity;*/

  @Field(type => String)
  @Column({ name: 'SEZIONE' })
  sezione: string;

  @Field(type => String)
  @Column({ name: 'AULA' })
  aula: string;

  @Field(type => Int)
  @Column({ name: 'NUMCHIAMATA' })
  numeroChiamata: number;

  @Field(type => Int)
  @Column({ name: 'NRG' })
  nrg: number;
  @Field(type => Int)
  @Column({ name: 'NUMERO' })
  numero: number;
  @Field(type => String)
  @Column({ name: 'STATOPROVVLAV' })
  statoProvvedimentoLavorazione: string;
}
