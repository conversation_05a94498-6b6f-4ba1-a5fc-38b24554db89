import { Inject } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTSpoglioEntity } from '../consultazioni-graphql/entities/penale_t_spoglio.entity';

@UfficioService()
export class SpoglioService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  spogli(): Promise<PenaleTSpoglioEntity[]> {
    return this.connection.getRepository(PenaleTSpoglioEntity).find();
  }

  spoglio(idSpoglio: number): Promise<PenaleTSpoglioEntity | null> {
    return this.connection
      .getRepository(PenaleTSpoglioEntity)
      .findOneBy({ idSpoglio: idSpoglio });
  }

  async spoglioFindByNrg(nrg: number): Promise<PenaleTSpoglioEntity | null> {
    return this.connection
      .getRepository(PenaleTSpoglioEntity)
      .findOneBy({ nrg: nrg });
  }
  async spoglioFindByNrgs(
    nrgs: Array<number>,
  ): Promise<PenaleTSpoglioEntity[]> {
    return this.connection
      .getRepository(PenaleTSpoglioEntity)
      .find({ where: { nrg: In(nrgs) } });
  }
}
