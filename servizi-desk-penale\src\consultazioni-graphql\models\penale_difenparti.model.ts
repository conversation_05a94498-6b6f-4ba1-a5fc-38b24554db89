import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleTParti } from './penale_t_parti.model';
import { CivileAnagdifen } from './civile_anagdifen.model';
@ObjectType()
export class PenaleDifenparti {
  public constructor(init?: Partial<PenaleDifenparti>) {
    Object.assign(this, init);
  }
  @Field(type => ID)
  idDifensoriParti: number;

  @Field(type => Int)
  idAnagraficaDifensore: number;
  @Field(type => Int)
  idParti: number;
  @Field(type => Int)
  nrg: number;
  @Field(type => Int)
  idFunzione: number;
  @Field(type => Int)
  operatore: number;
  @Field(type => Date)
  oggi?: Date;
  @Field(type => String)
  comune?: string;
  @Field(type => String)
  indirizzo?: string;
  @Field(type => String)
  cap?: string;
  @Field(type => String)
  provincia?: string;
  tipoDifensore?: PenaleParam;
  @Field(type => Date)
  dataAnomina?: Date;
  @Field(type => Date)
  dataRevoca?: Date;
  @Field(type => Date)
  dataRinunzia?: Date;
  @Field(type => String)
  avviso?: string;
  @Field(type => String)
  esito?: string;
  @Field(type => String)
  merito?: string;

  @Field(type => Int)
  gruppo?: number;
  @Field(type => Int)
  deceduto?: number;
  parte?: PenaleTParti;
  difensoreAnagrafica?: CivileAnagdifen;
}
