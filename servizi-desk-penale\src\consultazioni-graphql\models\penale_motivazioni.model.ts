import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class PenaleMotivazioniModel {
  @Field(() => Int)
  idMotivazione: number;

  @Field(() => Int)
  idRicudien: number;

  @Field()
  proposta: string;

  @Field()
  partiContro: string;

  @Field()
  ricorrenti: string;

  @Field()
  nonRicorrenti: string;

  @Field()
  provvedimento: string;

  @Field()
  atti: string;

  @Field()
  introduzione: string;

  @Field()
  motivazioni: string;

  @Field()
  finale: string;

  @Field()
  testoPqm: string;

  @Field(() => Int)
  operatore: number;

  @Field()
  dataInserimento: Date;

  @Field()
  dataAggiornamento: Date;

  @Field({ nullable: true })
  noteDifensori?: string;
}
