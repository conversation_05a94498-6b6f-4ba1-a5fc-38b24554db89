import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleAnagpartiEntity } from '../consultazioni-graphql/entities/penale_anagparti.entity';
import { PenaleTReati } from '../consultazioni-graphql/models/penale_t_reati.model';
import { PenaleTReatiEntity } from '../consultazioni-graphql/entities/penale_t_reati.entity';

@UfficioService()
export class ReatiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  reati(): Promise<PenaleTReatiEntity[]> {
    return this.connection.getRepository(PenaleTReatiEntity).find();
  }

  reato(idReato: number): Promise<PenaleTReatiEntity | null> {
    return this.connection
      .getRepository(PenaleTReatiEntity)
      .findOneBy({ idReato: idReato });
  }
}
