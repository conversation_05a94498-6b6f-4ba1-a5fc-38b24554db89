import { Module } from '@nestjs/common';
import { MultiTenantModule } from 'src/multi-tenant/multi-tenant.module';
import { TRicorsoService } from './t-ricorso.service';
import { DatiRicorsoPrincipaliService } from './dati-ricorso-principali.service';

@Module({
  imports: [
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [TRicorsoService, DatiRicorsoPrincipaliService],
  exports: [TRicorsoService, DatiRicorsoPrincipaliService],
})
export class FascicoloModule {}
