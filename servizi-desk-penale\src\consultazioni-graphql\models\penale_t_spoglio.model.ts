import { Field, Float, ID, Int, ObjectType } from '@nestjs/graphql';
import { Double } from 'typeorm';
import { DecimalScalar } from '../decimalScalar';

@ObjectType()
export class PenaleTSpoglio {
  @Field(type => ID)
  idSpoglio: number;

  @Field(type => Date)
  dataPrescrizione?: Date;
  @Field(type => Date)
  dataDecterm?: Date;
  @Field(type => Date)
  dataSosp?: Date;
  @Field(type => Int)
  idMotivo?: number;
  @Field(type => String)
  art127: string;
  @Field(type => String)
  art444: string;
  @Field(type => String)
  art438: string;
  @Field(type => String)
  art611: string;
  @Field(type => DecimalScalar)
  valPond?: number;
  @Field(type => Int)
  modello?: number;
  @Field(type => Int)
  idTipoudPrev: number;
  @Field(type => Int)
  nrg: number;
  @Field(type => String)
  presidi?: string;
  @Field(type => Int)
  idSpogliatore?: number;
  @Field(type => Int)
  idFunzione?: number;
  @Field(type => Date)
  oggi?: Date;
  @Field(type => Int)
  operatore?: number;
  @Field(type => String)
  commento?: string;
  @Field(type => Int)
  pgCustodia?: number;
  @Field(type => Int)
  pgSuper?: number;
  @Field(type => String)
  vecchiRito?: string;
  @Field(type => Int)
  codiceConf?: number;

  @Field(type => String)
  cognomePmce?: string;
  @Field(type => String)
  nomePmce?: string;
  @Field(type => Int)
  idAuloPmce?: number;
  @Field(type => String)
  cognomePmcc?: string;
  @Field(type => String)
  nomePmcc?: string;
  @Field(type => Int)
  idAuloPmcc?: number;

  @Field(type => String)
  note?: string;

  @Field(type => Date)
  dataPrescriz2?: Date;

  @Field(type => Date)
  dataPassaPg?: Date;

  @Field(type => String)
  urgentePg?: string;
  @Field(type => Int)
  glbDtime?: number;
  @Field(type => String)
  art12?: string;
  @Field(type => String)
  mae?: string;
  @Field(type => Int)
  privacy?: number;
  @Field(type => Date)
  dataDecreto?: Date;
  @Field(type => String)
  deplano?: string;
}
