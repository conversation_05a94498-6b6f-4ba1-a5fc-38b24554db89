import { ArgsType, Field, Int } from '@nestjs/graphql';
import {ProvvedimentiStatoEnum} from "../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum";

/**
 * Parametri di paginazione per i campi di tipo Connection
 */
@ArgsType()
export class PaginationQueryBaseArgs {
  /*
   * Forward pagination:
   * Restituisce i primi `first` risultati
   * dopo il record identificato da `after`
   */
  @Field(() => Int)
  first?: number;
  @Field()
  after?: string;

  /*
   * Backward pagination:
   * Restituisce i primi `last` risultati
   * prima del record identificato da `before`
   */
  @Field(() => Int)
  last?: number;
  @Field()
  before?: string;

}
@ArgsType()
export class PaginationQueryArgs extends PaginationQueryBaseArgs{
  @Field(() => Boolean)
  read?: boolean;

  @Field(() => String)
  term?: string;
}
