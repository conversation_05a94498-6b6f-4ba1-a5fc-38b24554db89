import { Lo<PERSON>, Modu<PERSON>} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { GraphQLError, GraphQLFormattedError } from 'graphql';
import { join } from 'path';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { EsitiModule } from 'src/esiti/esiti.module';
import { PenaleUdienzaModule } from 'src/penale-udienza/penale-udienza.module';
import { PenaleUdienzaResolver } from './resolver/penaleUdienza.resolver';
import { ScrivaniaModule } from 'src/scrivania/scrivania.module';
import { PenaleTRicorsoResolver } from './resolver/penaleTRicorso.resolver';
import { FascicoloModule } from 'src/fascicolo/fascicolo.module';
import { CodeDepositoModule } from 'src/code-deposito/code-deposito.module';
import { CodeDepositoResolver } from './resolver/codeDeposito.resolver';
import { UdienzaModule } from '../udienza/udienza.module';
import { PenaleTUdienzaResolver } from './resolver/penaleTUdienza.resolver';
import { PenaleTRicorsoUdienzaResolver } from './resolver/penaleTRicorsoUdienza.resolver';
import { RicorsoUdienzaModule } from '../ricorso-udienza/ricorso-udienza.module';
import { PartiModule } from '../parti/parti.module';
import { PenaleTPartiResolver } from './resolver/penaleTParti.resolver';
import { PenaleCollegioResolver } from './resolver/penaleCollegio.resolver';
import { CollegioModule } from '../collegio/collegio.module';
import { PenaleAnagmagisResolver } from './resolver/penaleAnagMagis.resolver';
import { PenaleTMagisResolver } from './resolver/penaleTMagis.resolver';
import { AnagraficaMagistratiModule } from '../anagrafica-magistrati/anagrafica-magistrati.module';
import { MagistratiModule } from '../magistrati/magistrati.module';
import { AnagraficaPartiModule } from '../anagrafica-parti/anagrafica-parti.module';
import { PenaleAnagraficaPartiResolver } from './resolver/penaleAnagraficaParti.resolver';
import { PenaleReatiRicorsoResolver } from './resolver/penaleReatiRicorso.resolver';
import { PenaleReatiResolver } from './resolver/penaleReati.resolver';
import { ReatiModule } from '../reati/reati.module';
import { ReatiRicorsoModule } from '../reati-ricorso/reati-ricorso.module';
import { ImpostazioniModule } from '../impostazioni/impostazioni.module';
import { ImpostazioniResolver } from './resolver/settings.resolver';
import { SpoglioModule } from '../spoglio/spoglio.module';
import { PenaleTSpoglioResolver } from './resolver/penaleTSpoglio.resolver';
import { DifensoriPartiModule } from '../difensori-parti/difensori-parti.module';
import { PenaleDefensorePartiResolver } from './resolver/penaleDefensoreParti.resolver';
import { ProvvedimentiModule } from '../provvedimenti/provvedimenti.module';
import { ProvvedimentiNoteModule } from '../provvedimenti-note/provvedimenti-note.module';
import { PenaleProvvedimentoResolver } from './resolver/penaleProvvedimento.resolver';
import { PenaleProvvedimentoNoteResolver } from './resolver/penaleProvvedimentoNote.resolver';
import { PenaleNotificheConnectionResolver } from './resolver/penaleNotificheConnectionResolver';
import { NotificheModule } from '../notifiche/notifiche.module';
import { ProvvedimentoChangeStatusModule } from '../provvedimento-change-status/provvedimento-change-status.module';
import { PenaleProvvChangeStatusResolver } from './resolver/penaleProvvChangeStatus.resolver';
import { PenaleUtenteResolver } from './resolver/penaleUtente.resolver';
import { PenaleProfilo } from './models/penale_profilo.model';
import { UtenteModule } from '../utente/utente.module';
import { ProfiloModule } from '../profilo/profilo.module';
import { AuthModule } from '../auth/auth.module';
import { UfficiModule } from '../uffici/uffici.module';
import { ControPartiModule } from '../contro-parti/contro-parti.module';
import { PartiLegateModule } from '../parti-legate/parti-legate.module';
import { PenalePartiLegateResolver } from './resolver/penalePartiLegate.resolver';
import { ProvvimpugnatiModule } from '../provvImpugnati/provvimpugnati.module';
import { PenaleTProvvedResolver } from './resolver/penaleTProvved.resolver';
import { VerbaliColleggioModule } from '../verbali-colleggio/verbali-colleggio.module';
import { PenaleVerbaliCollegioResolver } from './resolver/penaleVerbaliCollegio.resolver';
import { AuloModule } from '../aulo/aulo.module';
import { DecimalScalar } from './decimalScalar';
import { TemplateTextAreaModule } from '../template-text-area/template-text-area.module';
import { unwrapResolverError } from '@apollo/server/errors';
import { NsBaseException } from '../exceptions/ns-base.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { PenaleParamModule } from '../penale-param/penale-param.module';
import { EsitoParzialeResolver } from './resolver/esito_parziale.resolver';
import { PenaleTEstensoreSentenzaResolver } from './resolver/penaleTEstensoreSentenza.resolver';
import { EstensoreSentenzaModule } from '../estensore-sentenza/estensore-sentenza.module';
import { SentenzaModule } from '../sentenza/sentenzaModule';
import { PenaleRicorsoDetailsResolver } from './resolver/penaleRicorsoDetails.resolver';
import { CspbackendTimbripubbModule } from 'src/atti/cspbackend-timbripubb.module';
import { RicercaSentenzaModule } from '../ricerca-sentenza/ricerca-sentenza.module';
import { RiunitiModule } from '../riuniti/riuniti.module';
import { PenaleRiunitoDetailsResolver } from './resolver/penaleRiunitoDetails.resolver';
import { VersionModule } from 'src/version/version.module';
import { PenaleMotivazioniModule } from 'src/penale-motivazioni/penale-motivazioni.module';
import {PenaleScrivenerResolver} from "./resolver/penaleScrivener.resolver";
@Module({
  imports: [
    ImpostazioniModule,
    EsitiModule,
    PenaleUdienzaModule,
    ScrivaniaModule,
    FascicoloModule,
    CodeDepositoModule,
    AuthModule,
    UdienzaModule,
    RicorsoUdienzaModule,
    PartiModule,
    CollegioModule,
    AnagraficaMagistratiModule,
    MagistratiModule,
    AnagraficaPartiModule,
    ReatiModule,
    ProvvimpugnatiModule,
    ReatiRicorsoModule,
    SpoglioModule,
    DifensoriPartiModule,
    ProvvedimentiModule,
    ProvvedimentiNoteModule,
    NotificheModule,
    UtenteModule,
    AuloModule,
    ProfiloModule,
    UfficiModule,
    PartiLegateModule,
    ControPartiModule,
    TemplateTextAreaModule,
    VerbaliColleggioModule,
    ProvvedimentiModule,
    ProvvedimentoChangeStatusModule,
    PenaleParamModule,
    CspbackendTimbripubbModule,
    SentenzaModule,
    EstensoreSentenzaModule,
    RicercaSentenzaModule,
    RiunitiModule,
    VersionModule,
    PenaleMotivazioniModule,
    ScrivaniaModule,
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      imports: [ConfigModule],
      inject: [ConfigService],
      driver: ApolloDriver,
      useFactory: (config: ConfigService, logger: Logger) => ({
        debug: config.get('app.graphql.debug'),
        uploads: false,
        tracing: config.get('app.graphql.debug'),
        playground: config.get('app.graphql.enablePlayground'),
        // code first
        autoSchemaFile: join(process.cwd(), 'src/schema.graphql'),
        //schema first
        // typePaths: ['./**/*.graphql'],
        // definitions: {
        //   path: join(process.cwd(), 'schemaoutput.graphql'),
        //   outputAs: 'class',
        //   watch: true,
        // },
        sortSchema: true,
        mockEntireSchema: true,
        resolvers: { Decimal: DecimalScalar },
        formatError: (
          formattedError: GraphQLFormattedError,
          error: GraphQLError,
        ) => {
          if (unwrapResolverError(error) instanceof NsBaseException) {
            const nsExc: NsBaseException = unwrapResolverError(
              error,
            ) as NsBaseException;
            return {
              message: error.message,
              typeError: nsExc?.typeError,
              errorCode:
                CodeErrorEnumException[nsExc?.errorCode?.valueOf()] ||
                nsExc.errorCode,
              statusError: nsExc.getStatus(),
            };
          }

          //logger.log('restituiamo errore GraphQl non gestito')
          return { message: error.message };
        },
        context: ({ req }: { req: any }) => {
          return { headers: req.headers };
        },
      }),
    }),
  ],
  providers: [
    ImpostazioniResolver,
    PenaleUdienzaResolver,
    CodeDepositoResolver,
    PenaleTRicorsoResolver,
    PenaleTUdienzaResolver,
    PenaleTProvvedResolver,
    PenaleTRicorsoUdienzaResolver,
    PenaleTPartiResolver,
    PenaleCollegioResolver,
    PenaleAnagmagisResolver,
    PenaleTMagisResolver,
    PenaleAnagraficaPartiResolver,
    PenaleReatiRicorsoResolver,
    PenaleReatiResolver,
    PenaleTSpoglioResolver,
    PenaleDefensorePartiResolver,
    PenaleProvvedimentoResolver,
    PenaleProvvedimentoNoteResolver,
    PenaleNotificheConnectionResolver,
    PenaleProvvChangeStatusResolver,
    PenaleUtenteResolver,
    PenaleProfilo,
    PenaleVerbaliCollegioResolver,
    PenalePartiLegateResolver,
    PenaleTEstensoreSentenzaResolver,
    EsitoParzialeResolver,
    PenaleRicorsoDetailsResolver,
    PenaleRiunitoDetailsResolver,
    PenaleScrivenerResolver,
  ],
})
export class ConsultazioniGraphqlModule {}
