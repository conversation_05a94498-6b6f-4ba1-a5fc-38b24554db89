import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleTReati } from './penale_t_reati.model';
import { PenaleTRicorso } from './penale_t_ricorso.model';
import { PenaleParamEntity } from '../entities/penale_param.entity';
import { PenaleParam } from './penale_param.model';

@ObjectType()
export class PenaleReatiRicorso {
  @Field(type => ID)
  idReatiRicorsi: number;
  @Field(type => Int)
  nrg: number;
  @Field(type => Int)
  idReato?: number;
  @Field(type => String)
  tipoData?: string;

  @Field(type => Int)
  istProc?: number;

  @Field(type => Date)
  oggi: Date;

  @Field(type => Int)
  idFunzione: number;
  @Field(type => Int)
  operatore: number;

  @Field(type => Boolean)
  principale: boolean;
  @Field(type => String)
  note?: string;
  @Field(type => Int)
  glbDtime?: number;
  tipoD?: PenaleParam;
  @Field(type => Date)
  dataDa?: Date;
  @Field(type => Date)
  dataA?: Date;
  ricorso?: PenaleTRicorso;
  reato?: PenaleTReati;
}
