import {
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { ProvvedimentiService } from '../../provvedimenti/provvedimenti.service';
import { ProvvedimentiNoteService } from '../../provvedimenti-note/provvedimenti-note.service';
import { PenaleProvvedimenti } from '../models/penale_provvedimenti.model';
import { PenaleProvvedimentiNote } from '../models/penale_provvedimenti_note.model';
import { CreateProvvedimentiInput } from '../entities/dto/create-provvedimenti.input';
import { PenaleTUtente } from '../models/penale_t_utente.model';
import { UtenteService } from '../../utente/utente.service';
import { CreateProvvLavorazioneInput } from '../entities/dto/create-provvLavorazione.input';
import { ProvvLavorazioneService } from '../../provvedimenti/provvLavorazione.service';
import { FirmaProvvLavorazioneInput } from '../../provvedimenti/entities/dto/firma-provvLavorazione.input';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';
import { ProvvedimentoChangeStatusService } from '../../provvedimento-change-status/provvedimento-change-status.service';
import { CreateProvvedimentiChangeStatusInput } from '../entities/dto/create-provvedimenti-change-status.input';
import { AuthService } from '../../auth/auth.service';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { PenaleProvvLavFile } from '../models/penale_provv_lav_file.model';
import { ProvvedimentoRelazioneService } from '../../provvedimento-change-status/provvedimento-relazione.service';
import { Utils } from '../../utils/utils';
import { CodeErrorEnumException } from '../../exceptions/code-error-enum.exception';
import { GenericErrorException } from '../../exceptions/generic-error.exception';
import { ProvvedimentoNotDuplicatedException } from '../../exceptions/provvedimento-not-duplicated.exception';
import { CodeDepositoService } from 'src/code-deposito/code-deposito.service';
import { ProvvedimentiEstensoreService } from '../../provvedimenti/provvedimenti-estensore.service';
import { ProvvedimentoFindService } from '../../provvedimenti/provvedimento-find.service';
import { PenaleProvvChangeStatus } from '../models/penale_provv_change_status.model';
import { ProvvEditorLavorazioneService } from '../../provvedimenti/provvEditorLavorazione.service';

@Resolver(() => PenaleProvvedimenti)
export class PenaleProvvedimentoResolver {
  private logger = new Logger(PenaleProvvedimentoResolver.name);

  constructor(
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly utenteService: UtenteService,
    private readonly authService: AuthService,
    private readonly provvLavorazService: ProvvLavorazioneService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
    private readonly provvedimentiNoteService: ProvvedimentiNoteService,
    private readonly codaDepositoService: CodeDepositoService,
    private readonly provvedimentiEstensoreService: ProvvedimentiEstensoreService,
    private readonly provvedimentoFindService: ProvvedimentoFindService,
    private readonly provvedimentoChangeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvEditorLavorazioneService: ProvvEditorLavorazioneService,
  ) {}
  @Query(() => PenaleProvvedimenti, { name: 'provvedimento' })
  async collegio(@Args('id') id: string): Promise<PenaleProvvedimenti> {
    this.logger.log(`Ricerca Provvedimento. idProvv:${id}`);
    const provvedimento = await this.provvedimentiService.provvedimentoById(id);
    if (!provvedimento) {
      this.logger.warn(`Provvedimento non trovato. idProvv:${id}`);
      throw new NotFoundException('Provvedimento non trovato');
    }
    return provvedimento;
  }

  @Query(() => [PenaleProvvedimenti], { name: 'provvedimentiByIds' })
  async provvedimentiByIds(
    @Args('ids', { type: () => [String] }) ids: string[],
  ): Promise<PenaleProvvedimenti[]> {
    this.logger.log(`Ricerca Provvedimenti per ids: ${ids.join(', ')}`);

    const provvedimenti = await Promise.all(
      ids.map(async id => {
        const provvedimento = await this.provvedimentiService.provvedimentoById(
          id,
        );
        if (provvedimento) {
          const checkedProvvedimento =
            await this.provvedimentoFindService.checkShowButtonVerificaAndModificaNew(
              provvedimento,
              true, // includeStatoCorrection = true per mantenere la logica del resolver
            );
          return new PenaleProvvedimenti({ ...checkedProvvedimento });
        }
        return null;
      }),
    );

    return provvedimenti.filter((p): p is PenaleProvvedimenti => p !== null);
  }

  @Query(() => [PenaleProvvedimenti], { name: 'provvedimentoByIdUdien' })
  async provvedimentoByIdUdien(
    @Args('id') id: number,
  ): Promise<PenaleProvvedimenti[]> {
    this.logger.log(`Ricerca Provvedimento. idUdienza:${id}`);
    const provvedimento =
      await this.provvedimentiService.provvedimentoByIdUdien(id);
    if (!provvedimento) {
      this.logger.warn(`Provvedimento non trovato. idUdienza:${id}`);
      throw new NotFoundException('Provvedimento non trovato');
    }
    return provvedimento;
  }

  @Query(() => [PenaleProvvedimenti], {
    name: 'provvedimentoByIdUdienzaAndOrdine',
  })
  async provvedimentoByIdUdienzaAndOrdine(
    @Args('id') idUdien: number,
    @Args('ordine') ordine: number,
  ): Promise<PenaleProvvedimenti[]> {
    this.logger.log(
      `Ricerca Provvedimento. idUdienza:${idUdien}, ordine:${ordine}`,
    );
    const [nrgForProvvedimenti, idRicUdienza] =
      await this.ricorsoUdienzaService.getNrgForProvvedimenti(idUdien, ordine);
    const provvedimento =
      await this.provvedimentiService.provvedimentoByNrgAndIdUdien(
        idUdien,
        nrgForProvvedimenti,
      );
    if (!provvedimento) {
      this.logger.warn(`Provvedimento non trovato. nrg:${nrgForProvvedimenti}`);
      throw new NotFoundException(nrgForProvvedimenti);
    }
    return provvedimento;
  }

  @Query(() => [PenaleProvvedimenti], {
    name: 'provvedimentoByNrg',
  })
  async provvedimentoByNrg(
    @Args('nrg', { nullable: true }) id: number,
  ): Promise<PenaleProvvedimenti[]> {
    return this.provvedimentoFindService.provvedimentoFindByNrg(id);
  }

  @Query(() => [PenaleProvvedimenti], {
    name: 'provvedimentoByNrgPerPresidente',
  })
  async provvedimentoByNrgPerPresidente(
    @Args('nrg') id: number,
  ): Promise<PenaleProvvedimenti[]> {
    this.logger.log(`Graphql query provvedimentoByNrgPerPresidente. nrg:${id}`);
    let provvedimento =
      await this.provvedimentiService.provvedimentoByNrgPerPresidente(id);
    provvedimento = provvedimento.filter(
      pro =>
        !(
          pro.stato === ProvvedimentiStatoEnum.BUSTA_RIFIUTATA &&
          Utils.isMinuta(pro.tipo)
        ),
    );
    for (let penaleProvvedimentiEntity of provvedimento) {
      penaleProvvedimentiEntity =
        await this.provvedimentoFindService.checkShowButtonVerificaAndModificaNew(
          penaleProvvedimentiEntity,
          true, // includeStatoCorrezione = true per mantenere la logica del resolver
        );
    }
    if (!provvedimento) {
      this.logger.warn(`Provvedimento non trovato. nrg:${id}`);
      throw new NotFoundException(id);
    }
    return provvedimento;
  }

  @Mutation(() => PenaleProvvedimenti, { name: 'createProvvedimento' })
  async createProvvedimento(
    @Args('provvedimento') provvedimento: CreateProvvedimentiInput,
  ) {
    try {
      this.logger.log(`Graphql mutation createProvvedimento.`);
      const provvedimentiDiPresenti =
        await this.provvedimentiService.provvedimentoByIdUdienAndNrgModify(
          provvedimento.idUdienza,
          provvedimento.nrg,
        );
      const isEstesore = await this.authService.isEstensoreforNrg(
        provvedimento.nrg,
      );
      const notDuplicate =
        await this.provvedimentoRelazioneService.disabledCreaNuovoProv(
          provvedimentiDiPresenti,
          isEstesore,
        );
      if (notDuplicate) {
        throw new ProvvedimentoNotDuplicatedException(
          "il provvedimento non può essere creato perche ce n'è un altro in bozza",
        );
      }

      const provvedimentoId =
        await this.provvedimentiService.createProvvedimento(provvedimento);

      if (provvedimentoId) {
        this.provvedimentiEstensoreService.createCodeDepositoEstensore(
          provvedimento?.addCodeFirma,
          provvedimentoId,
        );
        return await this.provvedimentiService.provvedimentoById(
          provvedimentoId,
        );
      }
    } catch (e) {
      this.logger.error(
        `Errore nella creazione del provvedimento. idUdienza:${provvedimento.idUdienza}, nrg:${provvedimento.nrg}`,
      );
      throw new GenericErrorException(
        e.message,
        CodeErrorEnumException.PROVV_NOT_CREATED,
      );
    }
    this.logger.warn(`Graphql mutation provvedimento non creato.`);
    throw new GenericErrorException(
      'Provvedimento non inserito',
      CodeErrorEnumException.PROVV_NOT_CREATED,
    );
  }

  /**
   * Caso importazione PDF, viene chiamato per creare il provvedimento e generare i dati dell'atto.
   *
   * @param createProvvLavorazioneInput
   */
  @Mutation(() => PenaleProvvedimenti, {
    name: 'GenerazioneProvvedimentoCreateMutation',
  })
  async GenerazioneProvvedimentoCreateMutation(
    @Args('createProvvLavorazioneInput')
    createProvvLavorazioneInput: CreateProvvLavorazioneInput,
  ) {
    this.logger.log(`Graphql mutation genera provvedimento e dati atto xml.`);
    const provvedimentiDiPresenti =
      await this.provvedimentiService.provvedimentoByIdUdienAndNrgModify(
        createProvvLavorazioneInput.idUdienza,
        createProvvLavorazioneInput.nrg,
      );
    const isEstesore = await this.authService.isEstensoreforNrg(
      createProvvLavorazioneInput.nrg,
    );
    const notDuplicate =
      await this.provvedimentoRelazioneService.disabledCreaNuovoProv(
        provvedimentiDiPresenti,
        isEstesore,
      );
    if (notDuplicate) {
      throw new ProvvedimentoNotDuplicatedException(
        "il provvedimento non può essere creato perche ce n'è un altro in bozza",
      );
    }
    let provv = null;
    try {
      const idAutore = await this.authService.getCurrentId();
      const bufferDatiAtto = await this.provvedimentiService.generaDatiAtto(
        createProvvLavorazioneInput,
      );

      provv = await this.provvLavorazService.createService(
        createProvvLavorazioneInput,
        idAutore,
      );
      // await this.provvLavorazService.saveFileProvvPdf(upload[0], provv);

      if (!bufferDatiAtto || !provv.idProvvedimento) {
        throw new InternalServerErrorException(
          'Errore nella generazione del pdf dei dati atto',
        );
      }
      await this.provvLavorazService.saveFileDatiAtto(
        bufferDatiAtto,
        provv.idProvvedimento,
      );
      const changeStatusValue = new CreateProvvedimentiChangeStatusInput();
      changeStatusValue.idProvvedimento = provv.idProvvedimento;
      changeStatusValue.stato = ProvvedimentiStatoEnum.IN_BOZZA;
      changeStatusValue.idAutore = idAutore;

      this.logger.log('Crea provvedimento e inserimento nel change status.');
      await this.changeStatusService.createProvvedimentoChangeStatus(
        changeStatusValue,
      );
      return new PenaleProvvedimenti({
        nrg: provv.nrg,
        fkIdCat: provv.fkIdCat,
        idProvvedimento: provv.idProvvedimento,
        idAutore: provv.idAutore,
        idUdienza: provv.idUdienza,
        nomeDocumento: provv.nomeDocumento,
        stato: provv.stato,
        tipo: provv.tipo,
        origine: provv.origine,
        dataDeposito: provv.dataDeposito,
        dataUltimaModifica: provv.dataUltimaModifica,
        dataDecisione: provv.dataDecisione,
      });
    } catch (e) {
      this.logger.error(
        `Errore Graphql mutation genera provvedimento e dati atto.xml.`,
        e,
      );
      if (provv && provv.idProvvedimento) {
        await this.provvLavorazService.deleteProvvLavorazione(
          provv.idProvvedimento,
        );
      }
      this.logger.warn('Errore nel crea provvedimento', e);
      throw e;
    }
  }

  @ResolveField('hasNote', () => Boolean, { nullable: true })
  async hasNote(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<boolean> {
    this.logger.log(
      `Graphql resolve hasNote. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    if (penaleProvvedimenti.idProvvedimento) {
      const count = await this.provvedimentiNoteService.countNote(
        penaleProvvedimenti.idProvvedimento,
      );

      return count > 0;
    }
    return false;
  }

  @ResolveField('note', () => [PenaleProvvedimentiNote], { nullable: true })
  async getProvvedimentoNote(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<PenaleProvvedimentiNote[] | null> {
    this.logger.log(
      `Graphql resolve note. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    if (penaleProvvedimenti.idProvvedimento) {
      return await this.provvedimentiNoteService.provvedimentoNotaByIdProvvedimento(
        penaleProvvedimenti.idProvvedimento,
      );
    }
    return null;
  }

  @ResolveField('listaFile', () => [PenaleProvvLavFile], { nullable: true })
  async getListaFile(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<PenaleProvvLavFile[] | null> {
    this.logger.log(
      `Graphql resolve listaFile. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    if (penaleProvvedimenti.idProvvedimento) {
      return await this.provvLavorazService.getListaPDF(
        penaleProvvedimenti.idProvvedimento,
      );
    }
    return null;
  }

  @ResolveField('checkDownloadAndSign', () => Boolean, { nullable: true })
  async checkDownloadAndSign(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<boolean> {
    this.logger.log(
      `Graphql resolve controllo se può essere scaricato e firmato. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    const isEstesore = await this.authService.isEstensoreforNrg(
      penaleProvvedimenti.nrg,
    );
    if (!isEstesore) {
      return true;
    }
    if (penaleProvvedimenti.idProvvedimento) {
      return (
        (await this.provvLavorazService.countPdfFile(
          penaleProvvedimenti.idProvvedimento,
        )) > 0
      );
    }
    return false;
  }

  @ResolveField('autore', () => PenaleTUtente, { nullable: true })
  async getAutore(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<PenaleTUtente | null> {
    this.logger.log(
      `Graphql resolve autore del provvedimento. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    return this.utenteService.utente(penaleProvvedimenti.idAutore);
  }

  @ResolveField('disabledCreaNuovoProv', () => Boolean, {
    nullable: true,
  })
  async disabledCreaNuovoProv(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<boolean | null> {
    this.logger.log(
      `Graphql resolve se deve essere disabilitato il bottone crea nuovo. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    const isEstesore = await this.authService.isEstensoreforNrg(
      penaleProvvedimenti.nrg,
    );
    return await this.provvedimentoRelazioneService.disabledCreaNuovoProv(
      penaleProvvedimenti,
      isEstesore,
    );
  }

  @ResolveField('disabledModificaOrDuplica', () => Boolean, {
    nullable: true,
  })
  async disabledModificaOrDuplica(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<boolean | null> {
    this.logger.log(
      `Graphql resolve se deve essere disabilitato il bottone modifica o duplica. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    const isEstesore = await this.authService.isEstensoreforNrg(
      penaleProvvedimenti.nrg,
    );
    if (!isEstesore) {
      return true;
    }
    if (penaleProvvedimenti && isEstesore) {
      const provvedimentoClonatoMp =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
          penaleProvvedimenti.idProvvedimento,
        );
      // se c'è una relazione è allora può darsi che si tratta di una busta rifiutata
      if (provvedimentoClonatoMp) {
        // mi prelevo il provvedimento padre
        const provvedimentoPadre =
          await this.provvedimentiService.provvedimentoById(
            provvedimentoClonatoMp.idProvvedimentoDestinazione,
          );
        if (provvedimentoPadre) {
          // se il provvedimento padre è una ordinanza/sentenza ed si trata di una busta rifiutata ed il provvedimento
          // figlio è una richiesta di modifica allora il provvedimento è duplicabile
          if (
            Utils.isOrdinanzaOSentenza(provvedimentoPadre?.tipo) &&
            penaleProvvedimenti.stato ==
              ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE &&
            (provvedimentoPadre?.stato ==
              ProvvedimentiStatoEnum.BUSTA_RIFIUTATA ||
              provvedimentoPadre?.stato ==
                ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE)
          ) {
            // il provv è duplicabile
            return false;
          }
          //TT MAC 44633: gestione caso in cui la minuta risulta da modificare e prima della richiesta di modifica
          //è stata per errore creata una bozza dal presidente, il provvedimento è comunque modificabile
          if (
            Utils.isMinuta(provvedimentoPadre?.tipo) &&
            penaleProvvedimenti.stato ==
              ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE &&
            provvedimentoPadre?.stato == ProvvedimentiStatoEnum.BOZZA_PRESIDENTE
          ) {
            return false;
          }
        }
        // il provvedimento non è duplicabile perchè esiste una relazione
        return true;
      }
    }
    return false;
  }

  @ResolveField('changeStatus', () => PenaleProvvChangeStatus, {
    nullable: true,
  })
  async getChangeStatus(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<PenaleProvvChangeStatus | null> {
    this.logger.log(
      `Graphql resolve change status del provvedimento. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    return await this.changeStatusService.changeStatusByIdProvvAndLast(
      penaleProvvedimenti.idProvvedimento,
    );
  }

  @Query(returns => [PenaleProvvedimenti], { name: 'provvedimenti' })
  provvedimenti(): Promise<PenaleProvvedimenti[]> {
    this.logger.log(`Graphql query lista dei provvedimenti.`);
    return this.provvedimentiService.provvedimenti();
  }

  @Query(() => PenaleProvvedimenti)
  async getProvvLavorazione(
    @Args('nrg') nrg: number,
  ): Promise<PenaleProvvedimenti | undefined> {
    this.logger.log(
      `Graphql query lista dei provvedimenti per nrg. nrg:${nrg}`,
    );
    try {
      const idAutore = await this.authService.getCurrentId();
      const dati = await this.provvLavorazService.getProvvLavorazione(
        nrg,
        idAutore,
      );
      if (dati) {
        return dati;
      }
    } catch (e) {
      this.logger.log(
        `Erro Graphql query lista dei provvedimenti per nrg. nrg:${nrg}`,
        e,
      );
      throw new InternalServerErrorException('errReadProvvDB');
    }
  }

  @Query(() => PenaleProvvedimenti)
  async getProvvLavorazioneByIdProvv(
    @Args('idProvv') idProvvedimento: string,
  ): Promise<PenaleProvvedimenti | undefined> {
    this.logger.log(
      `Graphql query provvedimento by idPRovv. idProvv:${idProvvedimento}`,
    );
    try {
      const idAutore = await this.authService.getCurrentId();
      const dati = await this.provvLavorazService.getProvvLavorazioneByIdProvv(
        idProvvedimento,
        idAutore,
      );
      if (dati) {
        return dati;
      }
    } catch (e) {
      this.logger.error(
        `Errore Graphql query provvedimento by idPRovv. idProvv:${idProvvedimento}`,
        e,
      );
      throw new InternalServerErrorException('errReadProvvDB');
    }
  }

  @Mutation(() => PenaleProvvedimenti)
  async firmaEDeposita(
    @Args('firmaProvvLavorazioneInput')
    firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput,
  ) {
    this.logger.log(
      `Graphql querutation firma e deposita provvedimento . idProvv:${firmaProvvLavorazioneInput?.idProvvedimento}`,
    );
    try {
      return await this.provvLavorazService.firmaEDepositaMutation(
        firmaProvvLavorazioneInput,
      );
    } catch (e) {
      this.logger.error(
        `Error Graphql mutation firma e deposita provvedimento . idProvv:${firmaProvvLavorazioneInput?.idProvvedimento}`,
        e,
      );
      throw e;
    }
  }

  @ResolveField('isRevisione', () => Boolean, {
    nullable: true,
  })
  async isRevisione(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<boolean | null> {
    if (penaleProvvedimenti?.stato === ProvvedimentiStatoEnum.IN_BOZZA) {
      return await this.changeStatusService.checkIsRevisione(
        penaleProvvedimenti.idProvvedimento,
      );
    }
    return false;
  }



  @ResolveField('isOscurato', () => Boolean)
  async isOscurato(
    @Parent() penaleProvvedimenti: PenaleProvvedimenti,
  ): Promise<boolean> {
    this.logger.log(
      `Graphql resolve listaFile. idProvv:${penaleProvvedimenti.idProvvedimento}`,
    );
    if (penaleProvvedimenti.idProvvedimento) {
      return await this.provvEditorLavorazioneService.isOscurato(
        penaleProvvedimenti.idProvvedimento,
      );
    }
    return false;
  }
}
