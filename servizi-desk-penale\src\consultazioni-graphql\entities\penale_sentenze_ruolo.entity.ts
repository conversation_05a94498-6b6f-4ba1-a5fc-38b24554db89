import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_SENTENZE_RUOLO') //nome tabella su schema oracle
export class PenaleSentenzeRuoloEntity {
  @PrimaryColumn({ name: 'ID_SENT_RUOLO' })
  idSentRuolo: number;

  @Column({ name: 'ID_ESITO' })
  idEsito: number;

  @Column({ name: 'TIPOSENT' })
  tipoInvio: string;
  @Column({ name: 'PQM' })
  pqm: string;

  public constructor(init?: Partial<PenaleSentenzeRuoloEntity>) {
    Object.assign(this, init);
  }
}
