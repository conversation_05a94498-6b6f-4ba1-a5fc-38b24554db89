import { Field, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParamEntity } from '../entities/penale_param.entity';
import { PenaleParam } from './penale_param.model';

@ObjectType()
export class PenaleTSentenza {
  @Field(() => Int)
  sentenza: number;
  @Field(() => Int)
  idSent: number;

  idTipoSent?: PenaleParam;

  @Field(() => Int)
  idEstentore: number;
  @Field(() => Int)
  idFunzione: number;
  @Field(() => Int)
  operatore: number;
  @Field(() => Date)
  dataMinuta: Date;
  @Field(() => Date)
  dataPubblicazione: Date;
  @Field(() => Int)
  nraccg: number;
  @Field(() => Int)
  idPqm: number;
  @Field(() => Int)
  idTipoUdienza: number;
  @Field(() => Int)
  quanti: number;
  @Field(() => Int)
  quantiCon: number;
  @Field(() => Int)
  quantiPr: number;
  @Field(() => Int)
  quantiConPr: number;
  @Field(() => Int)
  numMass: number;
  @Field(() => Date)
  dataRest: Date;
  @Field(() => Date)
  dataRestPr: Date;
  @Field(() => Date)
  dataMass: Date;
  @Field(() => Date)
  dataAsse: Date;
  @Field(() => Int)
  idMagis: number;
}
