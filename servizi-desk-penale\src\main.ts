import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { json, urlencoded } from 'express';
import { INestApplication, Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as process from 'process';

/*
import { ValidationPipe } from '@nestjs/common';
import { ValidationError } from 'class-validator';
import { UserInputError } from '@nestjs/apollo';
 */

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
  });
  setupSwagger(app);
  const logger = app.get(Logger);
  app.useLogger(logger);
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));

  /*
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      exceptionFactory: (errors: ValidationError[]) =>
        new UserInputError('VALIDATION_ERROR', {
          invalidArgs: errors,
        }),
    }),
  );
   */
  app.enableCors({
    exposedHeaders: 'content-disposition',
  });
  const globalPath = process.env.GLOBAL_PATH || '';
  app.setGlobalPrefix(globalPath);
  const port = process.env.SERVER_PORT || '3000';
  await app.listen(port);
  logger.debug(`Start server on port ${port}; globalPath:${globalPath}`);
}

function setupSwagger(nestApp: INestApplication): void {
  let globalPath = process.env.GLOBAL_PATH || '';
  const accessTokenFieldName =
    process.env.SWAGGER_ACCESS_TOKEN_FIELD_NAME || '';
  if (!globalPath.startsWith('/')) {
    globalPath = '/' + globalPath;
  }
  const swaggerPath = process.env.SWAGGER_PATH || '';
  const config = new DocumentBuilder()
    .setTitle('DESK REST API')
    .setDescription(
      `<h2> Desk Cassazione Penale Rest API Documentazione </h2> <h3> download api al <b><strong><a href="${swaggerPath}-json">link</a></strong></b></h3>`,
    )
    .setVersion('0.0.1')
    .addServer(globalPath)
    .addBearerAuth(
      {
        // I was also testing it without prefix 'Bearer ' before the JWT
        description: `[just text field] Please enter token in following format: Bearer <JWT>`,
        name: 'Authorization',
        bearerFormat: 'Bearer', // I`ve tested not to use this field, but the result was the same
        scheme: 'Bearer',
        type: 'http', // I`ve attempted type: 'apiKey' too
        in: 'Header',
      },
      'access-token',
    )
    .build();

  const document = SwaggerModule.createDocument(nestApp, config);

  if (swaggerPath) {
    SwaggerModule.setup(swaggerPath, nestApp, document, {
      customSiteTitle: 'DESK REST API',
      swaggerOptions: {
        docsExpansion: false,
        operationsSorter: 'alpha',
        tagsSorter: 'alpha',
      },
    });
  }
}
bootstrap();
