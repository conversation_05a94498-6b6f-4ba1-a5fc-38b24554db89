import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { SettingsEntity } from '../consultazioni-graphql/entities/settings.entity';
import { SettingsInput } from '../consultazioni-graphql/entities/dto/settings.input';
import { InternalServerErrorException } from '@nestjs/common';

@UfficioService()
export class ImpostazioniService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  impostazioniByCf(cfUtente: string): Promise<SettingsEntity | null> {
    return this.connection.getRepository(SettingsEntity).findOne({
      where: { cfUtente: cfUtente },
    });
  }

  async createSetting(input: SettingsInput): Promise<string | null> {
    const Cryptr = require('cryptr');
    const cryptr = new Cryptr('secretKey', {
      encoding: 'base64',
      pbkdf2Iterations: 10000,
      saltLength: 10,
    });
    const encryptedString = cryptr.encrypt(input?.passwordFirma);

    const settings = new SettingsEntity();
    settings.cfUtente = input.cfUtente;
    settings.usernameFirma = input.usernameFirma;
    settings.passwordFirma = encryptedString;
    settings.dtCreazione = new Date();

    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(SettingsEntity)
      .values(settings)
      .returning(['cfUtente'])
      .execute();

    const cfUtente = result.raw[0];

    if (cfUtente == null) {
      throw new InternalServerErrorException(
        "Problema nell'inserimento del setting",
      );
    }

    return cfUtente[0];
  }

  async updateSetting(input: SettingsInput): Promise<string | null> {
    const Cryptr = require('cryptr');
    const cryptr = new Cryptr('secretKey', {
      encoding: 'base64',
      pbkdf2Iterations: 10000,
      saltLength: 10,
    });
    const encryptedString = cryptr.encrypt(input?.passwordFirma);

    const result = await this.connection
      .createQueryBuilder()
      .update(SettingsEntity)
      .set({
        usernameFirma: input.usernameFirma,
        passwordFirma: encryptedString,
        dtModifica: new Date(),
      })
      .where('cfUtente= :cfUtente', { cfUtente: input.cfUtente })
      .returning(['cfUtente', 'usernameFirma', 'passwordFirma'])
      .execute();

    const cfUtente = result.raw[0];
    const usernameFirma = result.raw[1];
    const passwordFirma = result.raw[2];

    if (cfUtente == null || usernameFirma == null || passwordFirma == null) {
      throw new InternalServerErrorException(
        'Problema nella modifica del setting',
      );
    }

    return cfUtente[0];
  }

  async deleteSetting(cf: string): Promise<boolean | null> {
    const result = await this.connection
      .getRepository(SettingsEntity)
      .delete({ cfUtente: cf });
    if (result?.affected != null && result.affected > 0) {
      return true;
    } else {
      return false;
    }
  }
}
