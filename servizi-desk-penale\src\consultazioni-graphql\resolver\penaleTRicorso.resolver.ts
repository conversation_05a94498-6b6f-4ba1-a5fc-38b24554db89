import { Logger } from '@nestjs/common';
import {
  Args,
  Int,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';

import { TRicorsoService } from 'src/fascicolo/t-ricorso.service';
import { PenaleTRicorso } from '../models/penale_t_ricorso.model';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { PartiService } from '../../parti/parti.service';
import { PenaleReatiRicorso } from '../models/penale_reati_ricorso.model';
import { ReatiRicorsoService } from '../../reati-ricorso/reati-ricorso.service';
import { PenaleTSpoglio } from '../models/penale_t_spoglio.model';
import { SpoglioService } from '../../spoglio/spoglio.service';
import {
  PenaleListPartiEControParteModel,
  PenaleListPartiModel,
} from '../models/penale_list_parti.model';
import { ControPartiService } from '../../contro-parti/contro-parti.service';
import { ProvvimpugnatiService } from '../../provvImpugnati/provvimpugnati.service';
import * as moment from 'moment';
import { AuloService } from '../../aulo/aulo.service';
import { Utils } from '../../utils/utils';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { UdienzaService } from '../../udienza/udienza.service';
import { DatiRicorsoPrincipaliService } from '../../fascicolo/dati-ricorso-principali.service';
import { GenericErrorException } from '../../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../../exceptions/code-error-enum.exception';
import { PenaleTProvvedEntity } from '../entities/penale_t_provved.entity';

@Resolver(() => PenaleTRicorso)
export class PenaleTRicorsoResolver {
  private logger = new Logger(PenaleTRicorsoResolver.name);

  constructor(
    protected readonly tRicorsoService: TRicorsoService,
    protected readonly partiService: PartiService,
    protected readonly spoglioService: SpoglioService,
    protected readonly datiRicorsoPrincipaliService: DatiRicorsoPrincipaliService,
    protected readonly provvimpugnatiService: ProvvimpugnatiService,
    protected readonly controPartiService: ControPartiService,
    protected readonly auloService: AuloService,
    protected readonly reatiRicorsoService: ReatiRicorsoService,
    protected readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    protected readonly udienzaService: UdienzaService,
  ) {}

  @ResolveField('anno', () => Int)
  async getAnno(@Parent() ricorso: PenaleTRicorso): Promise<number | null> {
    const nrgReale = ricorso.nrgReale + '';
    return Number(nrgReale.substring(0, 4));
  }

  @ResolveField('numero', () => Int)
  async getNumero(@Parent() ricorso: PenaleTRicorso): Promise<number | null> {
    const nrgReale = ricorso.nrgReale + '';
    return Number(nrgReale.substring(4, 10));
  }

  @ResolveField('parti', () => [PenaleTParti], { nullable: true })
  async getRicorsoParti(
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<PenaleTParti[] | null> {
    if (ricorso.nrg > 0) {
      return await this.partiService.ricorsoFindByNrg(ricorso.nrg);
    }
    return null;
  }

  @ResolveField('listaParti', () => PenaleListPartiModel, { nullable: true })
  async getListaParti(
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<PenaleListPartiModel | null> {
    this.logger.log(`Calcolo della lista delle parti per nrg:${ricorso.nrg}`);
    if (ricorso.nrg > 0) {
      //parti coinvolte
      const listaPartiEntity = await this.partiService.ricorsoFindByNrg(
        ricorso.nrg,
      );
      const listaPartiModel = new PenaleListPartiModel();
      // tutte le parti convertite in modello
      let listParti = listaPartiEntity?.map(r => {
        return new PenaleTParti({ ...r });
      });

      if (listParti) {
        listParti = listParti.sort((a, b) => {
          if (!a.numOrdine) {
            a.numOrdine = 0;
          }
          if (!b.numOrdine) {
            b.numOrdine = 0;
          }
          if (a.numOrdine == b.numOrdine) {
            return a.idParte < b.idParte ? -1 : 1;
          }
          return a.numOrdine < b.numOrdine ? -1 : 1;
        });
        listaPartiModel.parte = listParti;
        // prende solo gli id delle parti
        const customData = listParti.reduce((result, item) => {
          result.push(item.idParte);
          return result;
        }, [] as number[]);
        console.log('ids:', customData);
        // si fa restituire le controparti se esistono
        const controPartiIds = await this.controPartiService.getControParti(
          customData,
        );
        // tutte le controparti
        const controParteList = listParti.filter(r =>
          controPartiIds.some(cr => cr.idControParte === r.idParte),
        );
        console.log('controparti:', controParteList);
        //le parti che hanno subito il danno
        const controParteModelList =
          new Array<PenaleListPartiEControParteModel>();
        listParti.forEach(onlyParte => {
          const controParte = new PenaleListPartiEControParteModel();
          controParte.parte = onlyParte;
          controParte.controParte = controParteList.filter(contro => {
            return controPartiIds.some(ids => {
              return (
                ids.idControParte === contro.idParte &&
                onlyParte.idParte === ids.idParte
              );
            });
          });
          controParteModelList.push(controParte);
        });

        listaPartiModel.controParteList = controParteModelList;

        this.logger.log(
          `Fine calcolo della lista delle parti per nrg:${
            ricorso.nrg
          }: listaParti:${JSON.stringify(listaPartiModel)}`,
        );
        return listaPartiModel;
      }
    }
    this.logger.log(
      `Fine calcolo della lista delle parti per nrg:${ricorso.nrg}: listaParti:null}`,
    );
    return null;
  }

  @ResolveField('spoglio', () => PenaleTSpoglio, { nullable: true })
  async getSpoglio(
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<PenaleTSpoglio | null> {
    this.logger.log(`Calcolo dello spoglio per nrg:${ricorso.nrg}}`);
    if (ricorso.nrg > 0) {
      const spoglio = await this.spoglioService.spoglioFindByNrg(ricorso.nrg);

      if (spoglio && (!spoglio?.valPond || spoglio?.valPond === 0)) {
        if (ricorso.nrgPrinc) {
          const datiRicorsoByNrg =
            await this.datiRicorsoPrincipaliService.datiRicorsoByNrg(
              ricorso.nrgPrinc,
            );
          if (datiRicorsoByNrg?.tipoRiunione?.sigla.toUpperCase() === '02') {
            spoglio.valPond = 0.0;
          } else {
            spoglio.valPond = 1.0;
          }
        }
        spoglio.valPond = 1.0;
      }

      this.logger.log(
        `Fine Calcolo dello spoglio per nrg:${ricorso.nrg}}, con valore ponderale:${spoglio?.valPond}`,
      );
      return spoglio || null;
    }
    return null;
  }

  @ResolveField('provvedimentoImpugnato', () => [String], { nullable: true })
  async getProvvedimentoImpugnato(
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<Array<string> | null> {
    this.logger.log(
      `Calcolo del provvedimento impugnato per nrg:${ricorso.nrg}`,
    );
    if (ricorso.nrg > 0) {
      const provvImpugnatiList = new Array<string>();
      const provvList =
        await this.provvimpugnatiService.provvedimentoDetailsByNrg(ricorso.nrg);
      if (provvList?.length > 0) {
        for (const provvedimentoImpugnato of provvList) {
          let provvImp = '';
          if (provvedimentoImpugnato) {
            if (provvedimentoImpugnato.impugnato) {
              provvImp += 'IMP - ';
            }
            provvImp = await this.calcolaProvvedimentoimpugnato(
              provvedimentoImpugnato,
              provvImp,
            );
            provvImpugnatiList.push(provvImp);
          }
        }
        this.logger.log(
          ` Fine Calcolo del provvedimento impugnato per nrg:${ricorso.nrg}`,
        );
        return provvImpugnatiList.sort((a, _) =>
          a?.trim().indexOf('IMP -') ? 1 : -1,
        );
      }
    }
    this.logger.log(
      `Fine Calcolo del provvedimento impugnato per nrg:${ricorso.nrg} con risultato null`,
    );
    return null;
  }

  private async calcolaProvvedimentoimpugnato(
    provvedimentoImpugnato: PenaleTProvvedEntity,
    provvImp: string,
  ) {
    if (provvedimentoImpugnato.numProvv) {
      const [anno, numero] = Utils.calcoloNumeroFascicolo(
        provvedimentoImpugnato.numProvv + '',
      );
      provvImp += anno && numero ? numero + '-' + anno : '';
    }
    if (provvedimentoImpugnato?.tipoProvv?.descrizione) {
      provvImp += ' ' + provvedimentoImpugnato.tipoProvv.descrizione;
    }
    if (provvedimentoImpugnato?.gradoProvv?.descrizione) {
      provvImp += ' ' + provvedimentoImpugnato.gradoProvv.descrizione;
    }
    if (provvedimentoImpugnato.dataProvv) {
      provvImp +=
        ' ' + moment(provvedimentoImpugnato.dataProvv).format('DD/MM/YYYY');
    }
    if (provvedimentoImpugnato.idAulo) {
      const aulo = await this.auloService.aulaByIdAulo(
        provvedimentoImpugnato.idAulo,
      );
      if (aulo?.autorita.descrizione) {
        provvImp += ' ' + aulo?.autorita.descrizione + ' ' + aulo.localita;
      }
    }
    return provvImp;
  }

  @ResolveField('reatiRicorso', () => [PenaleReatiRicorso], { nullable: true })
  async getReatiRicorso(
    @Parent() ricorso: PenaleTRicorso,
  ): Promise<PenaleReatiRicorso[] | null> {
    if (ricorso.nrg > 0) {
      return await this.reatiRicorsoService.reatiRicorsoFindByNrg(ricorso.nrg);
    }
    return null;
  }

  @Query(() => PenaleTRicorso, { name: 'ricorso' })
  async ricorso(@Args('id') id: number): Promise<PenaleTRicorso> {
    const ricorso = await this.tRicorsoService.ricorsoFindByNrg(id);
    if (!ricorso) {
      this.logger.error(`Calcolo del ricorso per nrg:${id}`);
      throw new GenericErrorException(
        'Ricorso non trovato',
        CodeErrorEnumException.RICORSO_NOT_FOUND,
      );
    }
    return ricorso;
  }

  @Query(() => [PenaleTRicorso], { name: 'ricorsi' })
  ricorsi(): Promise<PenaleTRicorso[]> {
    return this.tRicorsoService.ricorsi();
  }
}
