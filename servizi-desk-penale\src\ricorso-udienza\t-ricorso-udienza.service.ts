import { Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { DataSource, EntityManager, In } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';
import { PenaleTEsitoSentEntity } from '../consultazioni-graphql/entities/penale_t_esito_sent.entity';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { PenaleTMagisEntity } from '../consultazioni-graphql/entities/penale_t_magis.entity';
import { PenaleAnagmagisEntity } from '../consultazioni-graphql/entities/penale_anagmagis.entity';
import { InfoDettaglioFascicoloModel } from '../consultazioni-graphql/models/dto/info-dettaglio-fascicolo.model';
import { PenaleTRicudien } from '../consultazioni-graphql/models/penale_t_ricudien.model';
import { endCursor, startCursor } from '../relay-pagination/pagination-utils';
import { PaginationCustomQueryFilter } from '../consultazioni-graphql/models/dto/generic_paginator';
import { PenaleTRicorsoEntity } from '../consultazioni-graphql/entities/penale_t_ricorso.entity';
import { PenaleParamEntity } from '../consultazioni-graphql/entities/penale_param.entity';

@UfficioService()
export class TRicorsoUdienzaService {
  private logger = new Logger(TRicorsoUdienzaService.name);

  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  ricorsiUdienza(): Promise<PenaleTRicorsoUdienzaEntity[]> {
    return this.connection.getRepository(PenaleTRicorsoUdienzaEntity).find();
  }

  /**
   * Restituisce la lista dei id del magistrato in base al CF
   * @param entityManager il gestore delle entity che si sta usando
   * @param cf
   */
  async getMagisByCF(
    entityManager: EntityManager,
    cf: string,
  ): Promise<number[]> {
    try {
      const magis = await entityManager
        .createQueryBuilder(PenaleTMagisEntity, 'magis')
        .innerJoin(
          PenaleAnagmagisEntity,
          'ana',
          'ana.idAnagmagis = magis.idAnagmagis',
        )
        .where('ana.CODICE_FISCALE = :cf', { cf })
        .select(['magis.idMagis'])
        .cache('magis_query_' + cf, 3600000) // Cache per 12 ore
        .getMany();
      return magis.map(row => row.idMagis);
    } catch (error) {
      this.logger.error(`Error in getMagisByCF for CF ${cf}:`, error);
      throw error;
    }
  }

  ricorsoUdienza(
    idRicudien: number,
  ): Promise<PenaleTRicorsoUdienzaEntity | null> {
    const ricorsoUdienza = this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOneBy({ idRicudien });
    this.logger.log(
      `Ricerca del ricorso udienza per idRicUdien: ${idRicudien}`,
    );
    return ricorsoUdienza;
  }
  async ricorsoUdienzaByUdienAndNrg(idUdienza: number, nrg: number) {
    this.logger.debug('entro nella query');
    const ricorsoUdienza = await this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .createQueryBuilder('RICUDIEN')
      .distinct(true)
      .leftJoinAndMapOne(
        'RICUDIEN.esito',
        PenaleTEsitoEntity,
        'esito',
        'RICUDIEN.ID_RICUDIEN = esito.ID_RICUDIEN',
      )
      .leftJoinAndMapOne(
        'esito.esitoSent',
        PenaleTEsitoSentEntity,
        'esitoSent',
        'esito.ID_ESITO = esitoSent.ID_ESITO',
      )
      .leftJoinAndMapOne(
        'esitoSent.sentenza',
        PenaleTSentenzaEntity,
        'TSET',
        'TSET.ID_SENT = esitoSent.ID_SENT',
      )
      .where('RICUDIEN.nrg = :nrg', { nrg })
      .andWhere('RICUDIEN.idUdienza = :idUdienza', { idUdienza })
      .getMany();
    this.logger.log(
      `Ricerca del ricorso udienza per idUdienza: ${idUdienza}, nrg: ${nrg}`,
    );
    return ricorsoUdienza;
  }
  async ricorsoUdienzaByUdien(
    cf: string,
    idUdien: number,
    paginationArgs: PaginationCustomQueryFilter,
  ): Promise<[result: PenaleTRicorsoUdienzaEntity[] | null, total: number]> {
    return await this.connection.manager.transaction(
      async (entityManager: EntityManager) => {
        // Otteniamo gli ID dei magistrati dalla query cachata
        const magistratiIds = await this.getMagisByCF(entityManager, cf);
        const from = startCursor(paginationArgs);
        const to = endCursor(paginationArgs);
        const offset = Number.parseInt(from) || 0;
        const limit = Number.parseInt(to) || 5;
        const penaleTRicorsoUdienzaEntitySelectQueryBuilder = entityManager
          .getRepository(PenaleTRicorsoUdienzaEntity)
          .createQueryBuilder('RICUDIEN')
          .distinct(true)
          .leftJoinAndMapOne(
            'RICUDIEN.esito',
            PenaleTEsitoEntity,
            'esito',
            'RICUDIEN.ID_RICUDIEN = esito.ID_RICUDIEN',
          )
          .leftJoinAndMapOne(
            'esito.esitoSent',
            PenaleTEsitoSentEntity,
            'esitoSent',
            'esito.ID_ESITO = esitoSent.ID_ESITO',
          )
          // Non usare leftJoinAndSelect altrimenti restituisce ricorsiUdienza duplicati perche abbiamo n provvedimenti nel db
          //leftJoinAndSelect
          .leftJoin(
            PenaleProvvedimentiEntity,
            'PROVV',
            `PROVV.idUdienza = RICUDIEN.ID_UDIEN AND PROVV.nrg = RICUDIEN.NRG AND PROVV.LAST_MODIFIED = 1`,
          )
          .leftJoinAndMapOne(
            'esitoSent.sentenza',
            PenaleTSentenzaEntity,
            'TSET',
            'TSET.ID_SENT = esitoSent.ID_SENT',
          )
          .leftJoin(PenaleParamEntity, 'PP', 'TSET.ID_TIPOSENT = PP.ID_PARAM')
          .where(
            `(TSET.ID_SENT IS NOT NULL OR RICUDIEN.ID_ESITO IS NOT NULL)
                         AND RICUDIEN.ID_UDIEN = :idUdienza
                         AND (RICUDIEN.ID_RELATORE IN (${magistratiIds.join(
                           ',',
                         )}) OR TSET.ID_ESTENSORE IN (${magistratiIds.join(
              ',',
            )}))
              AND (PP.SIGLA IN ('SE', 'OR') OR "esito".ESITO in (1425,1424,1421, 742787))`,
            {
              idUdienza: idUdien,
            },
          )
          .orderBy('RICUDIEN.idRicudien', 'DESC');

        if (paginationArgs.status) {
          // Essendo non legati direttamente dallo stato, se è indicato uno stato diverso da quelli seguenti,
          // PROVV_DEPOSITATO_SIC, PUBBLICATO_SIC e MINUTA_DEPOSITATA_SIC
          // i ricorsi che risultano depositati/pubblicati da SIC devono essere esclusi dai risultati
          if (
            paginationArgs.status !==
              ProvvedimentiStatoEnum.PROVV_DEPOSITATO_SIC &&
            paginationArgs.status !== ProvvedimentiStatoEnum.PUBBLICATO_SIC &&
            paginationArgs.status !==
              ProvvedimentiStatoEnum.MINUTA_DEPOSITATA_SIC
          ) {
            penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
              '(TSET.PUBBLICATO_TELEMATICO != 0 OR TSET.DATAPUBBL IS NULL OR TSET.NRACCG IS NOT NULL) AND ' +
                '(TSET.PUBBLICATO_TELEMATICO != 0 OR TSET.NRACCG IS NULL) AND ' +
                '(TSET.DEPOSITO_TELEMATICO != 0 OR TSET.DATAMINUTA IS NULL OR TSET.DATAPUBBL IS NOT NULL)',
            );
          }
          switch (paginationArgs.status) {
            case ProvvedimentiStatoEnum.DA_REDIGERE:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                'PROVV.ID_PROVV IS NULL AND "esito".ESITO = 1423',
              );
              break;
            case ProvvedimentiStatoEnum.IN_BOZZA:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                `PROVV.stato = 'BOZZA' AND NOT EXISTS(
                    SELECT * FROM PENALE_MP_PROVV pmp 
                    WHERE pmp.ID_PROVV_DEST = PROVV.ID_PROVV)`,
              );
              break;
            case ProvvedimentiStatoEnum.PROVV_DEPOSITATO_SIC:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                'TSET.PUBBLICATO_TELEMATICO = 0 AND TSET.DATAPUBBL IS NOT NULL AND  TSET.NRACCG IS NULL',
              );
              break;

            case ProvvedimentiStatoEnum.PUBBLICATO_SIC:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                'TSET.PUBBLICATO_TELEMATICO = 0 AND TSET.NRACCG IS NOT NULL',
              );
              break;

            case ProvvedimentiStatoEnum.MINUTA_DEPOSITATA_SIC:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                'TSET.DEPOSITO_TELEMATICO = 0 AND TSET.DATAMINUTA IS NOT NULL AND  TSET.DATAPUBBL IS NULL',
              );
              break;
            case ProvvedimentiStatoEnum.IN_RELAZIONE_ESTENSORE:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                "RICUDIEN.ID_RELATORE <> TSET.ID_ESTENSORE AND (PROVV.ID_PROVV IS NULL OR PROVV.stato = 'IN_BOZZA')  ",
              );
              break;
            case ProvvedimentiStatoEnum.BUSTA_RIFIUTATA:
              //le condizioni sono:
              //lo stato del provvedimento attuale è BUSTA_RIFIUTATA e non il provvedimento non è duplicato
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                `PROVV.stato = 'BUSTA_RIFIUTATA' AND (
                  SELECT PREV_STATO FROM (
                    SELECT ppcs.PREV_STATO FROM PENALE_PROVV_CHANGE_STATUS ppcs
                    WHERE ppcs.ID_PROVV = PROVV.ID_PROVV
                    ORDER BY ppcs.DATE_CHANGE DESC
                    ) WHERE ROWNUM = 1
                ) = 'INVIATO_IN_CANCELLERIA_RELATORE'`,
              );
              break;
            case ProvvedimentiStatoEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE:
              //le condizioni sono:
              //lo stato del provvedimento attuale è BUSTA_RIFIUTATA e quello precedente è INVIATO_IN_CANCEL_PRESIDENTE
              //oppure lo stato attuale è BOZZA_PRESIDENTE o CODA_DI_FIRMA (lato Presidente)
              // e lo stato del provvedimento precedente è BUSTA_RIFIUTATA
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                `(
                  (
                    PROVV.stato = 'BUSTA_RIFIUTATA' AND (
                      SELECT PREV_STATO 
                      FROM (
                        SELECT ppcs.PREV_STATO 
                        FROM PENALE_PROVV_CHANGE_STATUS ppcs
                        WHERE ppcs.ID_PROVV = PROVV.ID_PROVV
                        ORDER BY ppcs.DATE_CHANGE DESC
                      ) 
                      WHERE ROWNUM = 1
                    ) = 'INVIATO_IN_CANCEL_PRESIDENTE'
                  )
                  OR
                  (
                    (
                      PROVV.stato = 'BOZZA_PRESIDENTE' OR 
                      (PROVV.stato = 'CODA_DI_FIRMA' AND PROVV.tipo IN ('ORDINANZA', 'SENTENZA'))
                    )
                    AND PROVV.tipo IN ('ORDINANZA', 'SENTENZA') -- Spostato qui per semplificare
                    AND (
                      SELECT pp.STATO 
                      FROM PENALE_PROVVEDIMENTI pp
                      WHERE pp.ID_PROVV = (
                        SELECT ID_PROVV_ORIGINE
                        FROM (
                          SELECT pmp.ID_PROVV_ORIGINE 
                          FROM PENALE_MP_PROVV pmp
                          WHERE pmp.ID_PROVV_DEST = PROVV.ID_PROVV
                          ORDER BY pmp.CREATE_AT DESC
                        ) 
                        WHERE ROWNUM = 1
                      )
                    ) = 'BUSTA_RIFIUTATA'
                  )
                )`,
              );
              break;
            case ProvvedimentiStatoEnum.MINUTA_IN_REVISIONE:
              // Caso speciale per MINUTA_IN_REVISIONE: provvedimenti in stato IN_BOZZA che hanno come stato precedente
              // uno tra MINUTA_DA_MODIFICARE, BUSTA_RIFIUTATA o MINUTA_MODIFICATA_PRESIDENTE
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                `PROVV.stato = 'BOZZA' AND (
                  SELECT PREV_STATO FROM (
                    SELECT ppcs.PREV_STATO FROM PENALE_PROVV_CHANGE_STATUS ppcs
                    WHERE ppcs.ID_PROVV = PROVV.ID_PROVV
                    ORDER BY ppcs.DATE_CHANGE DESC
                  ) WHERE ROWNUM = 1
                ) IN ('MINUTA_DA_MODIFICARE', 'BUSTA_RIFIUTATA', 'MINUTA_MODIFICATA_PRESIDENTE')`,
              );
              break;
            case ProvvedimentiStatoEnum.MINUTA_ACCETTATA:
              //le condizioni sono:
              //lo stato del provvedimento attuale è MINUTA_ACCETTATA
              //oppure lo stato attuale è BOZZA_PRESIDENTE o CODA_DI_FIRMA (lato presidente)
              //  e lo stato del provvedimento precedente è MINUTA_ACCETTATA
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                `(PROVV.stato = 'MINUTA_ACCETTATA' OR (
                  (PROVV.stato = 'BOZZA_PRESIDENTE' OR 
                  PROVV.stato = 'CODA_DI_FIRMA' AND PROVV.tipo IN ('ORDINANZA', 'SENTENZA')) 
                  AND (
                  SELECT pp.STATO FROM PENALE_PROVVEDIMENTI pp
                    WHERE pp.ID_PROVV = (
                      SELECT ID_PROVV_ORIGINE
                      FROM (
                        SELECT pmp.ID_PROVV_ORIGINE 
                        FROM PENALE_MP_PROVV pmp
                        WHERE pmp.ID_PROVV_DEST = PROVV.ID_PROVV
                        ORDER BY pmp.CREATE_AT DESC
                  ) WHERE ROWNUM = 1
              )) = 'MINUTA_ACCETTATA'))`,
              );
              break;
            // gli altri stati sono stati gestiti del provv quindi si può eseguire la query senza problemi
            default:
              penaleTRicorsoUdienzaEntitySelectQueryBuilder.andWhere(
                'PROVV.stato = :status',
                { status: paginationArgs.status },
              );
              break;
          }
        }
        console.log(
          'Query SQL:',
          penaleTRicorsoUdienzaEntitySelectQueryBuilder.getSql(),
        );
        console.log(
          'Parameters:',
          penaleTRicorsoUdienzaEntitySelectQueryBuilder.getParameters(),
        );
        const [result, total] =
          await penaleTRicorsoUdienzaEntitySelectQueryBuilder
            .offset(offset)
            .limit(limit)
            .getManyAndCount();

        this.logger.log(
          `Ricerca del ricorso udienza per il relatore/estensore. esito:${result?.[0]?.esito} idUdienza: ${idUdien}: risultato:${result?.length} totale:${total}`,
        );

        return [result, total];
      },
    );
  }

  async getValorePonderaleComplessivoAndTotaleElementi(
    cf: string,
    idUdien: number,
  ): Promise<InfoDettaglioFascicoloModel> {
    return await this.connection.manager.transaction(
      async (entityManager: EntityManager) => {
        const magistratiIds = await this.getMagisByCF(entityManager, cf);

        const risultato = await entityManager
          .createQueryBuilder()
          .select([
            'SUM(VAL_POND_COMPLESSIVO.valorePonderale) as valorePonderaleTotale',
            'COUNT(DISTINCT VAL_POND_COMPLESSIVO.RICUDIEN) AS totalCount',
          ])
          .from(subQuery => {
            return subQuery
              .select([
                'RICUDIEN.ID_RICUDIEN AS RICUDIEN',
                'DECODE(SPOGLIO.VALPOND, 0, 1, NULL, 1, SPOGLIO.VALPOND) AS valorePonderale',
              ])
              .from('PENALE_T_RICUDIEN', 'RICUDIEN') // Aggiunta tabella principale
              .distinct(true)
              .leftJoin(
                PenaleTEsitoEntity,
                'ESITO',
                'RICUDIEN.ID_RICUDIEN = ESITO.ID_RICUDIEN',
              )
              .leftJoin(
                PenaleTEsitoSentEntity,
                'ESITOSENT',
                'ESITO.ID_ESITO = ESITOSENT.ID_ESITO',
              )
              .leftJoin(
                PenaleTSentenzaEntity,
                'TSET',
                'TSET.ID_SENT = ESITOSENT.ID_SENT',
              )
              .leftJoin(
                PenaleParamEntity,
                'PP',
                'TSET.ID_TIPOSENT = PP.ID_PARAM',
              )
              .leftJoin(
                'PENALE_T_SPOGLIO',
                'SPOGLIO',
                'SPOGLIO.NRG = RICUDIEN.NRG',
              )
              .where(
                '(TSET.ID_SENT IS NOT NULL OR RICUDIEN.ID_ESITO IS NOT NULL)',
              )
              .andWhere('RICUDIEN.ID_UDIEN = :idUdien', { idUdien: idUdien })
              .andWhere(
                `(RICUDIEN.ID_RELATORE IN (${magistratiIds.join(
                  ',',
                )}) OR TSET.ID_ESTENSORE IN (${magistratiIds.join(',')}))`,
              )
              .andWhere("(PP.SIGLA IN ('SE', 'OR') OR ESITO.ESITO in (1425,1424,1421, 742787))");
          }, 'VAL_POND_COMPLESSIVO')
          .getRawOne();

        return {
          valorePonderaleTotale: risultato.VALOREPONDERALETOTALE || 0,
          totalCount: risultato.TOTALCOUNT || 0,
        };
      },
    );
  }

  async ricorsoUdienzaByNrgAndIdUdienza(
    idUdien: number,
    nrg: number,
  ): Promise<PenaleTRicorsoUdienzaEntity | null> {
    const ricorsoUdienza = this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: { idUdienza: idUdien, nrg: nrg },
      });
    this.logger.log(`Ricerca del ricorso udienza per nrg. nrg: ${nrg}`);
    return ricorsoUdienza;
  }

  async ricorsoUdienzaByid(
    idRicudien: number,
  ): Promise<PenaleTRicorsoUdienzaEntity | null> {
    const ricorsoUdienza = this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: { idRicudien: idRicudien },
      });
    this.logger.log(
      `Ricerca del ricorso udienza per id. idRicudien: ${idRicudien}`,
    );
    return ricorsoUdienza;
  }

  async ricorsoUdienzaByIdRicUdienList(
    idRicudienList: number[],
  ): Promise<Array<PenaleTRicorsoUdienzaEntity> | null> {
    const ricorsoUdienza = this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .find({
        where: { idRicudien: In(idRicudienList) },
      });
    this.logger.log(
      `Ricerca del ricorso udienza per ids. idRicudien: ${idRicudienList}`,
    );
    return ricorsoUdienza;
  }

  async getNrgForProvvedimenti(
    id: number,
    ordine: number,
  ): Promise<[number, number]> {
    const promise = await this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: { idUdienza: id, numOrdine: ordine },
      });
    if (promise) {
      return [promise.nrg, promise.idRicudien];
    }
    throw new InternalServerErrorException(
      'Errore nella ricerca del provvedimento ',
    );
  }

  async getIdRicUdienzaByNrgAndIdUdienza(
    id: number,
    nrg: number,
  ): Promise<number> {
    const promise = await this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: { idUdienza: id, nrg: nrg },
      });
    if (promise) {
      return promise.idRicudien;
    }
    throw new InternalServerErrorException(
      'Errore nella ricerca del provvedimento ',
    );
  }

  async updateStatoProvvedimentoRicorsoUdienza(
    idUdienza: number,
    nrg: number | undefined,
    stato: ProvvedimentiStatoEnum,
  ) {
    return await this.connection
      .createQueryBuilder()
      .update(PenaleTRicorsoUdienzaEntity)
      .set({ statoProvvedimento: stato })
      .where('nrg= :nrg and idUdienza= :idUdien', {
        nrg: nrg,
        idUdien: idUdienza,
      })
      .execute();
  }

  async ricorsoUdienzaByPrincipale(
    entityManager: EntityManager,
    nrgPrincipale: number,
  ) {
    const promise = await entityManager.find(PenaleTRicorsoUdienzaEntity, {
      where: { principale: nrgPrincipale },
      relations: {
        ricorso: true,
      },
    });
    return promise;
  }

  async isEstensore(currentUserCf: string, penaleTRicudien: PenaleTRicudien) {
    return this.connection.manager.transaction(
      async transactionalEntityManager => {
        const magisIdsByCF = await this.getMagisByCF(
          transactionalEntityManager,
          currentUserCf,
        );
        return (
          magisIdsByCF &&
          magisIdsByCF.some(
            m => m === penaleTRicudien?.esito?.esitoSent?.sentenza?.idEstentore,
          )
        );
      },
    );
    return false;
  }

  async isRelatoreforNrg(
    currentUserCf: string,
    penaleTRicudien: PenaleTRicudien,
  ) {
    return this.connection.manager.transaction(
      async transactionalEntityManager => {
        const magisIdsByCF = await this.getMagisByCF(
          transactionalEntityManager,
          currentUserCf,
        );
        return (
          magisIdsByCF &&
          magisIdsByCF.some(m => m === penaleTRicudien?.idRelatore)
        );
      },
    );
    return false;
  }
}
