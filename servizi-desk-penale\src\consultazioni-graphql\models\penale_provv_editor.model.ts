import { Field, ID, ObjectType } from '@nestjs/graphql';
import {ApiProperty} from "@nestjs/swagger";
import {ProvvedimentiTipoEnum} from "../entities/enumaration/provvedimenti-tipo.enum";

@ObjectType()
export class PenaleProvvEditor {
  @ApiProperty()
  @Field(type => ID)
  idProvvedimentoEditor: string;
  @ApiProperty()
  @Field(type => String)
  idProvvedimento?: string;
  @ApiProperty()
  @Field(type => String)
  textLibero?: string | null;
  @ApiProperty()
  @Field(type => String)
  textOscurato?: string | null;
  @ApiProperty()
  @Field(type => String)
  introduzione?: string | null;
  @ApiProperty()
  @Field(type => String)
  motivoRicorso?: string | null;
  @ApiProperty()
  @Field(type => String)
  finaleDeposito?: string | null;
  @ApiProperty()
  @Field(type => String)
  pqm?: string | null;
  @Field(type => Date)
  createAt?: Date;
  @ApiProperty()
  oscurato?: boolean;
  @ApiProperty()
  strutturato?: boolean;
  @ApiProperty()
  semplificata: boolean;
  @ApiProperty()
  idSezionale: string | null;
  @ApiProperty()
  tipoProvvedimento: ProvvedimentiTipoEnum;

  public constructor(init?: Partial<PenaleProvvEditor>) {
    Object.assign(this, init);
  }
}
