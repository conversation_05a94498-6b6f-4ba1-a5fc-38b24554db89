import { ObjectType, Field } from '@nestjs/graphql';
import { ProvvedimentiStatoEnum } from '../../entities/enumaration/provvedimenti-stato.enum';
import { PenaleTRicorso } from '../penale_t_ricorso.model';

@ObjectType() //nome tabella su schema oracle
export class InfoProvvedimento {
  nrg?: number;
  numRaccoltaGenerale?: number;
  statoProvvedimento?: ProvvedimentiStatoEnum | null;
  idUdienza?: number;
  dataPubblicazione?: Date;
  dataMinuta?: Date;
  numRaccoltaGeneraleString?: string;
  ricorsoRiunito?: RiunitoDto;
  isPrincipalRicorsoRiunito?: boolean;

  public constructor(init?: Partial<InfoProvvedimento>) {
    Object.assign(this, init);
  }
}
@ObjectType()
export class RiunitoDto {
  anno: number | null;
  numero: number | null;
}
