import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleVerbaleRicorsoEntity } from '../consultazioni-graphql/entities/penale_verbale_ricorso.entity';
import { PenaleVerbaleUdienzaEntity } from '../consultazioni-graphql/entities/penale_verbale_udienza.entity';

@UfficioService()
export class VerbaleUdienzaService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  verbaleUdienza(): Promise<PenaleVerbaleUdienzaEntity[]> {
    return this.connection.getRepository(PenaleVerbaleUdienzaEntity).find();
  }

  verbaleUdienzaByIdUdienza(
    idUdienza: number,
  ): Promise<PenaleVerbaleUdienzaEntity | null> {
    return this.connection
      .getRepository(PenaleVerbaleUdienzaEntity)
      .findOneBy({ idUdienza: idUdienza });
  }
}
