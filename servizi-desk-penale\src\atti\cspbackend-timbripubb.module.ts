import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { CspbackendTimbripubbService } from './cspbackend-timbripubb.service';
import { UfficiModule } from 'src/uffici/uffici.module';

@Module({
  imports: [
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
    UfficiModule,
  ],
  providers: [CspbackendTimbripubbService],
  exports: [CspbackendTimbripubbService],
})
export class CspbackendTimbripubbModule {}
