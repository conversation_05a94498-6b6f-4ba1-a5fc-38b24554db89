import {<PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, PrimaryColumn} from 'typeorm';
import {PenaleParamEntity} from "./penale_param.entity";
import {PenaleTSentenzaEntity} from "./penale_t_sentenza.entity";

@Entity('PENALE_T_ESITOSENT') //nome tabella su schema oracle
export class PenaleTEsitoSentEntity {
  @PrimaryColumn({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'ID_ESITO1' })
  idEsito1: number;
  @Column({ name: 'ID_ESITO2' })
  idEsito2: number;
  @Column({ name: 'ID_ESITO3' })
  idEsito3: number;
  @Column({ name: 'ID_ESITO' })
  idEsito: number;
  @Column({ name: 'ID_SENT' })
  idSent: number;
  @Column({ name: 'ART28' })
  art28: string;

  @Column({ name: 'ART94' })
  art94: string;

  @OneToOne(() => PenaleTSentenzaEntity)
  @JoinColumn({ name: 'ID_SENT', referencedColumnName: 'idSent' })
  sentenza: PenaleTSentenzaEntity;

  public constructor(init?: Partial<PenaleTEsitoSentEntity>) {
    Object.assign(this, init);
  }
}
