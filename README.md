## Servizi web del Desk Cassazione Penale

### Documentazione di progetto

* [Architettura](/doc/architettura_cass_penale-Architettura.jpg)
* [Risorse utili](/doc/risorse_formazione.md) per la formazione (guide, tutorial, articoli...)
* [Best practices](/doc/best_practices.md)

### Build

* Installare Node.js (>= `16.15.0`)  
* Installare `yarn` (>= `1.22.19`) con `npm install -g yarn`
* Clonare il repository
* Entrare nella cartella del progetto   
* Eseguire `yarn install`
* Fare una copia locale di .env.example e chiamarla .env. Si può modificare in locale la configurazione in base alle esigenze di sviluppo.
* Installare le dipendenze con `yarn` oppure eseguire da `\servizi-desk-penale\` il comando `gradle build` che fa installazione e build
* Avviare il progetto in con `yarn start:dev` da terminale posizionandosi in `\servizi-desk-penale\servizi-desk-penale`

### Gestione librerie

TODO ...

### Gestione stato
TODO

### Invocazione servizi graphql
TODO

### Patching
* In gradle.properties specificare:
  version,
  rcVersion,
  releaseDate,
  elencoTicket.
* Se vi sono cambi relativamente a prodotti e/o schede effettuarli in `\servizi-desk-penale\distribution\patch\build.gradle`
* Per costruire la patch eseguire da terminale posizionandosi in `\servizi-desk-penale` il comando `gradle clean` e successivamente `gradle patch`
* Le patch sono disponibili in `\servizi-desk-penale\distribution\patch\build\distributions`

### Gestione dei log
Si sta utilizzando il log di nests che si trova dentro '@nestjs/common' 
- come implementare il log:
  importare la libreria:
   ```typescript
  import { Logger } from '@nestjs/common';
  ```
  inserire nella classe che si vuole utilizzare il log la creazione del Logger:
  ```typescript
     private logger = new Logger([NOME_CLASSE_O_COMPONENTE].name);
  ```
  esempio:
   ```typescript
  export class AuthGuard implements CanActivate {
      private logger = new Logger(AuthGuard.name);
  }
  ```
- modalità di loggate:
  * LOG: livello di log più alto viene visualizzato sempre e viene usato per
  loggare le informazioni d'importati del software,
  esempio viene usato quando stiamo firmando o depositando un documento oppure sto chiedendo l'utente
  loggato, Attenzione: non inserire nei log dati sensibili e inserire informazioni importati per tracciare l'operazione, nrg, idProvvedimento, ect
  esempio di loggata:
  ```typescript
    this.logger.log(
      `Richiesta di download archivio intestazione. idUdienza:${idUdienza}, ngrList: ${JSON.stringify(
      list,
     )}`,
    );
    ```
  IMPORTATE: se si logga un oggetto si deve implementare il tostring nell'oggetto o usare JSON.stringfy().
  * DEBUG: livello di log usato per il debug viene visualizzato solo nel caso in cui viene abiltiato il livello di 
    log debug usato molto spesso per stampare informazioni piu dettagliente in fase di sviluppo
  ```typescript
    this.logger.debug(
      `Richiesta di download archivio intestazione. idUdienza:${idUdienza}, ngrList: ${JSON.stringify(
      list,
     )}`,
    );
    ```
  * WARN: livello di log usato per avvisare che potrebbe causare un errore nelle
    operazioni successive oppure un errore gestito che non ha impatto sul software, 
    viene visualizzato solo se viene abilitato il livello warn 
  ```typescript
    this.logger.warn(
      `Richiesta di download archivio intestazione. idUdienza:${idUdienza}, ngrList: ${JSON.stringify(
      list,
     )}`,
    );
  ```
  * VERBOSE: viene visualizzato se viene abilitato il livello verbose,
    dovrebbe essere utilizzato per un registro molto dettagliato 
    che potrebbe causare un problema di prestazioni.
  ```typescript
    this.logger.verbose(
      `Richiesta di download archivio intestazione. idUdienza:${idUdienza}, ngrList: ${JSON.stringify(
      list,
     )}`,
    );
  ```
  * ERROR: Error viene utilizzato per registrare tutte le eccezioni non gestite. Questo viene in genere registrato 
    all'interno di un blocco catch al confine dell'applicazione. Viene sempre inserito nel file di log.
  ```typescript
   catch (e) {
      this.logger.error(
        `Non è possibile eliminare il provvedimento. idProvv:${idProvv}`,
        e,
      );
      throw new InternalServerErrorException(
        'Non è possibile eliminare il provvedimento: ' + e,
      );
    }
  ```
  Normalmente viene anche risollevata l'eccezione 

### Gestione Autentificazione e Protezione degli endpoints
Per la gestione dell'autentificazione viene usata la libreria di nestjs/jwt 
che controlla se il token e valido e controlla se è firmato correttamento tramite il jwts_uri di azure,
inoltre, viene usati AuthGuard di nestjs per proteggere tutti gli endpoint dell'applicativo
per ogni endpoint viene controllato se il token non è scaduto ed è ancora valido tramite il jwts_uri

TODO aggiungere come configurare il jwt module e l'authguard

### GESTIONE ERRORI
Come gestire gli errori lato BE:
  - vanno creare l'eccezionini, devono essere loggate nel log e risollevate fino all'endpoints
  - nei controller o nell'authGuard devono essere sempre inseriti nel messaggio un codice
    d'errore che inizia con CODE_ e il tipo di errore
  - esempio di gestione del codice nei metodi:
 ```typescript
     async richiestaModificaOrdinanzaOSentenza(
            provv: PenaleProvvedimentiEntity,
            idAutore: number,
            noteString: string,
    )
  {
      try {
          //TODO
      } catch (e) {
          this.logger.log(
              'Errore nella richiesta di modifica del presidente di una sentenza/ordinanza. provvedimento:',
              provv,
              e,
          );
          throw new ProvvedimentoNotFounfException(
              'Errore nella richiesta di modifica del presidente di una sentenza/ordinanza. ',
              e,
          );
      }
  }
 ```
- esempio di codice di gestione errore nei controller
 ```typescript
  @Post('/richiestaModifica/:idProvv')
      async richiestaModifica(
              @Param('idProvv') idProvv: string,
              @Body() presidenteDto: PresidenteDto,
      ){
      try {
        return await this.provvedimentoPresidenteService.richiestaModificaOrdinanzaOSentenza(
                provv,
                idAutore,
                presidenteDto.note || '',
        );
      } catch (e) {
          this.logger.error(`Provvedimento non trovato. idProvv: ${idProvv}`, e);
          throw new InternalServerErrorException('CODE_NOT_FOUND_PROVVEDIMENTO');
      }
  }
 ```


## Configurazione uffici.yaml (da creare dentro la cartella servizi-desk-penale)
 ```
datasource:
descUfficio: CDA Model Office
dbHost: dev3-db-cassazione
dbPort: 1521
dbType: oracle
dbServiceName: penale
dbUser: migra
dbPassword: migra
printSql: true 
```

## swagger
* creata configurazione per visualizzare le rest api
* nel .env è sta inserita una nuova conf `SWAGGER_PATH=/api/v1/desk-swagger` in path potete mettere quello che volete per
  per consultare le rest api dovete digitare host della `http/s://{host}:{port}/{GLOBAL_PATH}`
* esempio di localehost: `http://localhost:3001/api/v1/desk-swagger`
* esempio dockerpa2: `https://dockerpa2.netserv.it/api/v1/desk-swagger`
### come descrivere bene le api
* negli oggetti imput o DTO inserire il decoretor `@ApiProperty()` in modo che swagger capisce che si tratta di una attributo di una rest api.
  esempio:
```typescript
 export class TemplateInputDto {
  @ApiProperty()
  tipologia?: TipologiaTemplateTxtArea;
  @ApiProperty()
  description?: string;
  @ApiProperty()
  title?: string;
  @ApiProperty()
  content?: string;
  @ApiProperty()
  ordine?: number;
  }
  ```
* per rendere le api piu leggibili ogni api va taggata con un nome specifico `@ApiTags('{nomeTag}')`
  es:
```typescript
@Controller('presidente')
@ApiTags('provvedimenti-presidente')
export class ProvvedimentoPresidenteController {
    private logger = new Logger(ProvvedimentoPresidenteController.name);
}
```
