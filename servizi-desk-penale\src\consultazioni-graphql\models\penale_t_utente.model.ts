import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';

@ObjectType()
export class PenaleTUtente {
  @Field(type => ID)
  idUtente: number;
  @Field(type => String)
  identificativo: string;
  ufficio?: PenaleParam;
  @Field(type => String)
  nome?: string;
  @Field(type => String)
  cognome: string;
  @Field(type => Int)
  operatore: number;
  @Field(type => String)
  tipoUtente?: string;
  @Field(type => Int)
  idProfilo?: number;
  tipologia?: PenaleParam;
  uffAppartenenza?: PenaleParam;
  @Field(type => String)
  codiceFiscale?: string;

  public constructor(init?: Partial<PenaleTUtente>) {
    Object.assign(this, init);
  }
}
