import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleParam } from './penale_param.model';
import { PenaleParamEntity } from '../entities/penale_param.entity';
@ObjectType()
export class PenaleTProvved {
  @Field(type => ID)
  idProvvedimento: number;
  @Field(type => Int)
  numProvv: number;
  @Field(type => Int)
  nrg: number;
  @Field(type => Boolean)
  impugnato: boolean;
  @Field(type => Int)
  operatore: number;
  @Field(type => Date)
  oggi: Date;
  @Field(type => Date)
  dataProvv: Date;
  tipoProvv: PenaleParam;
  gradoProvv: PenaleParam;
}
