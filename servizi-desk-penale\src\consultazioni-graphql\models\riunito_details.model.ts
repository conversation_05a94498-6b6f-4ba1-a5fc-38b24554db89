import { ObjectType, Field, ArgsType, PartialType, Int } from '@nestjs/graphql';

@ObjectType() //nome tabella su schema oracle
export class RiunitoDetails {
  @Field(type => Int)
  idRicorsoUdienza: number;

  @Field(type => Int)
  nrg: number;
  @Field(type => Int)
  numero: number | null;
  @Field(type => Int)
  anno: number | null;

  public constructor(init?: Partial<RiunitoDetails>) {
    Object.assign(this, init);
  }
}
