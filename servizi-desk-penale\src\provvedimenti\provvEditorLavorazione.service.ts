import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleProvvEditorEntity } from '../consultazioni-graphql/entities/penale_provv_editor.entity';
import { ArgsProvvedimentoInput } from '../consultazioni-graphql/entities/dto/args-provvedimento.input';

@UfficioService()
export class ProvvEditorLavorazioneService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  async createProvvedimentoEditor(
    provvEditor: ArgsProvvedimentoInput,
    strutturato: boolean,
    provvId: string,
  ): Promise<string | null> {
    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(PenaleProvvEditorEntity)
      .values({
        idProvvedimento: provvId,
        createAt: new Date(),
        textLibero: this.convertTextForDB(provvEditor.text),
        textOscurato: this.convertTextForDB(provvEditor.textOscurato),
        introduzione: this.convertTextForDB(provvEditor.introduzione),
        finaleDeposito: this.convertTextForDB(provvEditor.finaleDeposito),
        motivoRicorso: this.convertTextForDB(provvEditor.motivoRicorso),
        strutturato: strutturato,
        oscurato: provvEditor.generaOscurato || false,
        pqm: this.convertTextForDB(provvEditor.pqm),
      })
      .returning(['idProvvedimentoEditor'])
      .execute();
    const x = result.raw[0];
    return x[0];
  }

  async getProvvedimentoEditorByIdProvv(provvId: string) {
    return await this.connection
      .getRepository(PenaleProvvEditorEntity)
      .findOne({
        where: {
          idProvvedimento: provvId,
        },
      });
  }

  convertTextForDB(text: string | undefined): null | string {
    let newText = '';
    if (text) {
      const splitted = text?.split('\n');
      for (const s of splitted) {
        if (s === '&nbsp;') {
          continue;
        }
        if (s.includes('<ol>') || s.includes('<ul>') || s.includes('</li>')) {
          newText += s;
          if (s.includes('</ol>') || s.includes('</ul>')) {
            newText += '\n';
          }
        } else {
          newText += s + '\n';
        }
      }
      newText = newText.replace('<head>', '');
      newText = newText.replace('</head>', '');
      newText = newText.replace('<body>', '');
      newText = newText.replace('</body>', '');
      newText = newText.replace(/<!--(.*?)-->/g, '');
      /*      newText = newText.replace(/<span(.*?)>/g, '');
      newText = newText.replace(/<\/span>/g, '');*/
      const number = newText.indexOf('<p');
      if (number < 0) {
        newText = '<p>' + newText + '</p>';
      }
    }

    if (!newText || newText.trim() == '') {
      newText = '<p></p>';
    }
    return newText == '' ? '<p></p>' : newText;
  }

  async isOscurato(idProvvedimento: string) {
    const result = await this.connection
      .getRepository(PenaleProvvEditorEntity)
      .findOne({
        select: ['oscurato'],
        where: {
          idProvvedimento: idProvvedimento,
        },
      });
    return result?.oscurato ?? false;
  }

  async updateProvvedimentoEditor(
    provvEditor: ArgsProvvedimentoInput,
    strutturato: boolean,
    idProvvedimentoEditor: string,
  ) {
    return await this.connection
      .createQueryBuilder()
      .update(PenaleProvvEditorEntity)
      .set({
        textLibero: this.convertTextForDB(provvEditor.text),
        textOscurato: this.convertTextForDB(provvEditor.textOscurato),
        introduzione: this.convertTextForDB(provvEditor.introduzione),
        motivoRicorso: this.convertTextForDB(provvEditor.motivoRicorso),
        finaleDeposito: this.convertTextForDB(provvEditor.finaleDeposito),
        oscurato: provvEditor.generaOscurato,
        strutturato: strutturato,
        pqm: this.convertTextForDB(provvEditor.pqm),
      })
      .where('idProvvedimentoEditor = :idProvvedimentoEditor', {
        idProvvedimentoEditor: idProvvedimentoEditor,
      })
      .execute();
  }

  async saveProvvEditor(provvEditor: PenaleProvvEditorEntity) {
    const provvEditorSave = await this.connection
      .getRepository(PenaleProvvEditorEntity)
      .save(provvEditor);
    return provvEditorSave;
  }
}
