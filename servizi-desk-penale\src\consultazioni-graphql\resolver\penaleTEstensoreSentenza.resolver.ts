import { Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleTEstensoriSentenza } from '../models/penale_t_estensori_sentenza.model';
import { EstensoreSentenzaService } from '../../estensore-sentenza/estensore-sentenza.service';
import { PenaleTSentenza } from '../models/penale_t_sentenza.model';
import { SentenzaService } from '../../sentenza/sentenza.service';

@Resolver(() => PenaleTEstensoriSentenza)
export class PenaleTEstensoreSentenzaResolver {
  constructor(
    private readonly estensoreSentenzaService: EstensoreSentenzaService,
    private readonly sentenzaService: SentenzaService,
  ) {}

  @Query(() => [PenaleTEstensoriSentenza], { name: 'PenaleTEstensoriSentenza' })
  async PenaleTEstensoriSentenza(): Promise<PenaleTEstensoriSentenza[]> {
    return this.estensoreSentenzaService.estensoriSentenza();
  }
  @ResolveField('sentenza', () => PenaleTSentenza, { nullable: true })
  async resolveSentenza(
    estensoreSentenza: PenaleTEstensoriSentenza,
  ): Promise<PenaleTSentenza | null> {
    if (estensoreSentenza?.idSentenza) {
      return this.sentenzaService.sentenza(estensoreSentenza.idSentenza);
    }
    return null;
  }
}
