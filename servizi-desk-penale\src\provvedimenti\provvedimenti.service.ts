import {
  Inject,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { DataSource, EntityManager, In, Not } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { CreateProvvedimentiInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti.input';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { ProvvedimentiOrigineEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-origine.enum';
import { ConfigService } from '@nestjs/config';
import { PlaceholderService } from './placeholder.service';
import * as FormData from 'form-data';
import axios from 'axios';
import { CreateProvvLavorazioneInput } from '../consultazioni-graphql/entities/dto/create-provvLavorazione.input';
import { AuthService } from '../auth/auth.service';
import { UfficiDBService } from '../uffici/ufficiDB.service';
import { exportFile } from '@ns/docx-tools-js';
import { readFileSync } from 'fs';
import { ArgsProvvedimentoInput } from '../consultazioni-graphql/entities/dto/args-provvedimento.input';
import { ProvvedimentiTipoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import { EsitiRuoloService } from '../esiti/esiti-ruolo.service';
import { SentenzaRuoloService } from '../esiti/sentenza-ruolo.service';
import { TRicorsoUdienzaService } from '../ricorso-udienza/t-ricorso-udienza.service';
import { EsitiService } from '../esiti/esiti.service';
import { SentenzaService } from '../sentenza/sentenza.service';
import { EsitiSentService } from '../esiti/esiti-sent.service';
import { PenaleParamEntity } from '../consultazioni-graphql/entities/penale_param.entity';
import { PenaleProvvLavFileEntity } from '../consultazioni-graphql/entities/penale_provv_lav_file.entity';
import { PenaleProvvEditorEntity } from '../consultazioni-graphql/entities/penale_provv_editor.entity';
import { PenaleProvvChangeStatusEntity } from '../consultazioni-graphql/entities/penale_provv_change_status.entity';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { ServiziDepositoException } from '../exceptions/servizi-deposito.exception';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';
import { PenaleTEsitoSentEntity } from '../consultazioni-graphql/entities/penale_t_esito_sent.entity';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { CollegioService } from '../collegio/collegio.service';
import { MagistratiService } from '../magistrati/magistrati.service';
import {
  CspbackendTimbripubbEntity,
  TipoOscuramentoEnum,
} from '../consultazioni-graphql/entities/cspbackend_timbripubb.entity';
import { RicercaSentenzaService } from '../ricerca-sentenza/ricerca-sentenza.service';
import { SentenzaInfoRidotta } from './entities/sentenza-info-ridotta';

@UfficioService()
export class ProvvedimentiService {
  private logger = new Logger(ProvvedimentiService.name);
  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private configService: ConfigService,
    private placeholderService: PlaceholderService,
    private authService: AuthService,
    private readonly esitiRuoloService: EsitiRuoloService,
    private readonly ruoloService: SentenzaRuoloService,
    private readonly ufficiDBService: UfficiDBService,
    private readonly esitiService: EsitiService,
    private readonly esitiSentService: EsitiSentService,
    private readonly sentenzaService: SentenzaService,
    private readonly ricorsoUdienza: TRicorsoUdienzaService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly collegioService: CollegioService,
    private readonly magistratiService: MagistratiService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
  ) { }

  async createProvvedimentoDocx(
    nrg: number,
    idUdienza: number,
    argsPlaceholder: ArgsProvvedimentoInput,
  ) {
    const file = readFileSync(
      'dist/templates/templateProvvedimento.json',
      'utf-8',
    );
    const ricUdien = await this.connection
      .getRepository(PenaleTRicorsoUdienzaEntity)
      .findOne({
        where: {
          nrg: nrg,
          idUdienza: idUdienza,
        },
      });
    if (ricUdien?.idRicudien) {
      const variables = await this.placeholderService.resolveVariables(
        nrg,
        ricUdien?.idRicudien,
        idUdienza,
        file,
        argsPlaceholder,
      );
      variables['@@@args.visibNomePopolo@@@'] =
        argsPlaceholder['tipologiaProvvedimento'] === 'MINUTA_SENTENZA';
      const json = JSON.parse(file);
      return await exportFile(json, variables);
    }
    this.logger.error('Errore nella creazione del provvedimento');
    throw new GenericErrorException(
      'Errore nella creazione del provvedimento, idRicUdien not trovato',
      CodeErrorEnumException.RICORSO_UDIENZA_NOT_FOUND,
    );
  }

  async createTemplateDocx(
    idRicUdien: number,
    nrg: number,
    idUdienza: number,
    argsPlaceholder: ArgsProvvedimentoInput,
  ) {
    const file = readFileSync(
      'dist/templates/templateProvvedimentoDocx.json',
      'utf-8',
    );
    const variables = await this.placeholderService.resolveVariables(
      nrg,
      idRicUdien,
      idUdienza,
      file,
      argsPlaceholder,
      false,
    );
    variables['@@@db.partiProvvedimento@@@'] = variables[
      '@@@db.partiProvvedimento@@@'
    ].replace('AUTOMATICO TEST', 'Automatico Test');
    variables['@@@args.visibNomePopolo@@@'] =
      argsPlaceholder['tipologiaProvvedimento'] === 'MINUTA_SENTENZA' ||
      argsPlaceholder['tipologiaProvvedimento'] === 'SENTENZA';
    const json = JSON.parse(file);
    return await exportFile(json, variables);
  }

  async generaDatiAtto(
    provvLavorazione: CreateProvvLavorazioneInput,
  ): Promise<Buffer | null> {
    this.logger.log(
      `Inizio generazione del dato atto xml con args:${JSON.stringify(
        provvLavorazione.argsProvvedimento,
      )},origine: ${provvLavorazione.origine},allegatoOscurato: ${provvLavorazione.allegatoOscurato
      }`,
    );
    const args = provvLavorazione.argsProvvedimento;
    const form = new FormData();
    form.append(
      'infoProvvedimento',
      Buffer.from(
        JSON.stringify({
          numeroFascicolo: args.numRuolo,
          annoFascicolo: args.anRuolo,
          tipoProvvedimento: args.tipologiaProvvedimento,
          allegatoOscurato: provvLavorazione.allegatoOscurato
            ? provvLavorazione.allegatoOscurato
            : false,
        }),
      ),

      { contentType: 'application/json' },
    );
    const codiceFiscaleMittente = await this.authService.getCurrentUser();
    const ufficioDB = await this.ufficiDBService.getFirstUfficio();
    if (!ufficioDB?.codiceUfficio) {
      this.logger.warn('Ufficio non trovato');
      throw new InternalServerErrorException('ufficio non trovato');
    }
    form.append(
      'infoBusta',
      Buffer.from(
        JSON.stringify({
          codiceFiscaleMittente: codiceFiscaleMittente,
          codiceUfficioDestinatario: ufficioDB?.codiceUfficio,
          // codiceUfficioDestinatario: f.idUfficio,
          ruoloMittente: 'MAG',
          idMsg: provvLavorazione.nrg,
        }),
      ),
      { contentType: 'application/json' },
    );
    const endpoint = '/provvedimenti/generaDatiAtto';

    const urlServiziDepositi =
      this.configService.get('app.depositiUrl') + endpoint;
    this.logger.debug(`call servizi depositi:${urlServiziDepositi}`);
    return await axios
      .post(urlServiziDepositi, form, {
        headers: form.getHeaders(),
        responseType: 'arraybuffer',
      })
      .then(response => {
        if (response.status === 200) {
          this.logger.log(
            `Fine generazione del dato atto xml con args:${JSON.stringify(
              provvLavorazione.argsProvvedimento,
            )}`,
          );
          //@ts-ignore
          const errors = response?.errors || new Array<any>();
          if (errors?.length > 0) {
            this.logger.error(
              `Errore nel deposito del provvedimento su servizi depositi. idUdienza:${provvLavorazione.idUdienza}, nrg:${provvLavorazione?.nrg}, ${response.status}: ${response.statusText}, errors: ${errors}`,
            );
            throw new ServiziDepositoException('DEPOSITO_ERROR');
          }
          if (response.data && response.data != '') {
            return response.data;
          }
          this.logger.error(
            `Errore nel deposita provvedimento su servizi depositi con idCat null. idUdienza:${provvLavorazione.idUdienza}, nrg:${provvLavorazione?.nrg}, ${response.status}: ${response.statusText}, errors: ${errors}`,
          );
          throw new ServiziDepositoException(
            'ID_DEPOSITO_NULL',
            undefined,
            CodeErrorEnumException.ID_DEPOSITO_NULL,
          );
        }
      })
      .catch(err => {
        this.logger.error(`Errore nella generazione del dato atto xml.`, err);
        if (err.response != undefined) {
          throw new ServiziDepositoException(err.response.data);
        } else if (
          err?.errors?.length > 0 &&
          err?.errors?.some((errorMessage: any) => {
            return errorMessage.message
              .toLowerCase()
              .includes('connect ECONNREFUSED'.toLowerCase());
          })
        ) {
          this.logger.error('Servizi depositi non disponibile', err?.errors);
          throw new ServiziDepositoException('DEPOSITO_ERROR_CONNECTION');
        } else {
          throw new ServiziDepositoException('noServiceError');
        }
      });
  }

  provvedimenti(): Promise<PenaleProvvedimentiEntity[]> {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find();
  }

  provvedimentoById(
    idProvvedimenti: string,
    stato?: ProvvedimentiStatoEnum,
  ): Promise<PenaleProvvedimentiEntity | null> {
    return this.connection.getRepository(PenaleProvvedimentiEntity).findOne({
      where: {
        idProvvedimento: idProvvedimenti,
        ...(stato ? { stato } : {}),
      },
    });
  }
  /**
   * Restituisce la lista deigli stati duplicati eliminando gli id duplicati e escludendo l'id iniziale.
   * @param idsAllProvv
   * @param idProvvDaEscludere
   * @private
   */
  async getAllStatusEliminaDuplicatiAndEcludeIdIniziale(
    idsAllProvv: Array<string>,
    idProvvDaEscludere: string,
  ) {
    const provv = await this.provvedimentoById(idProvvDaEscludere);
    if (!provv?.nrg) {
      throw new NotFoundException('Provvedimento non trovato');
    }
    idsAllProvv = idsAllProvv.filter(function (elem, index, self) {
      return index === self.indexOf(elem);
    });
    const indexProvvDaEcludere = idsAllProvv.indexOf(idProvvDaEscludere);
    if (indexProvvDaEcludere >= 0) {
      const elementoEcluso = idsAllProvv.splice(indexProvvDaEcludere, 1);
      this.logger.debug(`elemento escluso:${elementoEcluso}`);
    }
    if (idsAllProvv.length) {
      const ricorsivoCheckAlProvv =
        await this.changeStatusService.changeStatusByIdsProvvedimento(
          idsAllProvv,
          provv.nrg,
        );
      if (ricorsivoCheckAlProvv) {
        return ricorsivoCheckAlProvv;
      }
    }

    return [];
  }
  async deleteProvvDuplicato(idProvv: string) {
    this.logger.log(
      `Inizio cancellazione provvedimento duplicato. idProvv:${idProvv}`,
    );
    const provv = await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .findOne({
        where: {
          idProvvedimento: idProvv,
          stato: ProvvedimentiStatoEnum.CODA_DI_FIRMA,
        },
      });

    if (provv?.idProvvedimento) {
      await this.connection.getRepository(PenaleProvvLavFileEntity).delete({
        idProvvedimento: provv.idProvvedimento,
      });
      await this.connection
        .getRepository(PenaleProvvEditorEntity)
        .delete({ idProvvedimento: provv.idProvvedimento });
      await this.connection
        .getRepository(PenaleProvvChangeStatusEntity)
        .delete({
          idProvvedimento: provv.idProvvedimento,
        });
      await this.connection
        .getRepository(PenaleProvvedimentiEntity)
        .delete({ idProvvedimento: provv.idProvvedimento });
      this.logger.log(
        `Fine cancellazione provvedimento duplicato. idProvv:${idProvv}`,
      );
      return provv;
    }
    this.logger.error('Errore nella cancellazione del provvedimento');
    throw new GenericErrorException(
      'Non è possibile eliminare il provvedimento',
      CodeErrorEnumException.PROVV_NOT_DELETED,
    );
  }

  provvedimentoByIdUdien(
    idUdien: number,
  ): Promise<PenaleProvvedimentiEntity[] | null> {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: { idUdienza: idUdien },
    });
  }

  async createProvvedimento(
    provvedimento: CreateProvvedimentiInput,
  ): Promise<string | null> {
    this.logger.log(
      `salvataggi dei dati del provvedimento. idUdienza:${provvedimento.idUdienza}, nrg: ${provvedimento.nrg}`,
    );
    const idAutore = await this.authService.getCurrentId();
    const result = await this.connection
      .createQueryBuilder()
      .insert()
      .into(PenaleProvvedimentiEntity)
      .values({
        idUdienza: provvedimento.idUdienza,
        nrg: provvedimento.nrg,
        tipo: provvedimento.tipo,
        stato: provvedimento.stato ?? ProvvedimentiStatoEnum.IN_BOZZA,
        dataUltimaModifica: new Date(),
        idAutore: idAutore,
        origine: provvedimento.origine ?? ProvvedimentiOrigineEnum.LOCALE,
        nomeDocumento: 'ddddd',
        dataDeposito: provvedimento.dataDeposito,
        dataDecisione: new Date(),
      })
      .returning(['idProvvedimento'])
      .execute();
    const x = result.raw[0];
    this.logger.log(`Provvedimento salvato con id:${x[0]}`);
    return x[0];
  }

  async saveCopyProvvedimento(
    provvedimento: PenaleProvvedimentiEntity,
    entityManager?: EntityManager,
  ): Promise<PenaleProvvedimentiEntity | null> {
    provvedimento.dataUltimaModifica = new Date();
    let result: PenaleProvvedimentiEntity | null;
    if (entityManager) {
      result = await entityManager
        .getRepository(PenaleProvvedimentiEntity)
        .save(provvedimento);
    } else {
      result = await this.connection
        .getRepository(PenaleProvvedimentiEntity)
        .save(provvedimento);
    }

    this.logger.log(result);
    return result;
  }

  async updateProvvedimento(idProvvedimento: string, nomeFile: string) {
    return await this.connection
      .createQueryBuilder()
      .update(PenaleProvvedimentiEntity)
      .set({ nomeDocumento: nomeFile })
      .where('idProvvedimento= :idProvvedimento', {
        idProvvedimento: idProvvedimento,
      })
      .execute();
  }

  async updateDataUltimaModificaProvvedimento(idProvvedimento: string) {
    return await this.connection
      .createQueryBuilder()
      .update(PenaleProvvedimentiEntity)
      .set({ dataUltimaModifica: new Date() })
      .where('idProvvedimento= :idProvvedimento', {
        idProvvedimento: idProvvedimento,
      })
      .execute();
  }

  async updateDataDecisioneProvvedimento(
    idProvvedimento: string,
    dataDecisione: Date,
  ) {
    return await this.connection
      .createQueryBuilder()
      .update(PenaleProvvedimentiEntity)
      .set({ dataDecisione: dataDecisione })
      .where('idProvvedimento= :idProvvedimento', {
        idProvvedimento: idProvvedimento,
      })
      .execute();
  }

  async updateStatoProvvedimento(
    idProvvedimento: string,
    stato: ProvvedimentiStatoEnum,
    entityManager?: EntityManager,
  ) {
    if (entityManager) {
      return entityManager.update(PenaleProvvedimentiEntity, idProvvedimento, {
        stato: stato,
        dataUltimaModifica: new Date(),
      });
    }
    return await this.connection.manager.update(
      PenaleProvvedimentiEntity,
      idProvvedimento,
      { stato: stato, dataUltimaModifica: new Date() },
    );
  }

  async provvedimentoByIdUdienAndNrgModify(idUdien: number, nrg: number) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).findOne({
      where: { idUdienza: idUdien, nrg: nrg },
      order: {
        dataUltimaModifica: 'DESC',
      },
    });
  }
  async provvedimentoByIdUdienAndNrgModifyAndIdAutore(
    idUdien: number,
    nrg: number,
    idAutore: number,
  ) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).findOne({
      where: { idUdienza: idUdien, nrg: nrg, idAutore: idAutore },
      order: {
        dataUltimaModifica: 'DESC',
      },
    });
  }

  async provvedimentoByNrgAndIdUdien(idUdien: number, nrg: number) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: { idUdienza: idUdien, nrg: nrg },
    });
  }

  async getTipoProvvedimento(idUdien: number, nrg: number) {
    const idRicudien =
      await this.ricorsoUdienza.getIdRicUdienzaByNrgAndIdUdienza(idUdien, nrg);
    if (idRicudien > 0) {
      const esito = await this.esitiService.esito(idRicudien);
      if (esito?.idEsito) {
        const tesitoSent = await this.esitiSentService.esitoSent(esito.idEsito);
        if (tesitoSent?.idSent) {
          const sentenza = await this.sentenzaService.sentenza(
            tesitoSent?.idSent,
          );
          if (sentenza?.idTipoSent)
            return this.calcolaTipoDiSentenza(sentenza.idTipoSent);
        }
      }
    }
    return ProvvedimentiTipoEnum.ORDINANZA;
  }

  async checkTipoDiProvvedimento(
    tipoProvv: ProvvedimentiTipoEnum,
    idUdienza: number,
    idEstensore: number,
    cf: string,
  ): Promise<ProvvedimentiTipoEnum> {
    const isEstensorePresidente = await this.isPresidenteEstensore(
      cf,
      idUdienza,
      idEstensore,
    );
    switch (tipoProvv) {
      case ProvvedimentiTipoEnum.MINUTA_ORDINANZA:
      case ProvvedimentiTipoEnum.ORDINANZA:
        return isEstensorePresidente
          ? ProvvedimentiTipoEnum.ORDINANZA
          : ProvvedimentiTipoEnum.MINUTA_ORDINANZA;
      case ProvvedimentiTipoEnum.MINUTA_SENTENZA:
      case ProvvedimentiTipoEnum.SENTENZA:
        return isEstensorePresidente
          ? ProvvedimentiTipoEnum.SENTENZA
          : ProvvedimentiTipoEnum.MINUTA_SENTENZA;
      default:
        throw new GenericErrorException(
          'Non si tratta di una minuta o ordinanza sentenza.',
          CodeErrorEnumException.NO_MANAGE_TIPO_PROVVEDIMENTO,
        );
    }
  }
  async isPresidenteEstensore(
    cf: string,
    idUdien: number,
    idEstensore: number,
  ) {
    const magisIds = await this.magistratiService.getMagistratiIdByCf(cf);
    this.logger.debug('lista dei magistrati', magisIds);
    const listaPresidenti =
      await this.collegioService.collegioByIdUdienzaAndPresidente(idUdien);
    if (!listaPresidenti || listaPresidenti?.length === 0) {
      throw new GenericErrorException(
        'Il collegio presente non ha presidenti',
        CodeErrorEnumException.COLLEGIO_ERROR,
      );
    }

    if (!listaPresidenti || listaPresidenti?.length > 1) {
      throw new GenericErrorException(
        'Il collegio presente ha più di un presidente',
        CodeErrorEnumException.COLLEGIO_ERROR,
      );
    }
    return (
      magisIds &&
      magisIds.some(
        idMagis =>
          idMagis == listaPresidenti[0].idMagis && idMagis == idEstensore,
      )
    );
  }
  async getTipoProvvedimentoAndSemplificataByNrg(
    idudien: number,
    nrg: number,
  ): Promise<SentenzaInfoRidotta> {
    const sentenza = await this.connection
      .getRepository(PenaleTSentenzaEntity)
      .createQueryBuilder('SENTENZA')
      .leftJoin(
        PenaleTEsitoSentEntity,
        'ESITOSENT',
        'SENTENZA.ID_SENT = ESITOSENT.ID_SENT',
      )
      .innerJoinAndMapOne(
        'SENTENZA.esito',
        PenaleTEsitoEntity,
        'ESITO',
        'ESITO.ID_ESITO = ESITOSENT.ID_ESITO',
      )
      .leftJoin(
        PenaleTRicorsoUdienzaEntity,
        'RICUDIEN',
        'RICUDIEN.ID_RICUDIEN = ESITO.ID_RICUDIEN',
      )
      .innerJoinAndMapOne(
        'SENTENZA.idTipoSent',
        PenaleParamEntity,
        'PP',
        'SENTENZA.ID_TIPOSENT = PP.ID_PARAM',
      )
      .where('RICUDIEN.NRG = :NRG AND RICUDIEN.ID_UDIEN = :IDUDIEN', {
        NRG: nrg,
        IDUDIEN: idudien,
      })
      .getOne();
    if (sentenza?.idTipoSent) {
      return {
        tipoProvvedimento: this.calcolaTipoDiSentenza(sentenza.idTipoSent),
        idEstensore: sentenza.idEstentore,
        sentenza: sentenza.sentenza,
        semplificata: sentenza?.esito?.semplificata
          ? sentenza?.esito?.semplificata == '1'
          : false,
      };
    }
    this.logger.error('errore nella query getTipoProvvAndSemplicata');
    throw new GenericErrorException(
      'Sentenza non trovt e non si puo calcolare il tipo della sentenza.',
      CodeErrorEnumException.CALCOLO_TIPO_SENT_ERROR,
    );
  }

  calcolaTipoDiSentenza(
    sentenzaRuolo: PenaleParamEntity | null,
  ): ProvvedimentiTipoEnum {
    this.logger.log(
      `Inizio calcolo tipo di sentenza non gestito. siglaTipoSentenza:${sentenzaRuolo}`,
    );
    let tipoProv = ProvvedimentiTipoEnum.ORDINANZA;
    if (sentenzaRuolo) {
      switch (sentenzaRuolo.sigla) {
        case 'SE':
          tipoProv = ProvvedimentiTipoEnum.SENTENZA;
          break;
        case 'OR':
          tipoProv = ProvvedimentiTipoEnum.ORDINANZA;
          break;
        default:
          this.logger.error(
            `Tipo di sentenza non gestito. siglaTipoSentenza:${JSON.stringify(
              sentenzaRuolo,
            )}`,
          );
          throw new GenericErrorException(
            'Tipo di sentenza non gestito',
            CodeErrorEnumException.TIPO_SENT_NOT_MANAGER,
          );
      }
    }
    this.logger.log(`Fine calcolo tipo di sentenza non gestito`);
    return tipoProv;
  }

  async getProvvedimentoOscuratoSicSingle(
    idUdien: number,
    nrg: number,
  ): Promise<boolean> {
    this.logger.log(
      `Inizio calcolo dell'oscuramento del sic per idRicUdien:${idUdien}; nrg:${nrg}`,
    );
    const ricUdin = await this.ricorsoUdienza.ricorsoUdienzaByNrgAndIdUdienza(
      idUdien,
      nrg,
    );
    if (ricUdin?.idRicudien) {
      const esitoRuolo = await this.esitiService.esito(ricUdin.idRicudien);
      if (esitoRuolo?.privacyParam && esitoRuolo.privacyParam?.sigla == 'SI') {
        this.logger.log(
          `Fine calcolo dell'oscuramento del sic per idRicUdien:${idUdien}; nrg:${nrg}; privacy:true`,
        );
        return true;
      }
    }
    this.logger.log(
      `Fine calcolo dell'oscuramento del sic per idRicUdien:${idUdien}; nrg:${nrg}; privacy:false`,
    );
    return false;
  }
  async getProvvedimentoOscuratoSicComplessivo(
    idUdien: number,
    nrg: number | null,
  ): Promise<boolean> {
    if (nrg && nrg > 0) {
      this.logger.log(
        `Inizio calcolo dell'oscuramento complessivo del sic per idRicUdien:${idUdien}; nrg:${nrg}`,
      );
      const ricUdin = await this.ricorsoUdienza.ricorsoUdienzaByNrgAndIdUdienza(
        idUdien,
        nrg,
      );
      if (ricUdin?.idRicudien) {
        const esitoRuolo = await this.esitiService.esito(ricUdin.idRicudien);
        if (
          esitoRuolo?.privacyParam &&
          esitoRuolo.privacyParam?.sigla == 'SI'
        ) {
          this.logger.log(
            `Fine calcolo dell'oscuramento del sic complessivo per idRicUdien:${idUdien}; nrg:${nrg}; privacy:true`,
          );
          return true;
        }
        const ricorsoRiunitoViewByIdRicUdienPadre =
          await this.ricercaSentenzaService.getRicorsoRiunitoViewByIdRicUdienPadre(
            ricUdin?.idRicudien,
          );
        if (
          ricorsoRiunitoViewByIdRicUdienPadre &&
          ricorsoRiunitoViewByIdRicUdienPadre?.length > 0
        ) {
          const idsRicUdien = ricorsoRiunitoViewByIdRicUdienPadre.map(
            ric => ric.idRicUdien,
          );
          const esitiListRiuniti = await this.esitiService.esitoByidsRicUdien(
            idsRicUdien,
          );
          const privacyRiuniti = esitiListRiuniti?.some(
            ricRiunito =>
              ricRiunito?.privacyParam &&
              ricRiunito.privacyParam?.sigla == 'SI',
          );
          this.logger.log(
            `Fine calcolo dell'oscuramento del sic complessivo per idRicUdien:${idUdien}; nrg:${nrg}; privacy:${privacyRiuniti}`,
          );
          return privacyRiuniti;
        }
      }
    }
    this.logger.log(
      `Fine calcolo dell'oscuramento del sic complessivo per idRicUdien:${idUdien}; nrg:${nrg}; privacy:false`,
    );
    return false;
  }

  async updateTipoAndStatusProvvedimento(
    idProvvedimento: string,
    tipoProv: ProvvedimentiTipoEnum,
    stato: ProvvedimentiStatoEnum,
    entityManager?: EntityManager,
  ) {
    if (entityManager) {
      return entityManager.update(PenaleProvvedimentiEntity, idProvvedimento, {
        tipo: tipoProv,
        stato: stato,
        dataUltimaModifica: new Date(),
      });
    }
    return await this.connection
      .createQueryBuilder()
      .update(PenaleProvvedimentiEntity)
      .set({ tipo: tipoProv, stato: stato, dataUltimaModifica: new Date() })
      .where('idProvvedimento= :idProvvedimento', {
        idProvvedimento: idProvvedimento,
      })
      .execute();
  }

  async updateProvvedimentoAll(provvCopiato: PenaleProvvedimentiEntity) {
    provvCopiato.dataUltimaModifica = new Date();
    await this.connection
      .createQueryBuilder()
      .update(PenaleProvvedimentiEntity)
      .set(provvCopiato)
      .where('idProvvedimento= :idProvvedimento', {
        idProvvedimento: provvCopiato.idProvvedimento,
      })
      .execute();
    return await this.provvedimentoById(provvCopiato.idProvvedimento);
  }

  async provvedimentoByNrgAndAutore(nrg: number, currentId: number) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: { nrg: nrg, idAutore: currentId },
    });
  }
  /*async provvedimentoByIdUdienAndNrgAndAutore(
    idUdien: number,
    nrg: number,
    currentId: number,
  ) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: { nrg: nrg, idUdienza: idUdien, idAutore: currentId },
    });
  }*/

  async provvedimentoByIdUdienAndNrgAndAutore(
    idUdien: number,
    nrg: number,
    currentId: number,
  ) {
    this.logger.debug('Eseguo la query provvedimentoByIdUdienAndNrgAndAutore');
    // TODO Da verificare con presidente estensore
    /*.andWhere('provvedimenti.tipo IN (:...types)', {
          types: [
            ProvvedimentiTipoEnum.MINUTA_ORDINANZA,
            ProvvedimentiTipoEnum.MINUTA_SENTENZA,
          ],
        })*/
    return await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .createQueryBuilder('provvedimenti')
      .where('provvedimenti.nrg = :nrg', { nrg })
      .andWhere('provvedimenti.idUdienza = :idUdien', { idUdien })
      .andWhere(
        '(provvedimenti.idAutore = :currentId OR provvedimenti.modificaPresidente = 1 )',
        { currentId },
      )
      .andWhere('provvedimenti.stato NOT IN (:...stati)', {
        stati: [ProvvedimentiStatoEnum.BOZZA_PRESIDENTE],
      })
      .getMany();
  }

  async provvedimentoByNrgPerPresidente(nrg: number) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: {
        nrg: nrg,
        stato: Not(
          In([
            ProvvedimentiStatoEnum.IN_BOZZA,
            ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE,
            ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
          ]),
        ),
      },
    });
  }
  async provvedimentoByIdUdienAndNrgPerPresidente(
    idUdienza: number,
    nrg: number,
  ) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).find({
      where: {
        nrg: nrg,
        idUdienza: idUdienza,
        stato: Not(
          In([
            ProvvedimentiStatoEnum.IN_BOZZA,
            ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE,
            ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL,
          ]),
        ),
      },
    });
  }
  async provvedimentoByIdUdienAndNrg(idUdienza: number, nrg: number) {
    const porvvList = await this.connection
      .getRepository(PenaleProvvedimentiEntity)
      .find({
        select: {
          idProvvedimento: true,
          origine: true,
        },
        where: {
          nrg: nrg,
          idUdienza: idUdienza,
        },
        order: {
          dataUltimaModifica: 'asc',
        },
      });
    return porvvList?.length > 0 ? porvvList : null;
  }

  async isOscuratoEditor(idsProvv: string[]) {
    const porvvList = await this.connection
      .getRepository(PenaleProvvEditorEntity)
      .findOne({
        select: {
          idProvvedimentoEditor: true,
          oscurato: true,
        },
        where: {
          idProvvedimento: In(idsProvv),
        },
        order: {
          createAt: 'DESC',
        },
      });
    return porvvList?.oscurato || false;
  }

  /* serve a verificare il tipoOscuramento per uno specifico record
   * identificato da idUdienza e nrg nel database CspbackendTimbripubbEntity.*/
  async checkOscuramentoType(idUdienza: number, nrg: number) {
    const porvvList = await this.connection
      .getRepository(CspbackendTimbripubbEntity)
      .findOne({
        select: {
          idUdien: true,
          nrg: true,
          tipoOscuramento: true,
        },
        where: {
          idUdien: idUdienza,
          nrg: nrg,
        },
      });
    if (!porvvList) {
      return false;
    }
    return !(
      porvvList?.tipoOscuramento == null ||
      porvvList?.tipoOscuramento == TipoOscuramentoEnum.NESSUNO
    );
  }

  async changeProvvFromRedazioneToUpload(provv: PenaleProvvedimentiEntity) {
    provv.dataUltimaModifica = new Date();
    provv.origine = ProvvedimentiOrigineEnum.LOCALE;
    await this.connection
      .createQueryBuilder()
      .update(PenaleProvvedimentiEntity)
      .set(provv)
      .where('idProvvedimento= :idProvvedimento', {
        idProvvedimento: provv.idProvvedimento,
      })
      .execute();
  }

  provvedimentoLastProvvedimentoByIdUdienAndNrg(idUdien: number, nrg: number) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).findOne({
      where: { idUdienza: idUdien, nrg: nrg, lastModified: true },
    });
  }

  async lockProvvedimento(idProvv: string) {
    if (idProvv) {
      await this.connection
        .createQueryBuilder()
        .update(PenaleProvvedimentiEntity)
        .set({ provvedimentoLock: true })
        .where({
          idProvvedimento: idProvv,
        })
        .execute();
    }
  }
  async lockProvvedimenti(idProvvs: Array<string>) {
    if (idProvvs?.length) {
      await this.connection
        .createQueryBuilder()
        .update(PenaleProvvedimentiEntity)
        .set({ provvedimentoLock: true })
        .where({
          idProvvedimento: In(idProvvs),
        })
        .execute();
    }
  }
  async unLockProvvedimento(idProvv: string) {
    if (idProvv) {
      await this.connection
        .createQueryBuilder()
        .update(PenaleProvvedimentiEntity)
        .set({ provvedimentoLock: false })
        .where({
          idProvvedimento: idProvv,
        })
        .execute();
    }
  }
  async unlockProvvedimenti(idProvvs: Array<string>) {
    if (idProvvs?.length > 0) {
      await this.connection
        .createQueryBuilder()
        .update(PenaleProvvedimentiEntity)
        .set({ provvedimentoLock: false })
        .where({
          idProvvedimento: In(idProvvs),
        })
        .execute();
    }
  }
  async getProvvedimentoByIdProvvedimento(idProvv: string) {
    return this.connection.getRepository(PenaleProvvedimentiEntity).findOne({
      where: { idProvvedimento: idProvv },
    });
  }
}
