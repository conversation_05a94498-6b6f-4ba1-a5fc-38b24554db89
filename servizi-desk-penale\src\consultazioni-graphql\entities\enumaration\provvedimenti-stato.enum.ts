import { registerEnumType } from '@nestjs/graphql';

export enum ProvvedimentiStatoEnum {
  //STATO INIZIALE DEL RELATORE
  IN_BOZZA = 'BOZZA',
  //STATO BOZZA PRESIDENTE
  BOZZA_PRESIDENTE = 'BOZZA_PRESIDENTE',
  //STATO UTILIZZATO dall'estensore quando inserisce il provvedimento nella coda di firma
  IN_CODE_FIRMA_REL = 'IN_CODE_FIRMA_REL',
  //Quando viene inviato in calcelleria dal relatore
  INVIATO_IN_CANCELLERIA_RELATORE = 'INVIATO_IN_CANCELLERIA_RELATORE',
  // passa a questo stato quando la cancelleria rifiuta un provvedimento che invia il relatore o quando arriva il provvedimento firmato dal presidente
  BUSTA_RIFIUTATA = 'BUSTA_RIFIUTATA',
  // passa a questo stato quando la cancelleria rifiuta un provvedimento che invia il presidente
  BUSTA_RIFIUTATA_AL_PRESIDENTE = 'BUSTA_RIFIUTATA_AL_PRESIDENTE',
  //per relatore è la minuta accetta e inviata al presidente, per il presidente è lo stato minuta pervenuta
  MINUTA_ACCETTATA = 'MINUTA_ACCETTATA',
  // se il presidente richiede di modificare il deposito
  MINUTA_DA_MODIFICARE = 'MINUTA_DA_MODIFICARE',
  //Sato solo per il presidente quando esso clicca su verificato
  CODA_DI_FIRMA = 'CODA_DI_FIRMA',
  // se il relatore modifica una minuta la minuta passa allo stato minuta modificata
  MINUTA_MODIFICATA = 'MINUTA_MODIFICATA',

  // il presidente  firma e deposita il provvedimento
  INVIATO_IN_CANCEL_PRESIDENTE = 'INVIATO_IN_CANCEL_PRESIDENTE',
  // se la cancelleria accetta la busta dopo che lo stato era in INVIATO_IN_CANCEL_PRESIDENTE passa a pubblicato
  PUBBLICATA = 'PUBBLICATA',
  // se la pubblicazione da parte della cancelleria sul glc non riesce allora passa in questo stato.
  ERRORE_DI_PUBBLICAZIONE = 'ERRORE_DI_PUBBLICAZIONE',

  //nuovo stato MINUTA_MODIFICATA_PRESIDENTE quando il presidente modifica la minuta depositata dal relatore
  MINUTA_MODIFICATA_PRESIDENTE = 'MINUTA_MODIFICATA_PRESIDENTE',
  // stato usato per i ricorsi riuniti
  RIUNITO = 'RIUNITO',

  /**
   * STATI DEL SIC se il provv viene manipolato dal SIC
   */
  // nuovo stato del provvedimento quando questo viene depositata la sentenza dal SIC
  PROVV_DEPOSITATO_SIC = 'PROVV_DEPOSITATO_SIC',

  // nuovo stato del provvedimento quando la minuta viene depositato dal SIC
  MINUTA_DEPOSITATA_SIC = 'MINUTA_DEPOSITATA_SIC',
  // nuovo stato del provvedimento quando viene inserito numero raccolta generale dal SIC
  PUBBLICATO_SIC = 'PUBBLICATO_SIC',

  /**
   * DA INIZIATO GLI STATI FITTIZI cioè sosto stati che non vegono pesistiti sul DB va vengono usati solo per alcune query+
   *
   */
  // INDICA UN provvedimento che si trovata nello stato null cioè non è stato mai redetto quindi non esiste un provvedimento
  DA_REDIGERE = 'DA_REDIGERE',
  //INDICA TUTTI QUEI fascicoli che 'estensore è diverso dal relatore
  IN_RELAZIONE_ESTENSORE = 'IN_RELAZIONE_ESTENSORE',
  // INDICA UN provvedimento che è in stato di revisione (stato IN_BOZZA ma con precedente stato MINUTA_DA_MODIFICARE, BUSTA_RIFIUTATA o MINUTA_MODIFICATA_PRESIDENTE)
  MINUTA_IN_REVISIONE = 'MINUTA_IN_REVISIONE',
}

registerEnumType(ProvvedimentiStatoEnum, {
  name: 'ProvvedimentiStatoEnum',
});
