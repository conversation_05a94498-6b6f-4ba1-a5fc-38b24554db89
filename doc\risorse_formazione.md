Guide e approfondimenti sulle tecnologie del progetto
=====================================================

## React

* :mortar_board:  [React Tutorial](https://reactjs.org/tutorial/tutorial.html) - Il tutorial ufficiale di React. Spiega il vecchio paradigma delle classi Javascript invece dei componenti funzionali, quindi bisogna tenere presente che nello sviluppo di applicazioni ora si usano esclusivamente i secondi, salvo casi eccezionali.

* :warning: [Perchè l'immutabilità è importante](https://reactjs.org/tutorial/tutorial.html#why-immutability-is-important) - Fa parte del tutorial precedente, ma è uno di quei concetti che vanno ribaditi :smiley:

* :mortar_board:  [Introduzione agli hook React](https://reactjs.org/docs/hooks-intro.html) - Spiegazione degli Hooks, che rappresentano il nuovo approccio alla gestione della logica applicativa (principalmente lo stato e i side-effects) nei componenti funzionali.

* :mortar_board: [Learn ReactJs](https://www.youtube.com/watch?v=Dorf8i6lCuk) - Corso che rispecchia il nostro modo di sviluppare applicazioni.

## NextJS

**NextJS** è un framework Front End costruito su React

* [Documentazione ufficiale di Next JS 14](https://nextjs.org/docs/14/getting-started)
* [Examples Next JS Github ](https://github.com/vercel/next.js)    


## NestJS

**NestJS** è un framework per applicazioni Back End in NodeJS, fortemente basato sui meccanismi di dependency injection di Angular

* [Documentazione ufficiale di NestJS](https://docs.nestjs.com/)

## GraphQL

* :mortar_board: [Introduzione a GraphQL](https://graphql.org/learn/) dal sito ufficiale
* :mortar_board: [Tour di Relay](https://relay.dev/docs/guided-tour/) - Guida/tutorial per iniziare a sviluppare client GraphQL con Relay
* :trophy: [Thinking in Relay](https://relay.dev/docs/principles-and-architecture/thinking-in-relay/) - una sintesi dei principi e degli approcci al design di GraphQL proposti da Relay


## Liquibase
**Liquibase** è open-source database-independent library per la gestione e l'applicazione delle modifiche allo schema del database, soprattutto in un ambiente di sviluppo software agile

* [Documentazione ufficiale di Liquibase](https://www.liquibase.com/)


## Azure
firma di accesso condiviso in **Azure**

* [Documentazione di Azure](https://www.npmjs.com/package/@azure/msal-browser/)
