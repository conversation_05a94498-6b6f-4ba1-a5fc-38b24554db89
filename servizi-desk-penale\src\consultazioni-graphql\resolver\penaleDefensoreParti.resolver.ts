import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { DifensoriPartiService } from '../../difensori-parti/difensori-parti.service';
import { PenaleDifenparti } from '../models/penale_difenparti.model';
import { CivileAnagdifen } from '../models/civile_anagdifen.model';

@Resolver(() => PenaleDifenparti)
export class PenaleDefensorePartiResolver {
  constructor(private readonly difensoriPartiService: DifensoriPartiService) {}

  @ResolveField('difensoreAnagrafica', () => CivileAnagdifen, {
    nullable: true,
  })
  async getAnagraficaDifensore(
    @Parent() difensoriParti: PenaleDifenparti,
  ): Promise<CivileAnagdifen | null> {
    const idAnagDifen = difensoriParti.idAnagraficaDifensore;
    const anagrafica = await this.difensoriPartiService.getAnagraficaDifensore(
      idAnagDifen,
    );

    return anagrafica;
  }

  @Query(() => PenaleDifenparti, { name: 'difensoreParti' })
  async reato(@Args('id') id: number): Promise<PenaleDifenparti> {
    const parti = await this.difensoriPartiService.difensore(id);
    if (!parti) {
      throw new NotFoundException(id);
    }

    return parti;
  }

  @Query(() => CivileAnagdifen, { name: 'anagraficaDifensore' })
  async anagraficaDifensore(@Args('id') id: number): Promise<CivileAnagdifen> {
    const parti = await this.difensoriPartiService.getAnagraficaDifensore(id);
    if (!parti) {
      throw new NotFoundException(id);
    }

    return parti;
  }

  @Query(returns => [PenaleDifenparti], { name: 'difensoriParti' })
  reati(): Promise<PenaleDifenparti[]> {
    return this.difensoriPartiService.difensori();
  }
}
