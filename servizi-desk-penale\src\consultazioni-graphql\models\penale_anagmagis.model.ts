import { Field, ID, Int, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PenaleAnagmagis {
  @Field(type => ID)
  idAnagmagis: number;

  @Field(type => String)
  cognome?: string;
  @Field(type => String)
  nome?: string;

  @Field(type => Date)
  dataNascita?: Date;
  @Field(type => Date)
  oggi?: Date;
  @Field(type => Int)
  operatore?: number;
  @Field(type => Int)
  idFunzione?: number;
  @Field(type => String)
  codiceFiscale?: string;

  public constructor(init?: Partial<PenaleAnagmagis>) {
    Object.assign(this, init);
  }
}
