import { Field, ID, Int, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PenaleVerbaleRicorso {
  @Field(type => ID)
  idVerbaleNrg: number;
  @Field(type => Int)
  idRicorsoUdienza: number;
  @Field(type => String)
  descrizioniParti?: string;
  @Field(type => String)
  descrizioneRelatore: string;
  @Field(type => String)
  descrizioniDifensori?: string;
  @Field(type => String)
  testoVerbale: string;
  @Field(type => Date)
  dataInserimento: Date;
  @Field(type => Date)
  dataAggiornamento: Date;
  @Field(type => String)
  descrizioneCoclusioniPg: string;

  public constructor(init?: Partial<PenaleVerbaleRicorso>) {
    Object.assign(this, init);
  }
}
