import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTMagisEntity } from '../consultazioni-graphql/entities/penale_t_magis.entity';
import { PenaleAnagmagisEntity } from '../consultazioni-graphql/entities/penale_anagmagis.entity';

@UfficioService()
export class MagistratiService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  magistrati(): Promise<PenaleTMagisEntity[]> {
    return this.connection.getRepository(PenaleTMagisEntity).find({
      relations: {
        tipoMag: true,
      },
    });
  }

  magistratiServiceByIdMagis(
    idMagis: number,
  ): Promise<PenaleTMagisEntity | null> {
    return this.connection.getRepository(PenaleTMagisEntity).findOne({
      where: { idMagis: idMagis },
      relations: {
        tipoMag: true,
      },
    });
  }
  async getMagistratiIdByCf(cf: string) {
    const idMagis = await this.connection
      .getRepository(PenaleTMagisEntity)
      .createQueryBuilder('magis')
      .innerJoin(
        PenaleAnagmagisEntity,
        'ana',
        'ana.idAnagmagis = magis.idAnagmagis',
      )
      .where('ana.CODICE_FISCALE = :cf', { cf })
      .select(['magis.idMagis'])
      .getMany();
    const magisIds: number[] = idMagis.map(magis => {
      return magis.idMagis;
    });
    return magisIds;
  }
}
