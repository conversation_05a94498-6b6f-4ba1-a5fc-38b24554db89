import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PenaleTMagisEntity } from './penale_t_magis.entity';

@Entity('PENALE_T_ESTENSORI_SENTENZA')
export class PenaleTEstensoriSentenzaEntity {
  @PrimaryGeneratedColumn({ name: 'ID' })
  id: number;
  @Column({ name: 'ID_SENT', nullable: true })
  idSentenza: number;
  @OneToOne(() => PenaleTMagisEntity)
  @JoinColumn({ name: 'ID_ESTENSORE' })
  estensore?: PenaleTMagisEntity;
}
