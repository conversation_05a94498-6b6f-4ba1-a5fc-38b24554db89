import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('CIVILE_ANAGDIFEN') //nome tabella su schema oracle
export class CivileAnagdifenEntity {
  @PrimaryColumn({ name: 'ID_ANAGDIFEN' })
  idAngraficaDifensori: number;

  @Column({ name: 'CO<PERSON><PERSON><PERSON><PERSON>' })
  cognome: string;

  @Column({ name: 'NOME<PERSON><PERSON><PERSON>' })
  nome: string;
  @Column({ name: 'CODAVVOC' })
  codiceAvvocato: string;
  @Column({ name: 'CODFISC', nullable: true })
  codiceFiscale?: string;

  @Column({ name: 'DATANASC', nullable: true })
  dataNascita?: Date;
  @Column({ name: 'LUOGONASC', nullable: true })
  luogoNascita?: string;
  @Column({ name: 'PROVNASC', nullable: true })
  provicniaNascita?: string;
  @Column({ name: 'DATACASSAZIONISTA' })
  dataCassazionista: Date;
  @Column({ name: 'FOROPROV' })
  foroProvincia: number;
  @Column({ name: 'CODFORO' })
  codiceForo: number;
  @Column({ name: 'DTAVV' })
  dtAvv: Date;
  @Column({ name: 'DUFF' })
  dUff: number;
  @Column({ name: 'DTPROC' })
  dtProc: Date;
  @Column({ name: 'EMAIL' })
  email: string;
  @Column({ name: 'ID_AULORIF' })
  idAuloRif: number;
  @Column({ name: 'DECEDUTO' })
  deceduto: number;
  @Column({ name: 'FAX' })
  fax: string;
  @Column({ name: 'NAZIONALITA' })
  nazionalita: string;
  @Column({ name: 'CODFISCPREC' })
  codiceFiscalePrecedente: string;

  difensoreAnagrafica?: CivileAnagdifenEntity;
}
