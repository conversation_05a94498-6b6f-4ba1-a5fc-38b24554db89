import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, <PERSON>rent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { CollegioService } from '../../collegio/collegio.service';
import { PenaleCollegio } from '../models/penale_t_collegio.model';
import { PenaleTMagis } from '../models/penale_t_magis.model';
import { MagistratiService } from '../../magistrati/magistrati.service';
import { Log } from '../../decorators/log.decorator';

@Resolver(() => PenaleCollegio)
@Log() // Applicato all'intera classe - tutti i metodi Query/Mutation saranno loggati
export class PenaleCollegioResolver {
  constructor(
    private readonly collegioService: CollegioService,
    private readonly magistratiService: MagistratiService,
  ) {}

  @Query(() => PenaleCollegio, { name: 'collegio' })
  async collegio(@Args('id') id: number): Promise<PenaleCollegio> {
    const udienza = await this.collegioService.collegio(id);
    if (!udienza) {
      throw new NotFoundException(id);
    }
    return udienza;
  }

  @Query(() => [PenaleCollegio], { name: 'collegioByNrgAndIdUdienza' })
  async collegioByNrgAndIdUdienza(
    @Args('id') id: number,
    @Args('nrg') nrg: number,
  ): Promise<PenaleCollegio[]> {
    const udienza = await this.collegioService.collegioByIdUdienza(id);
    if (!udienza) {
      throw new NotFoundException(id);
    }
    return udienza;
  }

  @ResolveField('magistrato', () => PenaleTMagis)
  async getMagistrato(
    @Parent() penaleCollegio: PenaleCollegio,
  ): Promise<PenaleTMagis | null> {
    if (penaleCollegio.idMagis > 0) {
      const colleggi = await this.magistratiService.magistratiServiceByIdMagis(
        penaleCollegio.idMagis,
      );

      return colleggi;
    }
    return null;
  }

  @Query(returns => [PenaleCollegio], { name: 'collegi' })
  collegi(): Promise<PenaleCollegio[]> {
    return this.collegioService.colleggi();
  }
}
