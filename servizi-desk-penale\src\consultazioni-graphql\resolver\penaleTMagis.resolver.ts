import { NotFoundException } from '@nestjs/common';
import { Args, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleTMagis } from '../models/penale_t_magis.model';
import { MagistratiService } from '../../magistrati/magistrati.service';
import { PenaleAnagmagis } from '../models/penale_anagmagis.model';
import { AnagraficaMagistratiService } from '../../anagrafica-magistrati/anagrafica-magistrati.service';

@Resolver(() => PenaleTMagis)
export class PenaleTMagisResolver {
  constructor(
    private readonly magisService: MagistratiService,
    private readonly anagraficaMagistratiService: AnagraficaMagistratiService,
  ) {}

  @ResolveField('anagraficaMagistrato', () => PenaleAnagmagis, {
    nullable: true,
  })
  async getAnagraficaMagistrati(
    @Parent() penaleTMagis: PenaleTMagis,
  ): Promise<PenaleAnagmagis | null> {
    if (penaleTMagis.idAnagmagis && penaleTMagis.idAnagmagis > 0) {
      return await this.anagraficaMagistratiService.anagraficaMagistrato(
        penaleTMagis.idAnagmagis,
      );
    }
    return null;
  }

  @Query(() => PenaleTMagis, { name: 'magistrato' })
  async magistrato(@Args('id') id: number): Promise<PenaleTMagis> {
    const parti = await this.magisService.magistratiServiceByIdMagis(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleTMagis], { name: 'magistrati' })
  magistrati(): Promise<PenaleTMagis[]> {
    return this.magisService.magistrati();
  }
}
