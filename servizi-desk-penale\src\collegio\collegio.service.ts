import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleCollegioEntity } from '../consultazioni-graphql/entities/penale_collegio.entity';
import { Log } from '../decorators/log.decorator';

@UfficioService()
@Log() // Applicato all'intera classe - tutti i metodi pubblici saranno loggati
export class CollegioService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  colleggi(): Promise<PenaleCollegioEntity[]> {
    return this.connection.getRepository(PenaleCollegioEntity).find();
  }

  collegio(idFunzione: number): Promise<PenaleCollegioEntity | null> {
    return this.connection.getRepository(PenaleCollegioEntity).findOne({
      where: { idFunzione: idFunzione },
    });
  }

  async collegioByIdUdienza(
    idUdien: number,
  ): Promise<PenaleCollegioEntity[] | null> {
    return this.connection.getRepository(PenaleCollegioEntity).find({
      where: { idUdienza: idUdien, inUdienza: 1 },
    });
  }
  async collegioByIdUdienzaAndPresidente(
    idUdien: number,
  ): Promise<PenaleCollegioEntity[] | null> {
    return this.connection.getRepository(PenaleCollegioEntity).find({
      where: { idUdienza: idUdien, tipoMag: 'PRE', inUdienza: 1 },
    });
  }
}
