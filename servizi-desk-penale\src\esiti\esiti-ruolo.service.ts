import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleEsitiRuoloEntity } from '../consultazioni-graphql/entities/penale_esiti_ruolo.entity';

@UfficioService()
export class EsitiRuoloService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  esitiRuoli(): Promise<PenaleEsitiRuoloEntity[]> {
    return this.connection.getRepository(PenaleEsitiRuoloEntity).find();
  }

  esitoRuolo(nrg: number, idUdien: number): Promise<PenaleEsitiRuoloEntity | null> {
    return this.connection
      .getRepository(PenaleEsitiRuoloEntity)
      .findOneBy({ nrg: nrg, idUdienza: idUdien });
  }
}
