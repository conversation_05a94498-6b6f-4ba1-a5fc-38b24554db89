import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleTMagisDeatils } from './penale_t_magis_details.model';

@ObjectType()
export class PenaleCollegioDetailsModel {
  @Field(type => ID)
  idFunzione: number;

  magistrato?: PenaleTMagisDeatils;

  @Field(type => Int)
  operatore: number;

  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  gradoMag: number;
  @Field(type => String)
  tipoMag: string;
  @Field(type => Int)
  idMagis: number;
  @Field(type => Int)
  idMagiscolle: number;
  @Field(type => Int)
  idUdienza: number;

  @Field(type => Int)
  glbDtime?: number;
  @Field(type => Int)
  inUdienza?: number;
  isRelatore?: boolean = false;
  isEstensore?: boolean = false;

  public constructor(init?: Partial<PenaleCollegioDetailsModel>) {
    Object.assign(this, init);
  }
}
