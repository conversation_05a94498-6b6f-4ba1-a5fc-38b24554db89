import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleUfficioEntity } from '../consultazioni-graphql/entities/penale_ufficio.entity';

@UfficioService()
export class UfficiDBService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  uffici(): Promise<PenaleUfficioEntity[]> {
    return this.connection.getRepository(PenaleUfficioEntity).find();
  }

  utente(codiceUfficio: string): Promise<PenaleUfficioEntity | null> {
    return this.connection
      .getRepository(PenaleUfficioEntity)
      .findOneBy({ codiceUfficio: codiceUfficio });
  }

  async getFirstUfficio(): Promise<PenaleUfficioEntity | null> {
    const promise = await this.uffici();
    if (promise) {
      return promise[0];
    }
    return null;
  }
}
