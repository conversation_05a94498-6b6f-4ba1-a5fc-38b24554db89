import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleTMagisEntity } from '../entities/penale_t_magis.entity';
import { PenaleTMagis } from './penale_t_magis.model';

@ObjectType()
export class PenaleCollegio {
  @Field(type => ID)
  idFunzione: number;

  magistrato?: PenaleTMagis;

  @Field(type => Int)
  operatore: number;

  @Field(type => Date)
  oggi: Date;
  @Field(type => Int)
  gradoMag: number;
  @Field(type => String)
  tipoMag: string;
  @Field(type => Int)
  idMagis: number;
  @Field(type => Int)
  idMagiscolle: number;
  @Field(type => Int)
  idUdienza: number;

  @Field(type => Int)
  glbDtime?: number;
  @Field(type => Int)
  inUdienza?: number;
  isRelatore?: boolean = false;
  isEstensore?: boolean = false;

  public constructor(init?: Partial<PenaleCollegio>) {
    Object.assign(this, init);
  }
}
