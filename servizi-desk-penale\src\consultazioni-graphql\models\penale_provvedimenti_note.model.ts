import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { PenaleTUtente } from './penale_t_utente.model';
import { StatoInserimentoNoteEnum } from '../entities/enumaration/stato-inserimento-note';

@ObjectType()
export class PenaleProvvedimentiNote {
  @Field(type => ID)
  idProvvNote: string;
  @Field(type => String)
  note: string;
  @Field(type => String)
  idProvvedimento: string;

  @Field(type => Date)
  dataInserimento: Date;

  @Field(type => Int)
  idAutore: number;
  autore?: PenaleTUtente;

  @Field(type => StatoInserimentoNoteEnum)
  statoInserimento?: StatoInserimentoNoteEnum;
}
