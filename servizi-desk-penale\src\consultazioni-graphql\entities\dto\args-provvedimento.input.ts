import { Field, InputType, Int } from '@nestjs/graphql';
import { ProvvedimentiTipoEnum } from '../enumaration/provvedimenti-tipo.enum';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class ArgsProvvedimentoInput {
  @ApiProperty()
  @Field(() => String)
  tipologiaProvvedimento: ProvvedimentiTipoEnum;
  @ApiProperty()
  @Field(() => Int)
  numRuolo?: number;
  @ApiProperty()
  @Field(() => Int)
  anRuolo?: number;

  @ApiProperty()
  @Field(() => String)
  idProvv?: string;
  @ApiProperty()
  @Field(() => String)
  text?: string;
  @ApiProperty()
  @Field(() => String)
  textOscurato?: string;
  @ApiProperty()
  @Field(() => String)
  introduzione?: string;
  @ApiProperty()
  @Field(() => String)
  motivoRicorso?: string;
  @ApiProperty()
  @Field(() => String)
  finaleDeposito?: string;

  @ApiProperty()
  @Field(() => String)
  pqm?: string;

  @ApiProperty()
  @Field(() => Boolean)
  generaOscurato?: boolean;
  @ApiProperty()
  @Field(() => Date)
  dataDecisione?: Date;
}
