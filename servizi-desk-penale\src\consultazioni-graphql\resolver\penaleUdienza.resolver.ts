import { Args, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { PenaleUdienza } from '../models/penale_udienza.model';
import { PenaleUdienzaService } from '../../penale-udienza/penale-udienza.service';
import { InfoProvvedimento } from '../models/dto/info_provvedimento.model';
import { RicercaSentenzaService } from '../../ricerca-sentenza/ricerca-sentenza.service';
import { TRicorsoUdienzaService } from '../../ricorso-udienza/t-ricorso-udienza.service';
import { ProvvedimentiService } from '../../provvedimenti/provvedimenti.service';
import { RicorsoUdienzaCommonService } from '../../ricorso-udienza/ricorso-udienza-common.service';

@Resolver(() => PenaleUdienza)
export class PenaleUdienzaResolver {
  constructor(
    private readonly penaleUdienzaService: PenaleUdienzaService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly ricorsoUdienzaCommonService: RicorsoUdienzaCommonService,
  ) {}

  @Query(() => PenaleUdienza, { name: 'penaleUdienzaByIdUdienza' })
  async penaleUdienzaByIdUdienza(
    @Args('idUdienza') idUdienza: number,
  ): Promise<PenaleUdienza | null> {
    const code = await this.penaleUdienzaService.penaleUdienzaByIdUdienza(
      idUdienza,
    );
    return code;
  }

  /*Il metodo getPubblicatoSIC prende come argomento un'istanza di PenaleTRicudien e
   * controlla la proprietà dello stato dell'oggetto provvedimento, all'interno dell'istanza di PenaleTRicudien.
   * A seconda del valore dello stato, esegue diverse azioni*/
  @ResolveField('checkStatoOnSIC', () => InfoProvvedimento, {
    nullable: true,
  })
  async checkStatoOnSIC(
    @Args('idUdien') idUdien: number,
    @Args('nrg') nrg: number,
  ): Promise<InfoProvvedimento | null> {
    return await this.ricorsoUdienzaCommonService.checkStatoOnSicCommon(
      idUdien,
      nrg,
    );
  }
  @Query(() => InfoProvvedimento, { name: 'checkStatoOnSICQuery' })
  async checkStatoOnSICQuery(
    @Args('idProvv') idProvv: string,
  ): Promise<InfoProvvedimento | null> {
    const provv = await this.provvedimentiService.provvedimentoById(idProvv);
    if (provv) {
      return await this.ricorsoUdienzaCommonService.checkStatoOnSicCommon(
        provv?.idUdienza,
        provv?.nrg,
      );
    }
    return null;
  }
}
