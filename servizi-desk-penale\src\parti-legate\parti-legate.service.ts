import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenalePartiLegateEntity } from '../consultazioni-graphql/entities/penale_parti_legate.entity';

@UfficioService()
export class PartiLegateService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  partiLegati(): Promise<PenalePartiLegateEntity[]> {
    return this.connection.getRepository(PenalePartiLegateEntity).find({
      relations: {
        tipoLegame: true,
      },
    });
  }

  parteLegata(idParte: number): Promise<PenalePartiLegateEntity | null> {
    return this.connection.getRepository(PenalePartiLegateEntity).findOne({
      where: { idParte: idParte },
      relations: {
        tipoLegame: true,
      },
    });
  }
}
