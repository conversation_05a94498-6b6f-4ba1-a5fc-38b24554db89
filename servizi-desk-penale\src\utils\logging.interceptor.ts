import { Injectable, NestInterceptor, ExecutionContext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { IS_LOGGED_KEY } from '../decorators/log.decorator';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger('MethodTracing');

  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const isLogged = this.reflector.get<boolean>(IS_LOGGED_KEY, context.getHandler());
    if (!isLogged) return next.handle();

    const className = context.getClass().name;
    const methodName = context.getHandler().name;
    
    // Gestione parametri per diversi tipi di contesto (HTTP, GraphQL, etc.)
    const args = this.extractArguments(context);
    const argsString = this.formatArguments(args);

    this.logger.log(`[${className}] --> ${methodName}(${argsString})`);
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        this.logger.log(`[${className}] <-- ${methodName} (took ${duration}ms)`);
      }),
      catchError(error => {
        const duration = Date.now() - startTime;
        this.logger.error(`[${className}] <-- ${methodName} FAILED (took ${duration}ms)`, error.stack);
        throw error;
      }),
    );
  }

  private extractArguments(context: ExecutionContext): any[] {
    try {
      const contextType = context.getType<'http' | 'rpc' | 'ws'>();

      if (contextType === 'http') {
        // Per controller REST
        const request = context.switchToHttp().getRequest();
        return [
          `body: ${this.safeStringify(request.body)}`,
          `params: ${this.safeStringify(request.params)}`,
          `query: ${this.safeStringify(request.query)}`
        ];
      } else {
        // Per GraphQL e altri contesti, usa gli argomenti generici
        const args = context.getArgs();
        // Per GraphQL, gli argomenti sono: [parent, args, context, info]
        // Prendiamo solo gli argomenti effettivi (args)
        if (args && args.length > 1 && typeof args[1] === 'object') {
          return [this.safeStringify(args[1])]; // args GraphQL
        }
        return args.map(arg => this.safeStringify(arg));
      }
    } catch (error) {
      return ['[unable to extract arguments]'];
    }
  }

  private formatArguments(args: any[]): string {
    try {
      if (!args || args.length === 0) return '';
      
      const formattedArgs = args.map(arg => {
        if (typeof arg === 'object') {
          return this.safeStringify(arg);
        }
        return String(arg);
      });
      
      return formattedArgs.join(', ');
    } catch (error) {
      return '[unable to format arguments]';
    }
  }

  private safeStringify(obj: any): string {
    try {
      if (obj === null || obj === undefined) return String(obj);
      
      const jsonString = JSON.stringify(obj, (key, value) => {
        // Nasconde password e token sensibili
        if (typeof key === 'string' && 
            (key.toLowerCase().includes('password') || 
             key.toLowerCase().includes('token') ||
             key.toLowerCase().includes('secret'))) {
          return '[HIDDEN]';
        }
        
        // Tronca stringhe molto lunghe
        if (typeof value === 'string' && value.length > 200) {
          return value.substring(0, 197) + '...';
        }
        
        return value;
      });
      
      // Limita la lunghezza totale dell'output
      if (jsonString.length > 500) {
        return jsonString.substring(0, 497) + '...';
      }
      
      return jsonString;
    } catch (error) {
      return '[unserializable object]';
    }
  }
}
