import {
  HttpException,
  HttpExceptionOptions,
} from '@nestjs/common/exceptions/http.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class NsBaseException extends HttpException {
  errorCode: CodeErrorEnumException;
  typeError: NSTypeErrorEnum;

  constructor(
    response: string | Record<string, any>,
    errorCode: CodeErrorEnumException,
    typeError: NSTypeErrorEnum,
    options?: HttpExceptionOptions,
    status?: number,
  ) {
    super(response, status || 433, options);
    this.errorCode = errorCode;
    this.typeError = typeError;
  }
}
export interface INsBaseException {
  errorCode: string;
  typeError: NSTypeErrorEnum;
}
export class NsBaseException2 {
  errorCode: string;
  typeError: NSTypeErrorEnum;

  constructor(
    response: string | Record<string, any>,
    errorCode: string,
    typeError: NSTypeErrorEnum,
    options?: HttpExceptionOptions,
    status?: number,
  ) {
    this.errorCode = errorCode;
    this.typeError = typeError;
  }
}
