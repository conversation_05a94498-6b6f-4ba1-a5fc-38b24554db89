import { Column, Entity, PrimaryColumn } from 'typeorm';
import ColumnBooleanTransformer from './utility/column-boolean-transformer';

@Entity('V_RICERCA_RIUNITI') //nome vista su schema oracle
export class PenaleRicercaRiunitiEntity {
  /*  @Column({ name: 'ID_SENT' })
  idSent: number;*/

  @PrimaryColumn({ name: 'ID_RICUDIEN' })
  idRicUdien: number;
  @Column({ name: 'NRG', nullable: true })
  nrg?: number;

  @Column({ name: 'ID_RICUDIEN_PADRE', nullable: true })
  idRicudienPadre?: number;
  @Column({ name: 'NRG_PADRE', nullable: true })
  nrgPadre?: number;
  @Column({ name: 'NRGREALE_PADRE', nullable: true })
  nrgRealePadre?: number;
  //se valorizzato as 1 si tratta di un ricorso principale riunito
  @Column({
    name: 'ISPRINCIPALE',
    type: 'int',
    width: 1,
    nullable: true,
    transformer: new ColumnBooleanTransformer(),
  })
  isPrincipale?: boolean;

  // se valorizzato è 1 significa che il ricorso è stato riunito dopo udienza
  @Column({
    name: 'RIUNITO',
    nullable: true,
    type: 'varchar',
    width: 1,
    transformer: new ColumnBooleanTransformer(),
  })
  riunito?: boolean;
}
