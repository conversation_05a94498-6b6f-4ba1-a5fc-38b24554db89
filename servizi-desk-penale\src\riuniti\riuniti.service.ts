import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { TRicorsoUdienzaService } from '../ricorso-udienza/t-ricorso-udienza.service';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';

@UfficioService()
export class RiunitiService {
  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
  ) {}

  getRiuniti(idRicUdien: number) {
    return this.connection.manager.transaction(
      async transactionalEntityManager => {
        const ricorsoUdienza = await transactionalEntityManager.findOne(
          PenaleTRicorsoUdienzaEntity,
          {
            where: { idRicudien: idRicUdien },
            relations: {
              vSentenza: true,
            },
          },
        );
        if (ricorsoUdienza?.idRicudien) {
          const udienzaByPrincipale = new Array<PenaleTRicorsoUdienzaEntity>();
          if (ricorsoUdienza?.nrg) {
            udienzaByPrincipale.push(
              ...(await this.ricorsoUdienzaService.ricorsoUdienzaByPrincipale(
                transactionalEntityManager,
                ricorsoUdienza?.nrg,
              )),
            );
          }
          if (ricorsoUdienza?.vSentenza?.idSent) {
            const penaleVRicercaPenaleSentenzaEntities =
              await transactionalEntityManager.find(
                PenaleTRicorsoUdienzaEntity,
                {
                  where: {
                    vSentenza: {
                      idSent: ricorsoUdienza.vSentenza?.idSent,
                      riunito: true,
                    },
                  },
                  relations: {
                    ricorso: true,
                    vSentenza: true,
                  },
                },
              );
            if (penaleVRicercaPenaleSentenzaEntities?.length > 0) {
              udienzaByPrincipale.push(...penaleVRicercaPenaleSentenzaEntities);
            }
          }
          return udienzaByPrincipale;
        }
        throw new GenericErrorException(
          'Ricorso udienza non trovato',
          CodeErrorEnumException.RICORSO_NOT_FOUND,
        );
      },
    );
  }
}
