import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { ProvvedimentoChangeStatusService } from './provvedimento-change-status.service';
import { AuthModule } from '../auth/auth.module';
import { ProvvedimentoRelazioneService } from './provvedimento-relazione.service';

@Module({
  imports: [
    AuthModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [ProvvedimentoChangeStatusService, ProvvedimentoRelazioneService],
  exports: [ProvvedimentoChangeStatusService, ProvvedimentoRelazioneService],
})
export class ProvvedimentoChangeStatusModule {}
