import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PenaleCollegioEntity } from '../consultazioni-graphql/entities/penale_collegio.entity';
import { PenaleTMagisEntity } from '../consultazioni-graphql/entities/penale_t_magis.entity';
import { PenaleAnagmagisEntity } from '../consultazioni-graphql/entities/penale_anagmagis.entity';
import { PenaleTRicorsoUdienzaEntity } from '../consultazioni-graphql/entities/penale_t_ricudien.entity';
import { PenaleTUtenteEntity } from '../consultazioni-graphql/entities/penale_t_utente.entity';
import { PenaleTEsitoSentEntity } from '../consultazioni-graphql/entities/penale_t_esito_sent.entity';
import { PenaleTEsitoEntity } from '../consultazioni-graphql/entities/penale_t_esito.entity';
import { PenaleTSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_sentenza.entity';
import { PenaleVRicercaPenaleSentenzaEntity } from 'src/consultazioni-graphql/entities/penale_v_ricerca_penale_sentenza.entity';

/**
 * Servizi applicativi di consultazione sull'anagrafica distrettuale
 */
@Injectable()
export class UtentiQueryAuthService {
  constructor(
    @InjectRepository(PenaleCollegioEntity, 'auth')
    private penaleCollegioEntityRepository: Repository<PenaleCollegioEntity>,
    @InjectRepository(PenaleTUtenteEntity, 'auth')
    private penaleTUtenteEntityRepository: Repository<PenaleTUtenteEntity>,
    @InjectRepository(PenaleAnagmagisEntity, 'auth')
    private penalePenaleAnagmagisEntityRepository: Repository<PenaleAnagmagisEntity>,
    @InjectRepository(PenaleTRicorsoUdienzaEntity, 'auth')
    private penaleTRicorsoUdienzaEntityRepository: Repository<PenaleTRicorsoUdienzaEntity>,
  ) {}

  /**
   * Verifica che un soggetto sia censito nell'anagrafica distrettuale
   * @param codiceFiscale cf del soggetto
   */
  async soggettoExists(codiceFiscale: string): Promise<boolean> {
    /*   const found = await this.giudiceRepository.manager.query(
              'SELECT 1 FROM  persone p WHERE p.CODICEFISCALE = :cf',
              [codiceFiscale],
            );
            return found && found.length > 0;*/
    return false;
  }

  /**
   * Verifica che un soggetto sia censito come GIUDICE nell'anagrafica distrettuale
   * @param codiceFiscale cf del soggetto
   */
  async isGiudice(codiceFiscale: string): Promise<boolean> {
    /*   const found = await this.giudiceRepository.manager.query(
              'SELECT 1 FROM d_giudici_gdp g JOIN persone p ON g.IDSOGGETTO = p.IDSOGGETTO WHERE p.CODICEFISCALE = :cf',
              [codiceFiscale],
            );
            return found && found.length > 0;*/
    return false;
  }

  async isPresidenteByNrgAndIdUdienza(
    nrg: number,
    codiceFiscale: string,
    idUdienza: number,
  ): Promise<boolean> {
    try {
      const countIsPresidente = await this.penaleCollegioEntityRepository
        .createQueryBuilder('coll')
        .innerJoinAndMapMany(
          'coll.magistrati',
          PenaleTMagisEntity,
          'magis',
          'coll.idMagis = magis.idMagis',
        )
        .innerJoinAndMapMany(
          'coll.magistratiAnagrafica',
          PenaleAnagmagisEntity,
          'anag',
          'anag.idAnagmagis = magis.idAnagmagis',
        )
        .innerJoinAndMapMany(
          'coll.ricorsoUdienza',
          PenaleTRicorsoUdienzaEntity,
          'PTR',
          'coll.idUdienza = PTR.idUdienza',
        )
        .where(
          "anag.codiceFiscale = :cf and coll.tipoMag = 'PRE' and PTR.nrg = :nrg and PTR.idUdienza = :idUdienza",
          {
            cf: codiceFiscale,
            nrg: nrg,
            idUdienza: idUdienza,
          },
        ) // or you can change condition to 'key.userId = :userId' because of you have `userId` in Key
        .getCount();
      return countIsPresidente > 0;
    } catch (e) {
      console.log(e);
    }
    return false;
  }

  async isRelatore(codiceFiscale: string): Promise<boolean> {
    /**
     * select count(*) from PENALE_COLLEGIO coll
     *                 join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
     *                 join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
     *                 JOIN PENALE_T_RICUDIEN PTR on coll.ID_UDIEN = PTR.ID_UDIEN
     * where anag.CODICE_FISCALE = '****************' and PTR.ID_RELATORE = magis.ID_MAGIS;
     */
    try {
      const countIsRelatore = await this.penaleCollegioEntityRepository
        .createQueryBuilder('coll')
        .innerJoinAndMapMany(
          'coll.magistrati',
          PenaleTMagisEntity,
          'magis',
          'coll.idMagis = magis.idMagis',
        )
        .innerJoinAndMapMany(
          'coll.magistratiAnagrafica',
          PenaleAnagmagisEntity,
          'anag',
          'anag.idAnagmagis = magis.idAnagmagis',
        )
        .innerJoinAndMapMany(
          'coll.ricorsoUdienza',
          PenaleTRicorsoUdienzaEntity,
          'PTR',
          'coll.idUdienza = PTR.idUdienza',
        )
        .where('anag.codiceFiscale = :cf and PTR.idRelatore = magis.idMagis', {
          cf: codiceFiscale,
        }) // or you can change condition to 'key.userId = :userId' because of you have `userId` in Key
        .getCount();
      return countIsRelatore > 0;
    } catch (e) {
      console.log(e);
    }
    return false;
  }
  async isRelatoreByIdUdienza(
    nrg: number,
    codiceFiscale: string,
  ): Promise<boolean> {
    /**
     * select count(*) from PENALE_COLLEGIO coll
     *                 join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
     *                 join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
     *                 JOIN PENALE_T_RICUDIEN PTR on coll.ID_UDIEN = PTR.ID_UDIEN
     * where anag.CODICE_FISCALE = '****************' and PTR.ID_RELATORE = magis.ID_MAGIS;
     */
    try {
      const countIsRelatore = await this.penaleCollegioEntityRepository
        .createQueryBuilder('coll')
        .innerJoinAndMapMany(
          'coll.magistrati',
          PenaleTMagisEntity,
          'magis',
          'coll.idMagis = magis.idMagis',
        )
        .innerJoinAndMapMany(
          'coll.magistratiAnagrafica',
          PenaleAnagmagisEntity,
          'anag',
          'anag.idAnagmagis = magis.idAnagmagis',
        )
        .innerJoinAndMapMany(
          'coll.ricorsoUdienza',
          PenaleTRicorsoUdienzaEntity,
          'PTR',
          'coll.idUdienza = PTR.idUdienza',
        )
        .where(
          'anag.codiceFiscale = :cf and PTR.idRelatore = magis.idMagis AND PTR.nrg = :nrg',
          {
            cf: codiceFiscale,
            nrg: nrg,
          },
        ) // or you can change condition to 'key.userId = :userId' because of you have `userId` in Key
        .getCount();
      return countIsRelatore > 0;
    } catch (e) {
      console.log(e);
    }
    return false;
  }

  //FIXME controllare chi usa questa query e correggerla usando nrg ed idUdienza
  async isEstensoreByNrg(nrg: number, codiceFiscale: string): Promise<boolean> {
    /**
     * select count(*) from PENALE_COLLEGIO coll
     *                 join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
     *                 join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
     *                 JOIN PENALE_T_RICUDIEN PTR on coll.ID_UDIEN = PTR.ID_UDIEN
     * where anag.CODICE_FISCALE = '****************' and PTR.ID_RELATORE = magis.ID_MAGIS;
     */
    try {
      const countIsEstensore = await this.penaleCollegioEntityRepository
        .createQueryBuilder('coll')
        .distinct(true)
        .innerJoinAndMapMany(
          'coll.magistrati',
          PenaleTMagisEntity,
          'magis',
          'coll.idMagis = magis.idMagis',
        )
        .innerJoinAndMapMany(
          'coll.magistratiAnagrafica',
          PenaleAnagmagisEntity,
          'anag',
          'anag.idAnagmagis = magis.idAnagmagis',
        )
        .innerJoinAndMapMany(
          'coll.ricorsoUdienza',
          PenaleTRicorsoUdienzaEntity,
          'PTR',
          'coll.idUdienza = PTR.idUdienza',
        )
        .innerJoinAndMapMany(
          'coll.esito',
          PenaleTEsitoEntity,
          'PTE',
          'PTE.ID_RICUDIEN = PTR.ID_RICUDIEN',
        )
        .innerJoinAndMapMany(
          'coll.esitoSent',
          PenaleTEsitoSentEntity,
          'PTS',
          'PTE.ID_ESITO = PTS.ID_ESITO',
        )
        .innerJoinAndMapMany(
          'coll.sentenza',
          PenaleTSentenzaEntity,
          'TSET',
          'TSET.ID_SENT = PTS.ID_SENT',
        )
        //TODO INSERIRE la parte dell'estensore 2
        .where(
          'anag.codiceFiscale = :cf and TSET.ID_ESTENSORE = magis.ID_MAGIS AND PTR.nrg = :nrg',
          {
            cf: codiceFiscale,
            nrg: nrg,
          },
        ) // or you can change condition to 'key.userId = :userId' because of you have `userId` in Key
        .getCount();
      return countIsEstensore > 0;
    } catch (e) {
      console.log(e);
    }
    return false;
  }
  async isEstensoreByNrgAndIdUdienza(
    nrg: number,
    idUdien: number,
    codiceFiscale: string,
  ): Promise<boolean> {
    /**
     *SELECT count(*)  FROM PENALE_T_RICUDIEN PTR
     *  JOIN V_RICERCA_PENALE_SENTENZA VRPS ON PTR.ID_RICUDIEN = VRPS.ID_RICUDIEN
     *  JOIN PENALE_T_MAGIS PTM ON VRPS.ID_ESTENSORE = PTM.ID_MAGIS
     *  JOIN PENALE_ANAGMAGIS PA ON PTM.ID_ANAGMAGIS = PA.ID_ANAGMAGIS
     *WHERE PTR.NRG = :nrg AND PTR.ID_UDIEN = :idUdien AND PA.CODICE_FISCALE = :cf;
     */
    try {
      const countIsEstensore = await this.penaleTRicorsoUdienzaEntityRepository
        .createQueryBuilder('PTR')
        .distinct(true)
        .innerJoin(
          PenaleVRicercaPenaleSentenzaEntity,
          'VRPS',
          'PTR.idRicudien = VRPS.idRicUdien',
        )
        .innerJoin(
          PenaleTMagisEntity,
          'MAGIS',
          'VRPS.idEstensore = MAGIS.idMagis',
        )
        .innerJoin(
          PenaleAnagmagisEntity,
          'ANAG',
          'ANAG.idAnagmagis = MAGIS.idAnagmagis',
        )
        .where(
          'PTR.nrg = :nrg AND PTR.udienze = :idUdien AND ANAG.codiceFiscale = :cf',
          {
            nrg: nrg,
            idUdien: idUdien,
            cf: codiceFiscale,
          },
        )
        .getCount();
      return countIsEstensore > 0;
    } catch (e) {
      console.log(e);
    }
    return false;
  }
  async getIdMagisEstensore(nrg: number, idUdien: number): Promise<number | undefined> {
    /**
     * select count(*) from PENALE_COLLEGIO coll
     *                 join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
     *                 join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
     *                 JOIN PENALE_T_RICUDIEN PTR on coll.ID_UDIEN = PTR.ID_UDIEN
     * where anag.CODICE_FISCALE = '****************' and PTR.ID_RELATORE = magis.ID_MAGIS;
     */
    try {
      //TODO da rivedere per estensore 2
      const getIdEstensore = await this.penaleCollegioEntityRepository
        .createQueryBuilder('coll')
        .distinct(true)
        .select('TSET.ID_ESTENSORE')
        .innerJoinAndMapMany(
          'coll.magistrati',
          PenaleTMagisEntity,
          'magis',
          'coll.idMagis = magis.idMagis',
        )
        .innerJoinAndMapMany(
          'coll.magistratiAnagrafica',
          PenaleAnagmagisEntity,
          'anag',
          'anag.idAnagmagis = magis.idAnagmagis',
        )
        .innerJoinAndMapMany(
          'coll.ricorsoUdienza',
          PenaleTRicorsoUdienzaEntity,
          'PTR',
          'coll.idUdienza = PTR.idUdienza',
        )
        .innerJoinAndMapMany(
          'coll.esito',
          PenaleTEsitoEntity,
          'PTE',
          'PTE.ID_RICUDIEN = PTR.ID_RICUDIEN',
        )
        .innerJoinAndMapMany(
          'coll.esitoSent',
          PenaleTEsitoSentEntity,
          'PTS',
          'PTE.ID_ESITO = PTS.ID_ESITO',
        )
        .innerJoinAndMapMany(
          'coll.sentenza',
          PenaleTSentenzaEntity,
          'TSET',
          'TSET.ID_SENT = PTS.ID_SENT',
        )
        //TODO inserire or per estensore 2
        .where('TSET.ID_ESTENSORE = magis.ID_MAGIS AND PTR.nrg = :nrg AND PTR.idUdienza = :idUdien', {
          nrg: nrg,
          idUdien: idUdien
        })
        .getRawOne();
      return getIdEstensore.ID_ESTENSORE as number;
    } catch (e) {
      console.log(e);
    }
    return undefined;
  }
  async isEstensore(codiceFiscale: string) {
    /*
    SELECT DISTINCT count(DISTINCT (PTR.ID_RICUDIEN))
		from PENALE_COLLEGIO coll
                join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
                join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
                JOIN PENALE_T_RICUDIEN PTR on coll.ID_UDIEN = PTR.ID_UDIEN
				JOIN PENALE_T_ESITO PTE ON (PTE.ID_RICUDIEN = PTR.ID_RICUDIEN)
				JOIN PENALE_T_ESITOSENT PTS ON	( PTE.ID_ESITO = PTS.ID_ESITO)
                JOIN PENALE_T_SENTENZA TSET ON ( TSET.ID_SENT = PTS.ID_SENT)
where anag.CODICE_FISCALE = '****************' and tSet.ID_ESTENSORE = magis.ID_MAGIS;
     */
    try {
      const countIsEstensore = await this.penaleCollegioEntityRepository
        .createQueryBuilder('coll')
        .distinct(true)
        .innerJoinAndMapMany(
          'coll.magistrati',
          PenaleTMagisEntity,
          'magis',
          'coll.idMagis = magis.idMagis',
        )
        .innerJoinAndMapMany(
          'coll.magistratiAnagrafica',
          PenaleAnagmagisEntity,
          'anag',
          'anag.idAnagmagis = magis.idAnagmagis',
        )
        .innerJoinAndMapMany(
          'coll.ricorsoUdienza',
          PenaleTRicorsoUdienzaEntity,
          'PTR',
          'coll.idUdienza = PTR.idUdienza',
        )
        .innerJoinAndMapMany(
          'coll.esito',
          PenaleTEsitoEntity,
          'PTE',
          'PTE.ID_RICUDIEN = PTR.ID_RICUDIEN',
        )
        .innerJoinAndMapMany(
          'coll.esitoSent',
          PenaleTEsitoSentEntity,
          'PTS',
          'PTE.ID_ESITO = PTS.ID_ESITO',
        )
        .innerJoinAndMapMany(
          'coll.sentenza',
          PenaleTSentenzaEntity,
          'TSET',
          'TSET.ID_SENT = PTS.ID_SENT',
        )
        //TODO inserire or estensore2
        .where(
          'anag.codiceFiscale = :cf and TSET.ID_ESTENSORE = magis.ID_MAGIS',
          {
            cf: codiceFiscale,
          },
        ) // or you can change condition to 'key.userId = :userId' because of you have `userId` in Key
        .getCount();
      return countIsEstensore > 0;
    } catch (e) {
      console.log(e);
    }
    return false;
  }

  async isPresidente(codiceFiscale: string): Promise<boolean> {
    /*   questo maggiore di 0 è un presidente
        --in quanti ricorsi sono presidente
        select count(*) from PENALE_COLLEGIO coll
        join PENALE_T_MAGIS magis on coll.ID_MAGIS = magis.ID_MAGIS
        join PENALE_ANAGMAGIS anag on magis.ID_ANAGMAGIS = anag.ID_ANAGMAGIS
        where anag.CODICE_FISCALE = '****************' and coll.TIPOMAG = 'PRE';*/
    if (codiceFiscale) {
      try {
        const countIsPresidente = await this.penaleCollegioEntityRepository
          .createQueryBuilder('coll')
          .innerJoinAndMapMany(
            'coll.magistrati',
            PenaleTMagisEntity,
            'magis',
            'coll.idMagis = magis.idMagis',
          )
          .innerJoinAndMapMany(
            'coll.magistratiAnagrafica',
            PenaleAnagmagisEntity,
            'anag',
            'anag.idAnagmagis = magis.idAnagmagis',
          )
          .where("anag.codiceFiscale = :cf and coll.tipoMag = 'PRE'", {
            cf: codiceFiscale,
          }) // or you can change condition to 'key.userId = :userId' because of you have `userId` in Key
          .getCount();
        return countIsPresidente > 0;
      } catch (e) {
        console.log(e);
      }
    }
    return false;
  }

  /**
   * Trova un soggetto per CF nell'anagrafica distrettuale
   * @param codiceFiscale cf del soggetto
   */
  async ricercaPersona(
    codiceFiscale: string,
  ): Promise<PenaleCollegioEntity | null> {
    /*  return await this.personaRepository.findOne({
              where: { codiceFiscale: codiceFiscale },
            });*/
    return null;
  }

  /**
   * Trova un GIUDICE per CF nell'anagrafica distrettuale
   * @param codiceFiscale cf del soggetto
   */
  async ricercaGiudice(
    codiceFiscale: string,
  ): Promise<PenaleCollegioEntity | null> {
    /*const res = await this.giudiceRepository.findOne({
              relations: ['persona'],
              where: { persona: { codiceFiscale: codiceFiscale } },
            });
            console.log('res', res);
            return res;*/

    return null;
  }

  async getIdUtente(codiceFiscale: string): Promise<number> {
    const newVar = await this.penaleTUtenteEntityRepository.findOne({
      where: { codiceFiscale: codiceFiscale },
      cache: {
        id: `getIdUtente-${codiceFiscale}`, // chiave cache univoca
        milliseconds: 3600000, // 60 secondi di cache
      },
    });

    return newVar ? newVar.idUtente : 0;
  }

  async getUtenteByCf(codiceFiscale: string) {
    const newVar = await this.penaleTUtenteEntityRepository.findOne({
      where: { codiceFiscale: codiceFiscale },
      cache: {
        id: `getUtenteByCf-${codiceFiscale}`, // chiave cache univoca
        milliseconds: 3600000, // 60 secondi di cache
      },
    });
    return newVar;
  }
  async getMagisByCf(codiceFiscale: string) {
    const newVar = await this.penalePenaleAnagmagisEntityRepository.findOne({
      where: { codiceFiscale: codiceFiscale },
      cache: {
        id: `getMagisByCf-${codiceFiscale}`, // chiave cache univoca
        milliseconds: 3600000, // 60 secondi di cache
      },
    });
    return newVar;
  }
}
