import { Module } from '@nestjs/common';
import { MultiTenantModule } from 'src/multi-tenant/multi-tenant.module';
import { ProvvedimentiController } from './provvedimenti.controller';
import { PlaceholderService } from './placeholder.service';
import { ProvvedimentiService } from './provvedimenti.service';
import { AuthModule } from '../auth/auth.module';
import { UfficiModule } from '../uffici/uffici.module';
import { ProvvEditorLavorazioneService } from './provvEditorLavorazione.service';
import { RicorsoUdienzaModule } from '../ricorso-udienza/ricorso-udienza.module';
import { VerbaleRicorsoModule } from '../verbale-ricorso/verbale-ricorso.module';
import { EsitiModule } from '../esiti/esiti.module';
import { VerbaleUdienzaModule } from '../verbale-udienza/verbale-udienza.module';
import { SentenzaModule } from '../sentenza/sentenzaModule';
import { ProvvedimentoPresidenteController } from './provvedimento-presidente.controller';
import { ProvvedimentiNoteModule } from '../provvedimenti-note/provvedimenti-note.module';
import { CodeDepositoModule } from '../code-deposito/code-deposito.module';
import { ProvvedimentoPresidenteService } from './provvedimento-presidente.service';
import { ProvvedimentoChangeStatusModule } from '../provvedimento-change-status/provvedimento-change-status.module';
import { FascicoloModule } from '../fascicolo/fascicolo.module';
import { NotificheModule } from '../notifiche/notifiche.module';
import { UdienzaService } from '../udienza/udienza.service';
import { ProvvPlaceholderTextareaService } from './provvPlaceholderTextarea.service';
import { CreateHtmlIndexService } from './create-html.index.service';
import { ProvvLavorazioneService } from './provvLavorazione.service';
import { ProvvedimentiEstensoreService } from './provvedimenti-estensore.service';
import { ProvvedimentoFindService } from './provvedimento-find.service';
import { UtenteModule } from '../utente/utente.module';
import { MagistratiModule } from '../magistrati/magistrati.module';
import { CollegioModule } from '../collegio/collegio.module';
import { CspbackendTimbripubbModule } from '../atti/cspbackend-timbripubb.module';
import { RicercaSentenzaModule } from '../ricerca-sentenza/ricerca-sentenza.module';
import { PenaleMotivazioniModule } from '../penale-motivazioni/penale-motivazioni.module';
import { PenalePqmModule } from 'src/penale-pqm/penale-pqm.module';
import { SpoglioService } from 'src/spoglio/spoglio.service';

@Module({
  imports: [
    AuthModule,
    UfficiModule,
    RicorsoUdienzaModule,
    VerbaleRicorsoModule,
    VerbaleUdienzaModule,
    ProvvedimentiNoteModule,
    FascicoloModule,
    ProvvedimentoChangeStatusModule,
    NotificheModule,
    CodeDepositoModule,
    SentenzaModule,
    EsitiModule,
    UtenteModule,
    MagistratiModule,
    CollegioModule,
    CspbackendTimbripubbModule,
    RicercaSentenzaModule,
    PenalePqmModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql', 'provvedimento', 'presidente'],
    }),
    PenaleMotivazioniModule,
  ],
  providers: [
    ProvvedimentiController,
    ProvvedimentiService,
    ProvvLavorazioneService,
    PlaceholderService,
    ProvvedimentoPresidenteController,
    ProvvEditorLavorazioneService,
    UdienzaService,
    ProvvedimentoPresidenteService,
    CreateHtmlIndexService,
    ProvvPlaceholderTextareaService,
    ProvvedimentiEstensoreService,
    ProvvedimentoFindService,
    SpoglioService
  ],
  controllers: [ProvvedimentiController, ProvvedimentoPresidenteController],
  exports: [
    ProvvedimentiService,
    ProvvLavorazioneService,
    ProvvedimentoPresidenteService,
    ProvvEditorLavorazioneService,
    CreateHtmlIndexService,
    ProvvPlaceholderTextareaService,
    ProvvedimentiEstensoreService,
    ProvvedimentoFindService,
    SpoglioService
  ],
})
export class ProvvedimentiModule {}
