import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { ProvvedimentiStatoEnum } from '../entities/enumaration/provvedimenti-stato.enum';

@ObjectType()
export class PenaleProvvChangeStatus {
  @Field(type => ID)
  idProvvedimentoChangeStatus: string;

  @Field(type => String)
  idProvvedimento: string;
  @Field(type => String)
  stato: ProvvedimentiStatoEnum;
  @Field(type => String)
  prevStato?: ProvvedimentiStatoEnum;
  @Field(type => Date)
  dateChange: Date;
  @Field(type => Int)
  idAutore: number;

  @Field(type => Boolean)
  isRevisione?: boolean = false;
}
