import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinTable,
  ManyToMany,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';

@Entity('PENALE_VERBALE_RICORSO') //nome tabella su schema oracle
export class PenaleVerbaleRicorsoEntity {
  @PrimaryColumn({ name: 'ID_VERBALE_NRG' })
  idVerbaleNrg: number;
  @Column({ name: 'ID_RICUDIEN' })
  idRicorsoUdienza: number;

  @Column({
    name: 'DESC_PARTI',
    type: 'clob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  descrizioniParti: string;
  @Column({ name: 'DESC_RELATORE' })
  descrizioneRelatore: string;

  @Column({
    name: 'DESC_DIFENSORI',
    type: 'clob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  descrizioniDifensori: string;
  @Column({
    name: 'TESTO_VERBALE',
    type: 'clob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  testoVerbale: string;
  @Column({ name: 'DATA_INSERIMENTO' })
  dataInserimento: Date;
  @Column({ name: 'DATA_AGGIORNAMENTO' })
  dataAggiornamento: Date;
  @Column({
    name: 'DESC_CONCLUSIONI_PG',
    type: 'clob',
    nullable: true,
    transformer: {
      to: (value: string) => (value ? Buffer.from(value) : null),
      from: (value: Buffer) => (value ? value.toString() : null),
    },
  })
  descrizioneCoclusioniPg: string;
}
