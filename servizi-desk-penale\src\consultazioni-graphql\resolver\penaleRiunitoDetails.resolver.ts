import { Logger, NotFoundException } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import { RiunitoDetails } from '../models/riunito_details.model';
import { RiunitiService } from '../../riuniti/riuniti.service';
import { Utils } from '../../utils/utils';

@Resolver(() => RiunitoDetails)
export class PenaleRiunitoDetailsResolver {
  private logger = new Logger(PenaleRiunitoDetailsResolver.name);
  constructor(private readonly riunitiService: RiunitiService) {}

  @Query(returns => [RiunitoDetails], { name: 'getRiuntiByIdRicUdien' })
  async getRiuntiByIdRicUdien(
    @Args('idRicUdien') idRicUdien: number,
  ): Promise<RiunitoDetails[] | null> {
    const code = await this.riunitiService.getRiuniti(idRicUdien);
    if (!code) {
      return null;
    }
    const riunitoDetails = code.map(
      ricUdien =>
        new RiunitoDetails({
          idRicorsoUdienza: ricUdien.idRicudien,
          nrg: ricUdien.nrg,
          numero: Utils.getNumeroFascicolo(ricUdien?.ricorso?.nrgReale + ''),
          anno: Utils.getAnnoFascicolo(ricUdien?.ricorso?.nrgReale + ''),
        }),
    );
    const riunitoDetails1 = riunitoDetails.sort((a, b) =>
      a.idRicorsoUdienza >= b.idRicorsoUdienza ? 1 : -1,
    );
    return riunitoDetails1.filter((ri, index) => {
      return !riunitoDetails1.some((r, i) => {
        return i > index && r.numero === ri.numero && r.anno === ri.anno;
      });
    });
  }
}
