SERVER_PORT=3001
LOG_LEVEL=info
GRAPHQL_DEBUG=true
GRAPHQL_ENABLE_PLAYGROUND=true
YAML_CONFIG_FILENAME=uffici.yaml
GLOBAL_PATH=api/v1
SWAGGER_PATH=/api/v1/desk-swagger

# Servizi depositi in locale
#URL_SERVIZI_DEPOSITI=http://localhost:8080/
# Servizi deposito Puntiamo all'ambiente di test quando non è disponibile in locale
URL_SERVIZI_DEPOSITI=http://dockerpa2.netserv.it:8085/


# Servizi depositi
# avviato localmente

ENABLE_MODHEADER=false
ENABLE_FIRMA_OTP=false

#IS_DEBUG=true attiva anche lo Iam Keycloak Interno
IS_DEBUG=false

jwksUri=https://auth03coll.giustizia.it/b2cmingiustiziaspidcoll.onmicrosoft.com/b2c_1a_signin_aad/discovery/v2.0/keys
#jwksUri da utilizzare quando attivo Iam Keycloak Interno
#jwksUri=http://dockerpa3.netserv.it:8091/realms/test/protocol/openid-connect/certs
LOGGER_LEVEL=['log', 'error', 'warn', 'debug', 'verbose']

# da attivare il pre/esercizio importando la CA dello Iam Azure nel bundle della macchina.
#NODE_EXTRA_CA_CERTS=/etc/pki/tls/certs/ca-bundle.crt


# parametro per query di scrivania per ruolo presidente
MESI_QUERY_SCRIVANIA=9