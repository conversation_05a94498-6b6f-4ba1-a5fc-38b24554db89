import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {
  ConnectionType,
  EdgeType,
} from '../../relay-pagination/connection-paging';
import { PenaleParam } from './penale_param.model';
import { ProvvedimentiTipoEnum } from '../entities/enumaration/provvedimenti-tipo.enum';

@ObjectType()
export class PenaleNotifiche {
  @Field(type => ID)
  idNotifica: string;
  @Field(type => String)
  descrizione?: string;
  @Field(type => Int)
  nrg: number;
  @Field(type => String)
  tipo?: string;
  @Field(type => Boolean)
  read?: boolean;
  @Field(type => Date)
  dataCreazione?: Date;

  @Field(type => Int)
  idUtente: number;
  @Field(type => Int)
  idUdienza: number;
  annoFascicolo?: number | null;
  numeroFascicolo?: number | null;
  dataUdienza?: Date;
  tipoUdienza?: PenaleParam;
  sezione?: PenaleParam;
  coleggio?: PenaleParam;
  inizioUdienza?: Date;
  fineUdienza?: Date;
  tipoProvvedimento?: ProvvedimentiTipoEnum;
  isEstensore?: boolean;
  isPrincipale?: boolean;

  public constructor(init?: Partial<PenaleNotifiche>) {
    Object.assign(this, init);
  }
}

@ObjectType()
class AggregatePenaleNotifiche {
  @Field(_type => Number)
  count: number;
  @Field(_type => Number)
  total: number;
  @Field(_type => Number)
  unread?: number;
  @Field(_type => Number)
  totalElement?: number;
}

@ObjectType()
export class PenaleNotificheEdge extends EdgeType(PenaleNotifiche) {}

@ObjectType()
export class PenaleNotificheConnection extends ConnectionType(
  PenaleNotifiche,
  PenaleNotificheEdge,
) {
  @Field(_type => AggregatePenaleNotifiche)
  aggregate: AggregatePenaleNotifiche;
}
