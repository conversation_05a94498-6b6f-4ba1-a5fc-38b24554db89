import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_COLLEGIO') //nome tabella su schema oracle
export class PenaleCollegioEntity {
  @PrimaryColumn({ name: 'ID_MAGISCOLLE' })
  idMagiscolle: number;
  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OPERATOR<PERSON>' })
  operatore: number;

  @Column({ name: 'OGGI' })
  oggi: Date;
  @Column({ name: 'GRADOMAG' })
  gradoMag: number;
  @Column({ name: 'TIPOMAG' })
  tipoMag: string;

  @Column({ name: 'ID_MAGIS' })
  idMagis: number;

  @Column({ name: 'ID_UDIEN' })
  idUdienza: number;

  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;
  @Column({ name: 'IN_UDIENZA' })
  inUdienza: number;
}
