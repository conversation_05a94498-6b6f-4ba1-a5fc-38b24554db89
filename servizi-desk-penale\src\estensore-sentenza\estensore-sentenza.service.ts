import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenaleTEstensoriSentenzaEntity } from '../consultazioni-graphql/entities/penale_t_estensori_sentenza.entity';

@UfficioService()
export class EstensoreSentenzaService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  async estensoriSentenza() {
    const promise = this.connection
      .getRepository(PenaleTEstensoriSentenzaEntity)
      .find({
        relations: {
          estensore: true,
        },
      });
    return promise;
  }
  async estensoreSentenza(id: number) {
    const promise = this.connection
      .getRepository(PenaleTEstensoriSentenzaEntity)
      .findOne({
        where: { id: id },
        relations: {
          estensore: true,
        },
      });
    return promise;
  }
  async getListEstensoriPerSentenza(idSent: number) {
    const promise = this.connection
      .getRepository(PenaleTEstensoriSentenzaEntity)
      .findBy({
        idSentenza: idSent,
      });
    return promise;
  }
  async getByIdAnagraficaPresidente(idAnagrafica: number) {
    // data la relazione OneToOne di estensore
    const promise = this.connection
      .getRepository(PenaleTEstensoriSentenzaEntity)
      .find({
        where: {
          estensore: {
            idAnagmagis: idAnagrafica,
            tipoMag: {
              sigla: 'PRE',
            },
          },
        },
        relations: {
          estensore: {
            tipoMag: true,
          },
        },
      });
    return promise;
  }
}
