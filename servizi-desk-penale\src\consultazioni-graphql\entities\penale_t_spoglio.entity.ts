import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinT<PERSON>,
  ManyToMany,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';

@Entity('PENALE_T_SPOGLIO') //nome tabella su schema oracle
export class PenaleTSpoglioEntity {
  @PrimaryColumn({ name: 'ID_SPOGLIO' })
  idSpoglio: number;

  @Column({ name: 'DATAPRESCRIZ' })
  dataPrescrizione: Date;
  @Column({ name: 'DATADECTERM' })
  dataDecterm: Date;
  @Column({ name: 'DATASOSP' })
  dataSosp: Date;
  @Column({ name: 'ID_MOTIVO' })
  idMotivo: number;

  @Column({ name: 'ART127' })
  art127: string;
  @Column({ name: 'ART444' })
  art444: string;
  @Column({ name: 'ART438' })
  art438: string;
  @Column({ name: 'ART611' })
  art611: string;
  @Column({
    name: 'VALPOND',
    type: 'decimal',
    precision: 4,
    scale: 2,
    default: 0,
  })
  valPond: number;
  @Column({ name: 'MODELLO' })
  modello: number;
  @Column({ name: 'ID_TIPOUDPREV' })
  idTipoudPrev: number;
  @Column({ name: 'NRG' })
  nrg: number;

  @Column({ name: 'PRESIDI' })
  presidi: string;

  @Column({ name: 'ID_SPOGLIATORE' })
  idSpogliatore: number;

  @Column({ name: 'ID_FUNZIONE' })
  idFunzione: number;

  @Column({ name: 'OGGI' })
  oggi: Date;

  @Column({ name: 'OPERATORE' })
  operatore: number;

  @Column({ name: 'COMMENTO' })
  commento: string;

  @Column({ name: 'PGCUSTODIA' })
  pgCustodia: number;
  @Column({ name: 'PGSUPER' })
  pgSuper: number;

  @Column({ name: 'VECCHIORITO' })
  vecchiRito: string;

  @Column({ name: 'CODICECONF' })
  codiceConf: number;

  @Column({ name: 'COGNOME_PMCE' })
  cognomePmce: string;
  @Column({ name: 'NOME_PMCE' })
  nomePmce: string;

  @Column({ name: 'ID_AULO_PMCE' })
  idAuloPmce: number;

  @Column({ name: 'COGNOME_PMCC' })
  cognomePmcc: string;
  @Column({ name: 'NOME_PMCC' })
  nomePmcc: string;

  @Column({ name: 'ID_AULO_PMCC' })
  idAuloPmcc: number;

  @Column({ name: 'NOTE' })
  note: string;

  @Column({ name: 'DATAPRESCRIZ2' })
  dataPrescriz2: Date;
  @Column({ name: 'DATAPASSAPG' })
  dataPassaPg: Date;

  @Column({ name: 'URGENTEPG' })
  urgentePg: string;

  @Column({ name: 'GLB_DTIME' })
  glbDtime: number;

  @Column({ name: 'ART12' })
  art12: string;
  @Column({ name: 'MAE' })
  mae: string;
  @Column({ name: 'PRIVACY' })
  privacy: number;
  @Column({ name: 'DATA_DECRETO' })
  dataDecreto: Date;
  @Column({ name: 'DEPLANO' })
  deplano: string;
}
