import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { ScrivaniaService } from './scrivania.service';
import { ElencoProvvedimentiService } from './elenco-provvedimenti.service';
import { ScrivaniaController } from './scrivania.controller';
import { AuthModule } from 'src/auth/auth.module';
import { RicercaSentenzaModule } from '../ricerca-sentenza/ricerca-sentenza.module';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { OscuramentoService } from './oscuramento.service';
import { EsitiModule } from '../esiti/esiti.module';
import { RicorsoUdienzaModule } from '../ricorso-udienza/ricorso-udienza.module';
import { ProvvedimentiModule } from '../provvedimenti/provvedimenti.module';
import { ProvvedimentoChangeStatusModule } from '../provvedimento-change-status/provvedimento-change-status.module';

@Module({
  imports: [
    AuthModule,
    RicercaSentenzaModule,
    EsitiModule,
    RicorsoUdienzaModule,
    ProvvedimentiModule,
    ProvvedimentoChangeStatusModule,
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [
    ScrivaniaService,
    ElencoProvvedimentiService,
    ProvvedimentoChangeStatusService,
    OscuramentoService,
  ],
  controllers: [ScrivaniaController],
  exports: [
    ScrivaniaService,
    ElencoProvvedimentiService,
    ProvvedimentoChangeStatusService,
    OscuramentoService,
  ],
})
export class ScrivaniaModule {}
