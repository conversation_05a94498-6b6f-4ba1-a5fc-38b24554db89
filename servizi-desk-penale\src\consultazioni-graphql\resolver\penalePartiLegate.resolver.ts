import { NotFoundException } from '@nestjs/common';
import { <PERSON>rgs, <PERSON><PERSON>, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { AnagraficaPartiService } from '../../anagrafica-parti/anagrafica-parti.service';
import { PenaleAnagraficaParti } from '../models/penale_anagparti.model';
import { PenaleParteLegate } from '../models/penale_parte_legate.model';
import { PartiLegateService } from '../../parti-legate/parti-legate.service';

@Resolver(() => PenaleParteLegate)
export class PenalePartiLegateResolver {
  constructor(
    private readonly anagraficaPartiService: AnagraficaPartiService,
    private readonly partiLegateService: PartiLegateService,
  ) {}

  @ResolveField('anagraficaParte', () => PenaleAnagraficaParti, {
    nullable: true,
  })
  async getAnagraficaParti(
    @Parent() penaleTParti: PenaleParteLegate,
  ): Promise<PenaleAnagraficaParti | null> {
    if (penaleTParti.idAnagraficaParte && penaleTParti.idAnagraficaParte > 0) {
      return await this.anagraficaPartiService.anagraficaPartiByIdAnagraficaParte(
        penaleTParti.idAnagraficaParte,
      );
    }
    return null;
  }

  @Query(() => PenaleParteLegate, { name: 'parteLegate' })
  async parte(@Args('id') id: number): Promise<PenaleParteLegate> {
    const parti = await this.partiLegateService.parteLegata(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleParteLegate], { name: 'partiLegate' })
  parti(): Promise<PenaleParteLegate[]> {
    return this.partiLegateService.partiLegati();
  }
}
