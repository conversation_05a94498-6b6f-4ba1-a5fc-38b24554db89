import { Field, InputType, Int } from '@nestjs/graphql';
import { ProvvedimentiStatoEnum } from '../enumaration/provvedimenti-stato.enum';
import { ApiProperty } from '@nestjs/swagger';

@InputType()
export class CreateProvvedimentiChangeStatusInput {
  @ApiProperty()
  @Field(() => ProvvedimentiStatoEnum)
  stato: ProvvedimentiStatoEnum;
  @ApiProperty()
  @Field(() => ProvvedimentiStatoEnum, {
    nullable: true,
  })
  prevStato?: ProvvedimentiStatoEnum;
  @ApiProperty()
  @Field(() => Int)
  idAutore: number;
  @ApiProperty()
  @Field(() => String)
  idProvvedimento: string | null;
}
