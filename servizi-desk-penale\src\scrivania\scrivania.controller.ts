import { Controller, Get, Logger, Param, Post, Query } from '@nestjs/common';
import { ScrivaniaDto } from './dto/ScrivaniaDto';
import { ScrivaniaProvvedimentiModel } from '../consultazioni-graphql/models/scrivania_provvedimenti.model';
import { ElencoProvvedimentiService } from './elenco-provvedimenti.service';
import { ScrivaniaService } from './scrivania.service';
import { AuthService } from 'src/auth/auth.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  PaginationCustomQueryFilter,
  PenaleRicorsoUdienzaConnection,
  ProvvedimentiDtoConnection,
} from '../consultazioni-graphql/models/dto/generic_paginator';
import {
  computePageInfo,
  endCursor,
  startCursor,
} from '../relay-pagination/pagination-utils';
import { Utils } from '../utils/utils';
import { Args } from '@nestjs/graphql';
import { ProvvedimentiService } from '../provvedimenti/provvedimenti.service';
import { RicorsoUdienzaCommonService } from '../ricorso-udienza/ricorso-udienza-common.service';

@ApiTags('datiScrivania')
@ApiBearerAuth('access-token')
@Controller('datiScrivania')
export class ScrivaniaController {
  private logger = new Logger(ScrivaniaController.name);
  constructor(
    private readonly elencoProvvedimentiService: ElencoProvvedimentiService,
    private readonly scrivaniaService: ScrivaniaService,
    private readonly authService: AuthService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly ricorsoUdienzaCommonService: RicorsoUdienzaCommonService,
  ) {}

  @Get('/dashboard')
  async getDashboard(): Promise<ScrivaniaDto[] | null> {
    this.logger.log(`Richiesta dashboard per il presidente`);
    const cf = await this.authService.getCurrentUser();
    const data = await this.scrivaniaService.getUdienze(cf);
    return data;
  }

  @Get('/byFilter')
  async byFilter(
    @Query('dataUdienza') dataUdienza: string,
    @Query('sezione') sezione: string,
    @Query('tipoUdienza') tipoUdienza: string,
    @Query('collegio') collegio: string,
  ): Promise<ScrivaniaDto[] | null> {
    this.logger.log(
      `Richiesta di  filtro per la scrivania del presidente. dataUdienza:${dataUdienza}, sezione: ${sezione}, tipoUdienza: ${tipoUdienza}, collegio: ${collegio}`,
    );
    const cf = await this.authService.getCurrentUser();
    return await this.scrivaniaService.getUdienze(
      cf,
      dataUdienza,
      sezione,
      tipoUdienza,
      collegio,
    );
  }
}
