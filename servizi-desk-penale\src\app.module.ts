import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigurationModule } from './configuration/configuration.module';
import { ConfigurationController } from './configuration/configuration.controller';
import { APP_FILTER, REQUEST } from '@nestjs/core';
import { HttpExceptionFilter } from './exceptions/http-exception.filter';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { format, transports } from 'winston';
import {
  utilities as nestWinstonModuleUtilities,
  WinstonModule,
} from 'nest-winston';
import { HEADER_TRACE_ID } from './constants';
import { ConsultazioniGraphqlModule } from './consultazioni-graphql/consultazioni-graphql.module';
import { AuthModule } from './auth/auth.module';
import { AuthController } from './auth/auth.controller';

@Module({
  imports: [
    ConfigurationModule,
    AuthModule,
    ConsultazioniGraphqlModule,
    WinstonModule.forRootAsync({
      useFactory: (conf: ConfigService, req: Request) => ({
        level: conf.get('app.logLevel'),
        defaultMeta: {
          get traceId() {
            return (req.headers as any)[HEADER_TRACE_ID] as string;
          },
        },
        transports: [
          new transports.Console({
            format: format.combine(
              format.timestamp(),
              format.ms(),
              nestWinstonModuleUtilities.format.nestLike(
                'casspenale-consultazioni',
                {
                  prettyPrint: true,
                },
              ),
            ),
          }),
        ],
      }),
      imports: [ConfigModule],
      inject: [ConfigService, REQUEST],
    }),
  ],
  controllers: [ConfigurationController, AuthController],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    Logger,
  ],
})
export class AppModule {}
