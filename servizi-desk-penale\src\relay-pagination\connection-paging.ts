import { ArgsType, Field, Int, ObjectType } from '@nestjs/graphql';
import { Type } from '@nestjs/common';
import * as Relay from 'graphql-relay';
import {
  Min,
  Validate,
  ValidateIf,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { FindManyOptions, Repository, SelectQueryBuilder } from 'typeorm';
import { ObjectLiteral } from 'typeorm/common/ObjectLiteral';

@ObjectType()
export class PageInfo implements Relay.PageInfo {
  @Field(_type => Boolean, { nullable: true })
  hasNextPage: boolean;
  @Field(_type => Boolean, { nullable: true })
  hasPreviousPage: boolean;
  @Field(_type => String, { nullable: true })
  startCursor: Relay.ConnectionCursor | null;
  @Field(_type => String, { nullable: true })
  endCursor: Relay.ConnectionCursor | null;
}

@ValidatorConstraint({ async: false })
class CannotUseWithout implements ValidatorConstraintInterface {
  validate(_value: any, args: ValidationArguments) {
    const object = args.object as any;
    const required = args.constraints[0] as string;
    return object[required] !== undefined;
  }

  defaultMessage(args: ValidationArguments) {
    return `Cannot be used without \`${args.constraints[0]}\`.`;
  }
}

@ValidatorConstraint({ async: false })
class CannotUseWith implements ValidatorConstraintInterface {
  validate(_value: any, args: ValidationArguments) {
    const object = args.object as any;
    return args.constraints.every((propertyName: any) => {
      return object[propertyName] === undefined;
    });
  }

  defaultMessage(args: ValidationArguments) {
    return `Cannot be used with \`${args.constraints.join('` , `')}\`.`;
  }
}

@ArgsType()
export class ConnectionArgs implements Relay.ConnectionArguments {
  @Field(_type => String, {
    nullable: true,
    description: 'Paginate before opaque cursor',
  })
  @ValidateIf((o: any) => o.before !== undefined)
  @Validate(CannotUseWithout, ['last'])
  @Validate(CannotUseWith, ['after', 'first'])
  before?: Relay.ConnectionCursor;

  @Field(_type => String, {
    nullable: true,
    description: 'Paginate after opaque cursor',
  })
  @ValidateIf((o: any) => o.after !== undefined)
  @Validate(CannotUseWithout, ['first'])
  @Validate(CannotUseWith, ['before', 'last'])
  after?: Relay.ConnectionCursor;

  @Field(_type => Int, { nullable: true, description: 'Paginate first' })
  @ValidateIf((o: any) => o.first !== undefined)
  @Min(1)
  @Validate(CannotUseWith, ['before', 'last'])
  first?: number;

  @Field(_type => Int, { nullable: true, description: 'Paginate last' })
  @ValidateIf((o: any) => o.last !== undefined)
  // Required `before`. This is a weird corner case.
  // We'd have to invert the ordering of query to get the last few items then re-invert it when emitting the results.
  // We'll just ignore it for now.
  @Validate(CannotUseWithout, ['before'])
  @Validate(CannotUseWith, ['after', 'first'])
  @Min(1)
  last?: number;
}

export interface IEdgeType<T> {
  node: T;
  cursor: Relay.ConnectionCursor;
}

export function EdgeType<T>(classRef: Type<T>): Type<IEdgeType<T>> {
  @ObjectType({ isAbstract: true })
  abstract class Edge implements Relay.Edge<T> {
    @Field(() => classRef, { description: 'The target node' })
    node: T;

    @Field(_type => String, {
      description: 'Used in `before` and `after` args',
    })
    cursor: Relay.ConnectionCursor;
  }

  return Edge as Type<IEdgeType<T>>;
}

export interface IConnectionType<T> {
  pageInfo: PageInfo;
  edges: Array<Relay.Edge<T>>;
}

export function ConnectionType<T>(
  _classRef: Type<T>,
  Edge: any,
): Type<IConnectionType<T>> {
  @ObjectType({ isAbstract: true })
  abstract class Connection implements Relay.Connection<T> {
    @Field()
    pageInfo: PageInfo;

    @Field(() => [Edge])
    edges: Array<Relay.Edge<T>>;
  }

  return Connection as Type<IConnectionType<T>>;
}

type PagingMeta =
  | { pagingType: 'forward'; after?: string; first: number }
  | { pagingType: 'backward'; before?: string; last: number }
  | { pagingType: 'none' };

function getMeta(args: ConnectionArgs): PagingMeta {
  const { first = 0, last = 0, after, before } = args;
  const isForwardPaging = !!first || !!after;
  const isBackwardPaging = !!last || !!before;

  if (isForwardPaging) {
    return { pagingType: 'forward', after, first };
  } else if (isBackwardPaging) {
    return { pagingType: 'backward', before, last };
  } else {
    return { pagingType: 'none' };
  }
}

/**
 * Create a 'paging parameters' object with 'limit' and 'offset' fields based on the incoming
 * cursor-paging arguments.
 */
export function getPagingParameters(args: ConnectionArgs) {
  const meta = getMeta(args);

  switch (meta.pagingType) {
    case 'forward': {
      return {
        limit: meta.first,
        offset: meta.after ? Relay.cursorToOffset(meta.after) + 1 : 0,
      };
    }
    case 'backward': {
      const { last, before } = meta;
      let limit = last;
      let offset = Relay.cursorToOffset(before || '') - last;

      // Check to see if our before-page is underflowing past the 0th item
      if (offset < 0) {
        // Adjust the limit with the underflow value
        limit = Math.max(last + offset, 0);
        offset = 0;
      }

      return { offset, limit };
    }
    default:
      return {};
  }
}

export async function findAndPaginate<T extends ObjectLiteral>(
  condition: FindManyOptions<T>,
  connArgs: ConnectionArgs,
  repository: Repository<T>,
) {
  const { limit, offset } = getPagingParameters(connArgs);
  const [entities, count] = await repository.findAndCount({
    ...condition,
    skip: offset,
    take: limit,
  });

  return Relay.connectionFromArraySlice(entities, connArgs, {
    arrayLength: count,
    sliceStart: offset || 0,
  });
}

export async function getManyAndPaginate<T extends ObjectLiteral>(
  queryBuilder: SelectQueryBuilder<T>,
  connArgs: ConnectionArgs,
) {
  const { limit, offset } = getPagingParameters(connArgs);
  const [entities, count] = await queryBuilder
    .offset(offset)
    .limit(limit)
    .getManyAndCount();

  return Relay.connectionFromArraySlice(entities, connArgs, {
    arrayLength: count,
    sliceStart: offset || 0,
  });
}

export {
  connectionFromArray,
  connectionFromPromisedArray,
  connectionFromArraySlice,
  connectionFromPromisedArraySlice,
} from 'graphql-relay';
