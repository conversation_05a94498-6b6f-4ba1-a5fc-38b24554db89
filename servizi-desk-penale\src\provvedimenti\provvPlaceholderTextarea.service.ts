import { Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth/auth.service';
import { UfficiDBService } from '../uffici/ufficiDB.service';
import { ProvvedimentiService } from './provvedimenti.service';
import { PlaceholderService } from './placeholder.service';
import { ProvvEditorLavorazioneService } from './provvEditorLavorazione.service';
import { ContextValueDto } from './entities/dto/provv-placeholder-dto';

@UfficioService()
export class ProvvPlaceholderTextareaService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  async getPlaceholder(nrg: number): Promise<ContextValueDto[]> {
    const contextArray = new Array<ContextValueDto>();
    const elencoPartiNrg: Array<any> = await this.connection.query(
      "SELECT NRG, TIPOFIG, DESCRIZIONE, COGNOME, NOME, TIPOTAB, TO_CHAR(DATANASC,'dd/mm/yyyy') AS DATANASC" +
        '  FROM PENALE_PARTI, PENALE_ANAGPARTI, PENALE_PARAM' +
        " WHERE (PENALE_PARTI.NRG = :nrg) AND (RICORRENTE = 1) AND SECRETATA='0' AND PENALE_PARTI.ID_ANAGPARTE = PENALE_ANAGPARTI.ID_ANAGPARTE" +
        ' AND PENALE_PARTI.ID_PARAMFIG= PENALE_PARAM.ID_PARAM ORDER BY NUMORD, PENALE_PARTI.ID_PARTE',
      [nrg],
    );
    /*    if (parti?.length > 0) {
      result = {
        nrg: placeholder[0].NRG,
        tipoFig: placeholder[0].TIPOFIG,
        descrizione: placeholder[0].DESCRIZIONE,
        cognome: placeholder[0].COGNOME,
        nome: placeholder[0].NOME,
        dataNascita: placeholder[0].DATANASC,
      };*/
    /*      contextArray.push(
        ...[
          {
            key: 'NRG',
            value: placeholder[0].NRG,
          },
          {
            key: 'TIPOFIG',
            value: placeholder[0].TIPOFIG,
          },
          {
            key: 'DESCRIZIONE',
            value: placeholder[0].DESCRIZIONE,
          },
          {
            key: 'COGNOME',
            value: placeholder[0].COGNOME,
          },
          {
            key: 'NOME',
            value: placeholder[0].NOME,
          },
          {
            key: 'DATANASC',
            value: placeholder[0].DATANASC,
          },
          {
            key: 'TIPOTAB',
            value: placeholder[0].TIPOTAB,
          },
        ],
      );
    }*/
    const placeholder: Array<any> = await this.connection.query(
      'select p.ID_PROVV,  p.DATAPROVV, p.ID_TIPOPROVV, p.NRG, p.IMPUGNATO, p.GRADOPROVV, p.ID_AULO,' +
        '  rr.ART, rr.FONTENORM, rr.ANNO, rr.CAPO, rr.LETTERA, rr.LIBRO, rr.COMMA, rr.NUMLEG,' +
        ' (select y.localita from penale_aulo y where p.id_aulo=y.ID_AULO) LOCALITA,' +
        ' (select x.id_autorita from penale_t_aulo x  where x.id_aulo = p.id_aulo) ID_AUTORITA,' +
        '  i.descrizione as DESC_AUTORITA' +
        ' from penale_t_provved p, penale_t_ricorso r,  penale_Reatiricorso re, penale_t_reati rr, penale_t_aulo aa, penale_param i' +
        " where p.nrg = r.nrg and p.nrg = re.nrg(+) and p.id_aulo = aa.ID_AULO and i.tipotab= 'AUTORITA' and i.id_param = aa.id_autorita " +
        ' and re.id_reato = rr.id_reato(+) and r.nrg = :nrg ',
      [nrg],
    );
    if (placeholder?.length > 0) {
      const appello =
        (placeholder[0].ART > 0 ? "l' art. " + placeholder[0].ART : '') +
        (placeholder[0].FONTENORM ? ' ' + placeholder[0].FONTENORM : '') +
        (placeholder[0].NUMLEG ? '  n.' + placeholder[0].NUMLEG : '') +
        (placeholder[0].ANNO ? '  del  ' + placeholder[0].ANNO : '') +
        (placeholder[0].LETTERA ? ' let.' + placeholder[0].LETTERA : '') +
        (placeholder[0].COMMA ? ' com. ' + placeholder[0].COMMA : '');

      if (elencoPartiNrg.length > 0) {
        const ric =
          elencoPartiNrg[0].COGNOME ?? '' + ' ' + elencoPartiNrg[0].NOME ?? '';
        let listaRic = '';
        let listaX = '';
        for (const element of elencoPartiNrg) {
          const ele = element.COGNOME ?? '' + ' ' + element.NOME ?? '';
          listaRic = listaRic + ele + ', ';
          listaX =
            listaX +
            ele +
            " deduce: violazione di legge e vizio di motivazione con riferimento alla ritenuta responsabilità dell'imputato.\n" +
            'Il motivo è inammissibile ..............\n';
        }
        contextArray.push(
          ...[
            {
              key: 'PARTEINTERESSATA',
              value: ric,
            },
            {
              key: 'LISTAPARTEINT',
              value: listaRic,
            },
            {
              key: 'PARTEX',
              value: listaX,
            },
          ],
        );
      }
      contextArray.push(
        ...[
          {
            key: 'AUTORITAAPPELLO',
            value: placeholder[0].DESC_AUTORITA,
          },
          {
            key: 'CITTAAPPELLO',
            value: placeholder[0].LOCALITA,
          },
          {
            key: 'AUTORITAPRONUNCIA',
            value: placeholder[0].DESC_AUTORITA,
          },
          {
            key: 'CITTAPRONUNCIA',
            value: placeholder[0].LOCALITA,
          },
          {
            key: 'DATASENTENZA',
            value: placeholder[0].DATAPROVV,
          },
          {
            key: 'DATAPRONUNCIA',
            value: placeholder[0].DATAPROVV,
          },
          {
            key: 'ARTICOLO',
            value: appello,
          },
        ],
      );
    }
    return contextArray;
  }
}
