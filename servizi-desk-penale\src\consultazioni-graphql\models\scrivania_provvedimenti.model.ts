import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {ProvvedimentiStatoEnum} from "../entities/enumaration/provvedimenti-stato.enum";
import {InfoProvvedimento} from "./dto/info_provvedimento.model";

@ObjectType()
export class ScrivaniaProvvedimentiModel {

  @Field(() => Date)
  dataDeposito?: Date;

  @Field(() => Int)
  numOrdine?: number;

  @Field(() => Int)
  nrg?: number;

  @Field(() => Int)
  idRicUdien: number;

  @Field(() => String, { name: 'idProvvedimento' })
  idProvv?: string;

  @Field(() => String, { name: 'tipologia' })
  tipo?: string;

  @Field(() => ProvvedimentiStatoEnum)
  stato?: ProvvedimentiStatoEnum;


  @Field(() => Boolean)
  modificaPresidente?: boolean;

  nrgFormat?: string;
  oscuratoSIC?: string;
  oscuratoDESKCSP?: string;

  relatore?: string;
  riunito?: InfoProvvedimento;

  public constructor(init?: Partial<ScrivaniaProvvedimentiModel>) {
    Object.assign(this, init);
  }
}
