import { Resolver, Query } from '@nestjs/graphql';
import { Version } from './models/version.model';
import { VersionService } from './version.service';

@Resolver(() => Version)
export class VersionResolver {
  constructor(private readonly versionService: VersionService) {}

  @Query(() => Version, { name: 'appVersions' })
  async getAppVersions(): Promise<Version> {
    return await this.versionService.getVersions();
  }
}
