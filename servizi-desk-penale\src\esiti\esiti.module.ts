import { Module } from '@nestjs/common';
import { MultiTenantModule } from '../multi-tenant/multi-tenant.module';
import { EsitiRuoloService } from './esiti-ruolo.service';
import { SentenzaRuoloService } from './sentenza-ruolo.service';
import { EsitiService } from './esiti.service';
import { EsitiSentService } from './esiti-sent.service';

@Module({
  imports: [
    MultiTenantModule.forRoutes({
      routes: ['graphql'],
    }),
  ],
  providers: [
    EsitiRuoloService,
    SentenzaRuoloService,
    EsitiService,
    EsitiSentService,
  ],
  exports: [
    EsitiRuoloService,
    SentenzaRuoloService,
    EsitiService,
    EsitiSentService,
  ],
})
export class EsitiModule {}
