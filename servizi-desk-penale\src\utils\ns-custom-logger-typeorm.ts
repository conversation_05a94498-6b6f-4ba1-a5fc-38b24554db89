import {
  AdvancedConsoleLogger,
  LogLevel,
  LogMessage,
  Logger,
  LoggerOptions,
} from 'typeorm';
import { QueryRunner } from 'typeorm/query-runner/QueryRunner';
import { Logger as LoggerNest } from '@nestjs/common';
import { forEach } from 'jszip';
export class NsCustomLoggerTypeorm
  extends AdvancedConsoleLogger
  implements Logger
{
  private logger = new LoggerNest(NsCustomLoggerTypeorm.name);
  constructor(options?: LoggerOptions) {
    super(options);
  }
  logQueryCache(query: string, queryRunner?: QueryRunner): void {
    this.logger.log(`[CACHE HIT] ${query}`);
  }
  logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    const neewLoggerParam = new Array<any>();
    if (parameters && parameters?.length > 0) {
      for (let i = 0; i < parameters.length; i++) {
        if (parameters[i] && parameters[i] instanceof Buffer) {
          this.logger.log('is buffer');
          neewLoggerParam.push('[Blob]');
        } else if (parameters[i] && parameters[i].length > 120) {
          this.logger.log('is string to much long');
          neewLoggerParam.push(parameters[i].substring(0, 100) + '...');
        } else {
          neewLoggerParam.push(parameters[i]);
        }
      }
    }
    super.logQuery(query, neewLoggerParam, queryRunner);
  }
  /**
   * Write log to specific output.
   */
  protected writeLog(
    level: LogLevel,
    logMessage: LogMessage | string | number | (LogMessage | string | number)[],
    queryRunner?: QueryRunner,
  ): void {
    const messages = this.prepareLogMessages(logMessage, {
      highlightSql: false,
    });

    for (const message of messages) {
      switch (message.type ?? level) {
        case 'log':
        case 'schema-build':
        case 'migration':
          this.logger.log(message.message);
          break;

        case 'info':
        case 'query':
          if (message.prefix) {
            this.logger.log(`${message.prefix} ${message.message}`);
          } else {
            this.logger.log(message.message);
          }
          break;

        case 'warn':
        case 'query-slow':
          if (message.prefix) {
            this.logger.warn(`${message.prefix} ${message.message}`);
          } else {
            this.logger.warn(message.message);
          }
          break;

        case 'error':
        case 'query-error':
          const stack = new Error().stack;
          if (message.prefix) {
            this.logger.error(
              `${message.prefix} ${message.message} \n ${stack}`,
            );
          } else {
            this.logger.error(`${message.message} \n ${stack}`);
          }
          break;
      }
    }
  }
}
