import { NotFoundException } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import { PenaleTParti } from '../models/penale_t_parti.model';
import { PenaleTSpoglio } from '../models/penale_t_spoglio.model';
import { SpoglioService } from '../../spoglio/spoglio.service';

@Resolver(() => PenaleTParti)
export class PenaleTSpoglioResolver {
  constructor(private readonly spoglioService: SpoglioService) {}

  @Query(() => PenaleTSpoglio, { name: 'spoglio' })
  async spoglio(@Args('id') id: number): Promise<PenaleTSpoglio> {
    const parti = await this.spoglioService.spoglio(id);
    if (!parti) {
      throw new NotFoundException(id);
    }
    return parti;
  }

  @Query(returns => [PenaleTSpoglio], { name: 'spogli' })
  spogli(): Promise<PenaleTSpoglio[]> {
    return this.spoglioService.spogli();
  }
}
