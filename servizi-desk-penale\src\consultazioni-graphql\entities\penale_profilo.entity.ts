import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('PENALE_PROFILI') //nome tabella su schema oracle
export class PenaleProfiloEntity {
  @PrimaryColumn({ name: 'ID_PROFILO' })
  idProfilo: number;

  @Column({ name: 'DESC_PROFILO' })
  descrizioneProfilo: string;
  @Column({ name: 'IN_USO_A' })
  inUsoA: string;

  @Column({ name: 'OPERATORE' })
  operatore: number;
  @Column({ name: 'DATAINIZ<PERSON>' })
  dataInizio: Date;
  @Column({ name: 'DATAFINE' })
  dataFine: Date;
}
