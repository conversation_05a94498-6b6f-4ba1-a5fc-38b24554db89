import { NotFoundException } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { Settings } from '../models/settings.model';
import { SettingsInput } from '../entities/dto/settings.input';
import { SettingsEntity } from '../entities/settings.entity';
import { ImpostazioniService } from '../../impostazioni/impostazioni.service';
import { AuthService } from '../../auth/auth.service';

@Resolver(() => Settings)
export class ImpostazioniResolver {
  constructor(
    private readonly impostazioniService: ImpostazioniService,
    private readonly authService: AuthService,
  ) {}

  @Query(() => Settings, { name: 'impostazioniByCf' })
  async impostazioniByCf(): Promise<Settings | null> {
    const cf = await this.authService.getCurrentUser();
    const res = await this.impostazioniService.impostazioniByCf(cf);

    if (res != null) {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const Cryptr = require('cryptr');
      const cryptr = new Cryptr('secretKey', {
        encoding: 'base64',
        pbkdf2Iterations: 10000,
        saltLength: 10,
      });
      const decryptedString = cryptr.decrypt(res?.passwordFirma);

      const settingsRes = new SettingsEntity();
      settingsRes.cfUtente = res?.cfUtente ? res?.cfUtente : '';
      settingsRes.usernameFirma = res?.usernameFirma ? res?.usernameFirma : '';
      settingsRes.passwordFirma = decryptedString ?? '';

      return settingsRes;
    } else {
      return new Settings();
    }
  }

  @Mutation(() => Settings, { name: 'creaSettings' })
  async createSetting(@Args('settings') settingsInput: SettingsInput) {
    const cf = await this.authService.getCurrentUser();
    settingsInput.cfUtente = cf;

    const cfUtente = await this.impostazioniService.createSetting(
      settingsInput,
    );

    if (cfUtente) {
      return await this.impostazioniService.impostazioniByCf(cfUtente);
    }
    throw new NotFoundException('Impostazioni non inserite');
  }

  @Mutation(() => Settings, { name: 'updateSetting' })
  async updateSetting(@Args('settings') settingsInput: SettingsInput) {
    const cf = await this.authService.getCurrentUser();
    settingsInput.cfUtente = cf;

    const cfUtente = await this.impostazioniService.updateSetting(
      settingsInput,
    );

    if (cfUtente) {
      return await this.impostazioniService.impostazioniByCf(cfUtente);
    }
    throw new NotFoundException('impostazioni non modificate');
  }

  @Mutation(() => Boolean, { name: 'deleteSetting' })
  async deleteSetting() {
    const cf = await this.authService.getCurrentUser();
    const result = await this.impostazioniService.deleteSetting(cf);

    return result;
  }
}
