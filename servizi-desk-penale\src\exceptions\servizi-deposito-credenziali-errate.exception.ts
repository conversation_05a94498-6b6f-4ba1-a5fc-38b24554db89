import { HttpExceptionOptions } from '@nestjs/common/exceptions/http.exception';
import { NsBaseException } from './ns-base.exception';
import {
  CodeErrorEnumException,
  NSTypeErrorEnum,
} from './code-error-enum.exception';

export class ServiziDepositoCredenzialiErrateException extends NsBaseException {
  constructor(
    response: string | Record<string, any>,
    options?: HttpExceptionOptions,
    error?: CodeErrorEnumException,
  ) {
    super(
      response,
      error ?? CodeErrorEnumException.CREDENZIALI_ERRATE,
      NSTypeErrorEnum.ERROR,
      options,
    );
  }
}
