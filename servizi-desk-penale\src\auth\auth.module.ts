import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UtentiQueryAuthModule } from '../utenti-auth/utenti-query-auth.module';
import { Constants } from './constants';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { AuthGuard } from './guard/auth.guard';

@Module({
  imports: [
    UtentiQueryAuthModule,
    JwtModule.register({
      global: true,
      secret: Constants.JWT_SECRET,
      signOptions: { expiresIn: Constants.JWT_EXPIRED },
    }),
  ],
  providers: [
    AuthService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
  exports: [AuthService],
})
export class AuthModule {}
