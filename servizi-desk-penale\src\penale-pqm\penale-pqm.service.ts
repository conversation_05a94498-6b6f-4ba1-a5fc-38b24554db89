import { Inject, InternalServerErrorException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { PenalePqmEntity } from 'src/consultazioni-graphql/entities/penale_pqm.entity';

@UfficioService()
export class PenalePqmService {
  constructor(@Inject(UFFICIO_CONNECTION) private connection: DataSource) {}

  penalePqm(): Promise<PenalePqmEntity[] | null> {
    return this.connection.getRepository(PenalePqmEntity).find({});
  }

  penalePqmByIdPqm(idPqm: number): Promise<PenalePqmEntity | null> {
    return this.connection.getRepository(PenalePqmEntity).findOne({
      where: { idPqm: idPqm },
    });
  }
}
